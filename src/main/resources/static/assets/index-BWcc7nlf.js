function Kv(i,r){for(var s=0;s<r.length;s++){const l=r[s];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in i)){const f=Object.getOwnPropertyDescriptor(l,u);f&&Object.defineProperty(i,u,f.get?f:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const f of u)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&l(d)}).observe(document,{childList:!0,subtree:!0});function s(u){const f={};return u.integrity&&(f.integrity=u.integrity),u.referrerPolicy&&(f.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?f.credentials="include":u.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function l(u){if(u.ep)return;u.ep=!0;const f=s(u);fetch(u.href,f)}})();function rp(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Wl={exports:{}},ri={},ql={exports:{}},ce={},md;function Gv(){if(md)return ce;md=1;/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i=Symbol.for("react.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.provider"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),y=Symbol.iterator;function _(k){return k===null||typeof k!="object"?null:(k=y&&k[y]||k["@@iterator"],typeof k=="function"?k:null)}var D={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,x={};function N(k,H,ue){this.props=k,this.context=H,this.refs=x,this.updater=ue||D}N.prototype.isReactComponent={},N.prototype.setState=function(k,H){if(typeof k!="object"&&typeof k!="function"&&k!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,k,H,"setState")},N.prototype.forceUpdate=function(k){this.updater.enqueueForceUpdate(this,k,"forceUpdate")};function M(){}M.prototype=N.prototype;function T(k,H,ue){this.props=k,this.context=H,this.refs=x,this.updater=ue||D}var L=T.prototype=new M;L.constructor=T,U(L,N.prototype),L.isPureReactComponent=!0;var R=Array.isArray,I=Object.prototype.hasOwnProperty,J={current:null},z={key:!0,ref:!0,__self:!0,__source:!0};function b(k,H,ue){var fe,he={},pe=null,Ee=null;if(H!=null)for(fe in H.ref!==void 0&&(Ee=H.ref),H.key!==void 0&&(pe=""+H.key),H)I.call(H,fe)&&!z.hasOwnProperty(fe)&&(he[fe]=H[fe]);var ye=arguments.length-2;if(ye===1)he.children=ue;else if(1<ye){for(var Ne=Array(ye),at=0;at<ye;at++)Ne[at]=arguments[at+2];he.children=Ne}if(k&&k.defaultProps)for(fe in ye=k.defaultProps,ye)he[fe]===void 0&&(he[fe]=ye[fe]);return{$$typeof:i,type:k,key:pe,ref:Ee,props:he,_owner:J.current}}function V(k,H){return{$$typeof:i,type:k.type,key:H,ref:k.ref,props:k.props,_owner:k._owner}}function ne(k){return typeof k=="object"&&k!==null&&k.$$typeof===i}function ae(k){var H={"=":"=0",":":"=2"};return"$"+k.replace(/[=:]/g,function(ue){return H[ue]})}var ge=/\/+/g;function X(k,H){return typeof k=="object"&&k!==null&&k.key!=null?ae(""+k.key):H.toString(36)}function je(k,H,ue,fe,he){var pe=typeof k;(pe==="undefined"||pe==="boolean")&&(k=null);var Ee=!1;if(k===null)Ee=!0;else switch(pe){case"string":case"number":Ee=!0;break;case"object":switch(k.$$typeof){case i:case r:Ee=!0}}if(Ee)return Ee=k,he=he(Ee),k=fe===""?"."+X(Ee,0):fe,R(he)?(ue="",k!=null&&(ue=k.replace(ge,"$&/")+"/"),je(he,H,ue,"",function(at){return at})):he!=null&&(ne(he)&&(he=V(he,ue+(!he.key||Ee&&Ee.key===he.key?"":(""+he.key).replace(ge,"$&/")+"/")+k)),H.push(he)),1;if(Ee=0,fe=fe===""?".":fe+":",R(k))for(var ye=0;ye<k.length;ye++){pe=k[ye];var Ne=fe+X(pe,ye);Ee+=je(pe,H,ue,Ne,he)}else if(Ne=_(k),typeof Ne=="function")for(k=Ne.call(k),ye=0;!(pe=k.next()).done;)pe=pe.value,Ne=fe+X(pe,ye++),Ee+=je(pe,H,ue,Ne,he);else if(pe==="object")throw H=String(k),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(k).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.");return Ee}function Se(k,H,ue){if(k==null)return k;var fe=[],he=0;return je(k,fe,"","",function(pe){return H.call(ue,pe,he++)}),fe}function Le(k){if(k._status===-1){var H=k._result;H=H(),H.then(function(ue){(k._status===0||k._status===-1)&&(k._status=1,k._result=ue)},function(ue){(k._status===0||k._status===-1)&&(k._status=2,k._result=ue)}),k._status===-1&&(k._status=0,k._result=H)}if(k._status===1)return k._result.default;throw k._result}var ke={current:null},Q={transition:null},oe={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Q,ReactCurrentOwner:J};function Y(){throw Error("act(...) is not supported in production builds of React.")}return ce.Children={map:Se,forEach:function(k,H,ue){Se(k,function(){H.apply(this,arguments)},ue)},count:function(k){var H=0;return Se(k,function(){H++}),H},toArray:function(k){return Se(k,function(H){return H})||[]},only:function(k){if(!ne(k))throw Error("React.Children.only expected to receive a single React element child.");return k}},ce.Component=N,ce.Fragment=s,ce.Profiler=u,ce.PureComponent=T,ce.StrictMode=l,ce.Suspense=m,ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=oe,ce.act=Y,ce.cloneElement=function(k,H,ue){if(k==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+k+".");var fe=U({},k.props),he=k.key,pe=k.ref,Ee=k._owner;if(H!=null){if(H.ref!==void 0&&(pe=H.ref,Ee=J.current),H.key!==void 0&&(he=""+H.key),k.type&&k.type.defaultProps)var ye=k.type.defaultProps;for(Ne in H)I.call(H,Ne)&&!z.hasOwnProperty(Ne)&&(fe[Ne]=H[Ne]===void 0&&ye!==void 0?ye[Ne]:H[Ne])}var Ne=arguments.length-2;if(Ne===1)fe.children=ue;else if(1<Ne){ye=Array(Ne);for(var at=0;at<Ne;at++)ye[at]=arguments[at+2];fe.children=ye}return{$$typeof:i,type:k.type,key:he,ref:pe,props:fe,_owner:Ee}},ce.createContext=function(k){return k={$$typeof:d,_currentValue:k,_currentValue2:k,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},k.Provider={$$typeof:f,_context:k},k.Consumer=k},ce.createElement=b,ce.createFactory=function(k){var H=b.bind(null,k);return H.type=k,H},ce.createRef=function(){return{current:null}},ce.forwardRef=function(k){return{$$typeof:p,render:k}},ce.isValidElement=ne,ce.lazy=function(k){return{$$typeof:g,_payload:{_status:-1,_result:k},_init:Le}},ce.memo=function(k,H){return{$$typeof:v,type:k,compare:H===void 0?null:H}},ce.startTransition=function(k){var H=Q.transition;Q.transition={};try{k()}finally{Q.transition=H}},ce.unstable_act=Y,ce.useCallback=function(k,H){return ke.current.useCallback(k,H)},ce.useContext=function(k){return ke.current.useContext(k)},ce.useDebugValue=function(){},ce.useDeferredValue=function(k){return ke.current.useDeferredValue(k)},ce.useEffect=function(k,H){return ke.current.useEffect(k,H)},ce.useId=function(){return ke.current.useId()},ce.useImperativeHandle=function(k,H,ue){return ke.current.useImperativeHandle(k,H,ue)},ce.useInsertionEffect=function(k,H){return ke.current.useInsertionEffect(k,H)},ce.useLayoutEffect=function(k,H){return ke.current.useLayoutEffect(k,H)},ce.useMemo=function(k,H){return ke.current.useMemo(k,H)},ce.useReducer=function(k,H,ue){return ke.current.useReducer(k,H,ue)},ce.useRef=function(k){return ke.current.useRef(k)},ce.useState=function(k){return ke.current.useState(k)},ce.useSyncExternalStore=function(k,H,ue){return ke.current.useSyncExternalStore(k,H,ue)},ce.useTransition=function(){return ke.current.useTransition()},ce.version="18.3.1",ce}var vd;function au(){return vd||(vd=1,ql.exports=Gv()),ql.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gd;function Yv(){if(gd)return ri;gd=1;var i=au(),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,u=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function d(p,m,v){var g,y={},_=null,D=null;v!==void 0&&(_=""+v),m.key!==void 0&&(_=""+m.key),m.ref!==void 0&&(D=m.ref);for(g in m)l.call(m,g)&&!f.hasOwnProperty(g)&&(y[g]=m[g]);if(p&&p.defaultProps)for(g in m=p.defaultProps,m)y[g]===void 0&&(y[g]=m[g]);return{$$typeof:r,type:p,key:_,ref:D,props:y,_owner:u.current}}return ri.Fragment=s,ri.jsx=d,ri.jsxs=d,ri}var yd;function Zv(){return yd||(yd=1,Wl.exports=Yv()),Wl.exports}var P=Zv(),F=au();const uu=rp(F),eg=Kv({__proto__:null,default:uu},[F]);var Ro={},$l={exports:{}},st={},Vl={exports:{}},Xl={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wd;function tg(){return wd||(wd=1,function(i){function r(Q,oe){var Y=Q.length;Q.push(oe);e:for(;0<Y;){var k=Y-1>>>1,H=Q[k];if(0<u(H,oe))Q[k]=oe,Q[Y]=H,Y=k;else break e}}function s(Q){return Q.length===0?null:Q[0]}function l(Q){if(Q.length===0)return null;var oe=Q[0],Y=Q.pop();if(Y!==oe){Q[0]=Y;e:for(var k=0,H=Q.length,ue=H>>>1;k<ue;){var fe=2*(k+1)-1,he=Q[fe],pe=fe+1,Ee=Q[pe];if(0>u(he,Y))pe<H&&0>u(Ee,he)?(Q[k]=Ee,Q[pe]=Y,k=pe):(Q[k]=he,Q[fe]=Y,k=fe);else if(pe<H&&0>u(Ee,Y))Q[k]=Ee,Q[pe]=Y,k=pe;else break e}}return oe}function u(Q,oe){var Y=Q.sortIndex-oe.sortIndex;return Y!==0?Y:Q.id-oe.id}if(typeof performance=="object"&&typeof performance.now=="function"){var f=performance;i.unstable_now=function(){return f.now()}}else{var d=Date,p=d.now();i.unstable_now=function(){return d.now()-p}}var m=[],v=[],g=1,y=null,_=3,D=!1,U=!1,x=!1,N=typeof setTimeout=="function"?setTimeout:null,M=typeof clearTimeout=="function"?clearTimeout:null,T=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function L(Q){for(var oe=s(v);oe!==null;){if(oe.callback===null)l(v);else if(oe.startTime<=Q)l(v),oe.sortIndex=oe.expirationTime,r(m,oe);else break;oe=s(v)}}function R(Q){if(x=!1,L(Q),!U)if(s(m)!==null)U=!0,Le(I);else{var oe=s(v);oe!==null&&ke(R,oe.startTime-Q)}}function I(Q,oe){U=!1,x&&(x=!1,M(b),b=-1),D=!0;var Y=_;try{for(L(oe),y=s(m);y!==null&&(!(y.expirationTime>oe)||Q&&!ae());){var k=y.callback;if(typeof k=="function"){y.callback=null,_=y.priorityLevel;var H=k(y.expirationTime<=oe);oe=i.unstable_now(),typeof H=="function"?y.callback=H:y===s(m)&&l(m),L(oe)}else l(m);y=s(m)}if(y!==null)var ue=!0;else{var fe=s(v);fe!==null&&ke(R,fe.startTime-oe),ue=!1}return ue}finally{y=null,_=Y,D=!1}}var J=!1,z=null,b=-1,V=5,ne=-1;function ae(){return!(i.unstable_now()-ne<V)}function ge(){if(z!==null){var Q=i.unstable_now();ne=Q;var oe=!0;try{oe=z(!0,Q)}finally{oe?X():(J=!1,z=null)}}else J=!1}var X;if(typeof T=="function")X=function(){T(ge)};else if(typeof MessageChannel<"u"){var je=new MessageChannel,Se=je.port2;je.port1.onmessage=ge,X=function(){Se.postMessage(null)}}else X=function(){N(ge,0)};function Le(Q){z=Q,J||(J=!0,X())}function ke(Q,oe){b=N(function(){Q(i.unstable_now())},oe)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(Q){Q.callback=null},i.unstable_continueExecution=function(){U||D||(U=!0,Le(I))},i.unstable_forceFrameRate=function(Q){0>Q||125<Q?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<Q?Math.floor(1e3/Q):5},i.unstable_getCurrentPriorityLevel=function(){return _},i.unstable_getFirstCallbackNode=function(){return s(m)},i.unstable_next=function(Q){switch(_){case 1:case 2:case 3:var oe=3;break;default:oe=_}var Y=_;_=oe;try{return Q()}finally{_=Y}},i.unstable_pauseExecution=function(){},i.unstable_requestPaint=function(){},i.unstable_runWithPriority=function(Q,oe){switch(Q){case 1:case 2:case 3:case 4:case 5:break;default:Q=3}var Y=_;_=Q;try{return oe()}finally{_=Y}},i.unstable_scheduleCallback=function(Q,oe,Y){var k=i.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?k+Y:k):Y=k,Q){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=Y+H,Q={id:g++,callback:oe,priorityLevel:Q,startTime:Y,expirationTime:H,sortIndex:-1},Y>k?(Q.sortIndex=Y,r(v,Q),s(m)===null&&Q===s(v)&&(x?(M(b),b=-1):x=!0,ke(R,Y-k))):(Q.sortIndex=H,r(m,Q),U||D||(U=!0,Le(I))),Q},i.unstable_shouldYield=ae,i.unstable_wrapCallback=function(Q){var oe=_;return function(){var Y=_;_=oe;try{return Q.apply(this,arguments)}finally{_=Y}}}}(Xl)),Xl}var Sd;function ng(){return Sd||(Sd=1,Vl.exports=tg()),Vl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ed;function rg(){if(Ed)return st;Ed=1;var i=au(),r=ng();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,u={};function f(e,t){d(e,t),d(e+"Capture",t)}function d(e,t){for(u[e]=t,e=0;e<t.length;e++)l.add(t[e])}var p=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,v=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,g={},y={};function _(e){return m.call(y,e)?!0:m.call(g,e)?!1:v.test(e)?y[e]=!0:(g[e]=!0,!1)}function D(e,t,n,o){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return o?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function U(e,t,n,o){if(t===null||typeof t>"u"||D(e,t,n,o))return!0;if(o)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function x(e,t,n,o,a,c,h){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=o,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=c,this.removeEmptyString=h}var N={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){N[e]=new x(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];N[t]=new x(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){N[e]=new x(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){N[e]=new x(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){N[e]=new x(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){N[e]=new x(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){N[e]=new x(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){N[e]=new x(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){N[e]=new x(e,5,!1,e.toLowerCase(),null,!1,!1)});var M=/[\-:]([a-z])/g;function T(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(M,T);N[t]=new x(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(M,T);N[t]=new x(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(M,T);N[t]=new x(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){N[e]=new x(e,1,!1,e.toLowerCase(),null,!1,!1)}),N.xlinkHref=new x("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){N[e]=new x(e,1,!1,e.toLowerCase(),null,!0,!0)});function L(e,t,n,o){var a=N.hasOwnProperty(t)?N[t]:null;(a!==null?a.type!==0:o||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(U(t,n,a,o)&&(n=null),o||a===null?_(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=n===null?a.type===3?!1:"":n:(t=a.attributeName,o=a.attributeNamespace,n===null?e.removeAttribute(t):(a=a.type,n=a===3||a===4&&n===!0?"":""+n,o?e.setAttributeNS(o,t,n):e.setAttribute(t,n))))}var R=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,I=Symbol.for("react.element"),J=Symbol.for("react.portal"),z=Symbol.for("react.fragment"),b=Symbol.for("react.strict_mode"),V=Symbol.for("react.profiler"),ne=Symbol.for("react.provider"),ae=Symbol.for("react.context"),ge=Symbol.for("react.forward_ref"),X=Symbol.for("react.suspense"),je=Symbol.for("react.suspense_list"),Se=Symbol.for("react.memo"),Le=Symbol.for("react.lazy"),ke=Symbol.for("react.offscreen"),Q=Symbol.iterator;function oe(e){return e===null||typeof e!="object"?null:(e=Q&&e[Q]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,k;function H(e){if(k===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);k=t&&t[1]||""}return`
`+k+e}var ue=!1;function fe(e,t){if(!e||ue)return"";ue=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(j){var o=j}Reflect.construct(e,[],t)}else{try{t.call()}catch(j){o=j}e.call(t.prototype)}else{try{throw Error()}catch(j){o=j}e()}}catch(j){if(j&&o&&typeof j.stack=="string"){for(var a=j.stack.split(`
`),c=o.stack.split(`
`),h=a.length-1,w=c.length-1;1<=h&&0<=w&&a[h]!==c[w];)w--;for(;1<=h&&0<=w;h--,w--)if(a[h]!==c[w]){if(h!==1||w!==1)do if(h--,w--,0>w||a[h]!==c[w]){var S=`
`+a[h].replace(" at new "," at ");return e.displayName&&S.includes("<anonymous>")&&(S=S.replace("<anonymous>",e.displayName)),S}while(1<=h&&0<=w);break}}}finally{ue=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?H(e):""}function he(e){switch(e.tag){case 5:return H(e.type);case 16:return H("Lazy");case 13:return H("Suspense");case 19:return H("SuspenseList");case 0:case 2:case 15:return e=fe(e.type,!1),e;case 11:return e=fe(e.type.render,!1),e;case 1:return e=fe(e.type,!0),e;default:return""}}function pe(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case z:return"Fragment";case J:return"Portal";case V:return"Profiler";case b:return"StrictMode";case X:return"Suspense";case je:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ae:return(e.displayName||"Context")+".Consumer";case ne:return(e._context.displayName||"Context")+".Provider";case ge:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Se:return t=e.displayName||null,t!==null?t:pe(e.type)||"Memo";case Le:t=e._payload,e=e._init;try{return pe(e(t))}catch{}}return null}function Ee(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return pe(t);case 8:return t===b?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ye(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ne(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function at(e){var t=Ne(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(h){o=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return o},setValue:function(h){o=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function mi(e){e._valueTracker||(e._valueTracker=at(e))}function Su(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),o="";return e&&(o=Ne(e)?e.checked?"true":"false":e.value),e=o,e!==n?(t.setValue(e),!0):!1}function vi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ko(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Eu(e,t){var n=t.defaultValue==null?"":t.defaultValue,o=t.checked!=null?t.checked:t.defaultChecked;n=ye(t.value!=null?t.value:n),e._wrapperState={initialChecked:o,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function _u(e,t){t=t.checked,t!=null&&L(e,"checked",t,!1)}function Go(e,t){_u(e,t);var n=ye(t.value),o=t.type;if(n!=null)o==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(o==="submit"||o==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Yo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Yo(e,t.type,ye(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function xu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!(o!=="submit"&&o!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Yo(e,t,n){(t!=="number"||vi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var yr=Array.isArray;function Dn(e,t,n,o){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&o&&(e[n].defaultSelected=!0)}else{for(n=""+ye(n),t=null,a=0;a<e.length;a++){if(e[a].value===n){e[a].selected=!0,o&&(e[a].defaultSelected=!0);return}t!==null||e[a].disabled||(t=e[a])}t!==null&&(t.selected=!0)}}function Zo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ku(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(s(92));if(yr(n)){if(1<n.length)throw Error(s(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:ye(n)}}function Cu(e,t){var n=ye(t.value),o=ye(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),o!=null&&(e.defaultValue=""+o)}function Ru(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Nu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function es(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Nu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var gi,Tu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,o,a){MSApp.execUnsafeLocalFunction(function(){return e(t,n,o,a)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(gi=gi||document.createElement("div"),gi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=gi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function wr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Sr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},em=["Webkit","ms","Moz","O"];Object.keys(Sr).forEach(function(e){em.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Sr[t]=Sr[e]})});function Pu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Sr.hasOwnProperty(e)&&Sr[e]?(""+t).trim():t+"px"}function Ou(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var o=n.indexOf("--")===0,a=Pu(n,t[n],o);n==="float"&&(n="cssFloat"),o?e.setProperty(n,a):e[n]=a}}var tm=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ts(e,t){if(t){if(tm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function ns(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var rs=null;function is(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var os=null,Fn=null,Mn=null;function ju(e){if(e=Hr(e)){if(typeof os!="function")throw Error(s(280));var t=e.stateNode;t&&(t=Bi(t),os(e.stateNode,e.type,t))}}function Lu(e){Fn?Mn?Mn.push(e):Mn=[e]:Fn=e}function Iu(){if(Fn){var e=Fn,t=Mn;if(Mn=Fn=null,ju(e),t)for(e=0;e<t.length;e++)ju(t[e])}}function bu(e,t){return e(t)}function Au(){}var ss=!1;function Uu(e,t,n){if(ss)return e(t,n);ss=!0;try{return bu(e,t,n)}finally{ss=!1,(Fn!==null||Mn!==null)&&(Au(),Iu())}}function Er(e,t){var n=e.stateNode;if(n===null)return null;var o=Bi(n);if(o===null)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var ls=!1;if(p)try{var _r={};Object.defineProperty(_r,"passive",{get:function(){ls=!0}}),window.addEventListener("test",_r,_r),window.removeEventListener("test",_r,_r)}catch{ls=!1}function nm(e,t,n,o,a,c,h,w,S){var j=Array.prototype.slice.call(arguments,3);try{t.apply(n,j)}catch(W){this.onError(W)}}var xr=!1,yi=null,wi=!1,as=null,rm={onError:function(e){xr=!0,yi=e}};function im(e,t,n,o,a,c,h,w,S){xr=!1,yi=null,nm.apply(rm,arguments)}function om(e,t,n,o,a,c,h,w,S){if(im.apply(this,arguments),xr){if(xr){var j=yi;xr=!1,yi=null}else throw Error(s(198));wi||(wi=!0,as=j)}}function Sn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Du(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Fu(e){if(Sn(e)!==e)throw Error(s(188))}function sm(e){var t=e.alternate;if(!t){if(t=Sn(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,o=t;;){var a=n.return;if(a===null)break;var c=a.alternate;if(c===null){if(o=a.return,o!==null){n=o;continue}break}if(a.child===c.child){for(c=a.child;c;){if(c===n)return Fu(a),e;if(c===o)return Fu(a),t;c=c.sibling}throw Error(s(188))}if(n.return!==o.return)n=a,o=c;else{for(var h=!1,w=a.child;w;){if(w===n){h=!0,n=a,o=c;break}if(w===o){h=!0,o=a,n=c;break}w=w.sibling}if(!h){for(w=c.child;w;){if(w===n){h=!0,n=c,o=a;break}if(w===o){h=!0,o=c,n=a;break}w=w.sibling}if(!h)throw Error(s(189))}}if(n.alternate!==o)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function Mu(e){return e=sm(e),e!==null?Bu(e):null}function Bu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bu(e);if(t!==null)return t;e=e.sibling}return null}var zu=r.unstable_scheduleCallback,Hu=r.unstable_cancelCallback,lm=r.unstable_shouldYield,am=r.unstable_requestPaint,be=r.unstable_now,um=r.unstable_getCurrentPriorityLevel,us=r.unstable_ImmediatePriority,Wu=r.unstable_UserBlockingPriority,Si=r.unstable_NormalPriority,cm=r.unstable_LowPriority,qu=r.unstable_IdlePriority,Ei=null,jt=null;function fm(e){if(jt&&typeof jt.onCommitFiberRoot=="function")try{jt.onCommitFiberRoot(Ei,e,void 0,(e.current.flags&128)===128)}catch{}}var _t=Math.clz32?Math.clz32:pm,dm=Math.log,hm=Math.LN2;function pm(e){return e>>>=0,e===0?32:31-(dm(e)/hm|0)|0}var _i=64,xi=4194304;function kr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ki(e,t){var n=e.pendingLanes;if(n===0)return 0;var o=0,a=e.suspendedLanes,c=e.pingedLanes,h=n&268435455;if(h!==0){var w=h&~a;w!==0?o=kr(w):(c&=h,c!==0&&(o=kr(c)))}else h=n&~a,h!==0?o=kr(h):c!==0&&(o=kr(c));if(o===0)return 0;if(t!==0&&t!==o&&(t&a)===0&&(a=o&-o,c=t&-t,a>=c||a===16&&(c&4194240)!==0))return t;if((o&4)!==0&&(o|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=o;0<t;)n=31-_t(t),a=1<<n,o|=e[n],t&=~a;return o}function mm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vm(e,t){for(var n=e.suspendedLanes,o=e.pingedLanes,a=e.expirationTimes,c=e.pendingLanes;0<c;){var h=31-_t(c),w=1<<h,S=a[h];S===-1?((w&n)===0||(w&o)!==0)&&(a[h]=mm(w,t)):S<=t&&(e.expiredLanes|=w),c&=~w}}function cs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function $u(){var e=_i;return _i<<=1,(_i&4194240)===0&&(_i=64),e}function fs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Cr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-_t(t),e[t]=n}function gm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-_t(n),c=1<<a;t[a]=0,o[a]=-1,e[a]=-1,n&=~c}}function ds(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var o=31-_t(n),a=1<<o;a&t|e[o]&t&&(e[o]|=t),n&=~a}}var we=0;function Vu(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Xu,hs,Ju,Qu,Ku,ps=!1,Ci=[],Xt=null,Jt=null,Qt=null,Rr=new Map,Nr=new Map,Kt=[],ym="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Gu(e,t){switch(e){case"focusin":case"focusout":Xt=null;break;case"dragenter":case"dragleave":Jt=null;break;case"mouseover":case"mouseout":Qt=null;break;case"pointerover":case"pointerout":Rr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nr.delete(t.pointerId)}}function Tr(e,t,n,o,a,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:o,nativeEvent:c,targetContainers:[a]},t!==null&&(t=Hr(t),t!==null&&hs(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,a!==null&&t.indexOf(a)===-1&&t.push(a),e)}function wm(e,t,n,o,a){switch(t){case"focusin":return Xt=Tr(Xt,e,t,n,o,a),!0;case"dragenter":return Jt=Tr(Jt,e,t,n,o,a),!0;case"mouseover":return Qt=Tr(Qt,e,t,n,o,a),!0;case"pointerover":var c=a.pointerId;return Rr.set(c,Tr(Rr.get(c)||null,e,t,n,o,a)),!0;case"gotpointercapture":return c=a.pointerId,Nr.set(c,Tr(Nr.get(c)||null,e,t,n,o,a)),!0}return!1}function Yu(e){var t=En(e.target);if(t!==null){var n=Sn(t);if(n!==null){if(t=n.tag,t===13){if(t=Du(n),t!==null){e.blockedOn=t,Ku(e.priority,function(){Ju(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ri(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=vs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var o=new n.constructor(n.type,n);rs=o,n.target.dispatchEvent(o),rs=null}else return t=Hr(n),t!==null&&hs(t),e.blockedOn=n,!1;t.shift()}return!0}function Zu(e,t,n){Ri(e)&&n.delete(t)}function Sm(){ps=!1,Xt!==null&&Ri(Xt)&&(Xt=null),Jt!==null&&Ri(Jt)&&(Jt=null),Qt!==null&&Ri(Qt)&&(Qt=null),Rr.forEach(Zu),Nr.forEach(Zu)}function Pr(e,t){e.blockedOn===t&&(e.blockedOn=null,ps||(ps=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Sm)))}function Or(e){function t(a){return Pr(a,e)}if(0<Ci.length){Pr(Ci[0],e);for(var n=1;n<Ci.length;n++){var o=Ci[n];o.blockedOn===e&&(o.blockedOn=null)}}for(Xt!==null&&Pr(Xt,e),Jt!==null&&Pr(Jt,e),Qt!==null&&Pr(Qt,e),Rr.forEach(t),Nr.forEach(t),n=0;n<Kt.length;n++)o=Kt[n],o.blockedOn===e&&(o.blockedOn=null);for(;0<Kt.length&&(n=Kt[0],n.blockedOn===null);)Yu(n),n.blockedOn===null&&Kt.shift()}var Bn=R.ReactCurrentBatchConfig,Ni=!0;function Em(e,t,n,o){var a=we,c=Bn.transition;Bn.transition=null;try{we=1,ms(e,t,n,o)}finally{we=a,Bn.transition=c}}function _m(e,t,n,o){var a=we,c=Bn.transition;Bn.transition=null;try{we=4,ms(e,t,n,o)}finally{we=a,Bn.transition=c}}function ms(e,t,n,o){if(Ni){var a=vs(e,t,n,o);if(a===null)Is(e,t,o,Ti,n),Gu(e,o);else if(wm(a,e,t,n,o))o.stopPropagation();else if(Gu(e,o),t&4&&-1<ym.indexOf(e)){for(;a!==null;){var c=Hr(a);if(c!==null&&Xu(c),c=vs(e,t,n,o),c===null&&Is(e,t,o,Ti,n),c===a)break;a=c}a!==null&&o.stopPropagation()}else Is(e,t,o,null,n)}}var Ti=null;function vs(e,t,n,o){if(Ti=null,e=is(o),e=En(e),e!==null)if(t=Sn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Du(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ti=e,null}function ec(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(um()){case us:return 1;case Wu:return 4;case Si:case cm:return 16;case qu:return 536870912;default:return 16}default:return 16}}var Gt=null,gs=null,Pi=null;function tc(){if(Pi)return Pi;var e,t=gs,n=t.length,o,a="value"in Gt?Gt.value:Gt.textContent,c=a.length;for(e=0;e<n&&t[e]===a[e];e++);var h=n-e;for(o=1;o<=h&&t[n-o]===a[c-o];o++);return Pi=a.slice(e,1<o?1-o:void 0)}function Oi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ji(){return!0}function nc(){return!1}function ut(e){function t(n,o,a,c,h){this._reactName=n,this._targetInst=a,this.type=o,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var w in e)e.hasOwnProperty(w)&&(n=e[w],this[w]=n?n(c):c[w]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ji:nc,this.isPropagationStopped=nc,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ji)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ji)},persist:function(){},isPersistent:ji}),t}var zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ys=ut(zn),jr=Y({},zn,{view:0,detail:0}),xm=ut(jr),ws,Ss,Lr,Li=Y({},jr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_s,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Lr&&(Lr&&e.type==="mousemove"?(ws=e.screenX-Lr.screenX,Ss=e.screenY-Lr.screenY):Ss=ws=0,Lr=e),ws)},movementY:function(e){return"movementY"in e?e.movementY:Ss}}),rc=ut(Li),km=Y({},Li,{dataTransfer:0}),Cm=ut(km),Rm=Y({},jr,{relatedTarget:0}),Es=ut(Rm),Nm=Y({},zn,{animationName:0,elapsedTime:0,pseudoElement:0}),Tm=ut(Nm),Pm=Y({},zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Om=ut(Pm),jm=Y({},zn,{data:0}),ic=ut(jm),Lm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Im={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},bm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Am(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=bm[e])?!!t[e]:!1}function _s(){return Am}var Um=Y({},jr,{key:function(e){if(e.key){var t=Lm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Oi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Im[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_s,charCode:function(e){return e.type==="keypress"?Oi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Oi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Dm=ut(Um),Fm=Y({},Li,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),oc=ut(Fm),Mm=Y({},jr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_s}),Bm=ut(Mm),zm=Y({},zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Hm=ut(zm),Wm=Y({},Li,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),qm=ut(Wm),$m=[9,13,27,32],xs=p&&"CompositionEvent"in window,Ir=null;p&&"documentMode"in document&&(Ir=document.documentMode);var Vm=p&&"TextEvent"in window&&!Ir,sc=p&&(!xs||Ir&&8<Ir&&11>=Ir),lc=" ",ac=!1;function uc(e,t){switch(e){case"keyup":return $m.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function cc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Hn=!1;function Xm(e,t){switch(e){case"compositionend":return cc(t);case"keypress":return t.which!==32?null:(ac=!0,lc);case"textInput":return e=t.data,e===lc&&ac?null:e;default:return null}}function Jm(e,t){if(Hn)return e==="compositionend"||!xs&&uc(e,t)?(e=tc(),Pi=gs=Gt=null,Hn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return sc&&t.locale!=="ko"?null:t.data;default:return null}}var Qm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function fc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Qm[e.type]:t==="textarea"}function dc(e,t,n,o){Lu(o),t=Di(t,"onChange"),0<t.length&&(n=new ys("onChange","change",null,n,o),e.push({event:n,listeners:t}))}var br=null,Ar=null;function Km(e){Oc(e,0)}function Ii(e){var t=Xn(e);if(Su(t))return e}function Gm(e,t){if(e==="change")return t}var hc=!1;if(p){var ks;if(p){var Cs="oninput"in document;if(!Cs){var pc=document.createElement("div");pc.setAttribute("oninput","return;"),Cs=typeof pc.oninput=="function"}ks=Cs}else ks=!1;hc=ks&&(!document.documentMode||9<document.documentMode)}function mc(){br&&(br.detachEvent("onpropertychange",vc),Ar=br=null)}function vc(e){if(e.propertyName==="value"&&Ii(Ar)){var t=[];dc(t,Ar,e,is(e)),Uu(Km,t)}}function Ym(e,t,n){e==="focusin"?(mc(),br=t,Ar=n,br.attachEvent("onpropertychange",vc)):e==="focusout"&&mc()}function Zm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ii(Ar)}function ev(e,t){if(e==="click")return Ii(t)}function tv(e,t){if(e==="input"||e==="change")return Ii(t)}function nv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:nv;function Ur(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(o=0;o<n.length;o++){var a=n[o];if(!m.call(t,a)||!xt(e[a],t[a]))return!1}return!0}function gc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function yc(e,t){var n=gc(e);e=0;for(var o;n;){if(n.nodeType===3){if(o=e+n.textContent.length,e<=t&&o>=t)return{node:n,offset:t-e};e=o}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=gc(n)}}function wc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?wc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Sc(){for(var e=window,t=vi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=vi(e.document)}return t}function Rs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function rv(e){var t=Sc(),n=e.focusedElem,o=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&wc(n.ownerDocument.documentElement,n)){if(o!==null&&Rs(n)){if(t=o.start,e=o.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var a=n.textContent.length,c=Math.min(o.start,a);o=o.end===void 0?c:Math.min(o.end,a),!e.extend&&c>o&&(a=o,o=c,c=a),a=yc(n,c);var h=yc(n,o);a&&h&&(e.rangeCount!==1||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(t=t.createRange(),t.setStart(a.node,a.offset),e.removeAllRanges(),c>o?(e.addRange(t),e.extend(h.node,h.offset)):(t.setEnd(h.node,h.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var iv=p&&"documentMode"in document&&11>=document.documentMode,Wn=null,Ns=null,Dr=null,Ts=!1;function Ec(e,t,n){var o=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ts||Wn==null||Wn!==vi(o)||(o=Wn,"selectionStart"in o&&Rs(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),Dr&&Ur(Dr,o)||(Dr=o,o=Di(Ns,"onSelect"),0<o.length&&(t=new ys("onSelect","select",null,t,n),e.push({event:t,listeners:o}),t.target=Wn)))}function bi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var qn={animationend:bi("Animation","AnimationEnd"),animationiteration:bi("Animation","AnimationIteration"),animationstart:bi("Animation","AnimationStart"),transitionend:bi("Transition","TransitionEnd")},Ps={},_c={};p&&(_c=document.createElement("div").style,"AnimationEvent"in window||(delete qn.animationend.animation,delete qn.animationiteration.animation,delete qn.animationstart.animation),"TransitionEvent"in window||delete qn.transitionend.transition);function Ai(e){if(Ps[e])return Ps[e];if(!qn[e])return e;var t=qn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in _c)return Ps[e]=t[n];return e}var xc=Ai("animationend"),kc=Ai("animationiteration"),Cc=Ai("animationstart"),Rc=Ai("transitionend"),Nc=new Map,Tc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yt(e,t){Nc.set(e,t),f(t,[e])}for(var Os=0;Os<Tc.length;Os++){var js=Tc[Os],ov=js.toLowerCase(),sv=js[0].toUpperCase()+js.slice(1);Yt(ov,"on"+sv)}Yt(xc,"onAnimationEnd"),Yt(kc,"onAnimationIteration"),Yt(Cc,"onAnimationStart"),Yt("dblclick","onDoubleClick"),Yt("focusin","onFocus"),Yt("focusout","onBlur"),Yt(Rc,"onTransitionEnd"),d("onMouseEnter",["mouseout","mouseover"]),d("onMouseLeave",["mouseout","mouseover"]),d("onPointerEnter",["pointerout","pointerover"]),d("onPointerLeave",["pointerout","pointerover"]),f("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),f("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),f("onBeforeInput",["compositionend","keypress","textInput","paste"]),f("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),lv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Pc(e,t,n){var o=e.type||"unknown-event";e.currentTarget=n,om(o,t,void 0,e),e.currentTarget=null}function Oc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var o=e[n],a=o.event;o=o.listeners;e:{var c=void 0;if(t)for(var h=o.length-1;0<=h;h--){var w=o[h],S=w.instance,j=w.currentTarget;if(w=w.listener,S!==c&&a.isPropagationStopped())break e;Pc(a,w,j),c=S}else for(h=0;h<o.length;h++){if(w=o[h],S=w.instance,j=w.currentTarget,w=w.listener,S!==c&&a.isPropagationStopped())break e;Pc(a,w,j),c=S}}}if(wi)throw e=as,wi=!1,as=null,e}function Ce(e,t){var n=t[Ms];n===void 0&&(n=t[Ms]=new Set);var o=e+"__bubble";n.has(o)||(jc(t,e,2,!1),n.add(o))}function Ls(e,t,n){var o=0;t&&(o|=4),jc(n,e,o,t)}var Ui="_reactListening"+Math.random().toString(36).slice(2);function Mr(e){if(!e[Ui]){e[Ui]=!0,l.forEach(function(n){n!=="selectionchange"&&(lv.has(n)||Ls(n,!1,e),Ls(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ui]||(t[Ui]=!0,Ls("selectionchange",!1,t))}}function jc(e,t,n,o){switch(ec(t)){case 1:var a=Em;break;case 4:a=_m;break;default:a=ms}n=a.bind(null,t,n,e),a=void 0,!ls||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(a=!0),o?a!==void 0?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):a!==void 0?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Is(e,t,n,o,a){var c=o;if((t&1)===0&&(t&2)===0&&o!==null)e:for(;;){if(o===null)return;var h=o.tag;if(h===3||h===4){var w=o.stateNode.containerInfo;if(w===a||w.nodeType===8&&w.parentNode===a)break;if(h===4)for(h=o.return;h!==null;){var S=h.tag;if((S===3||S===4)&&(S=h.stateNode.containerInfo,S===a||S.nodeType===8&&S.parentNode===a))return;h=h.return}for(;w!==null;){if(h=En(w),h===null)return;if(S=h.tag,S===5||S===6){o=c=h;continue e}w=w.parentNode}}o=o.return}Uu(function(){var j=c,W=is(n),q=[];e:{var B=Nc.get(e);if(B!==void 0){var K=ys,Z=e;switch(e){case"keypress":if(Oi(n)===0)break e;case"keydown":case"keyup":K=Dm;break;case"focusin":Z="focus",K=Es;break;case"focusout":Z="blur",K=Es;break;case"beforeblur":case"afterblur":K=Es;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":K=rc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":K=Cm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":K=Bm;break;case xc:case kc:case Cc:K=Tm;break;case Rc:K=Hm;break;case"scroll":K=xm;break;case"wheel":K=qm;break;case"copy":case"cut":case"paste":K=Om;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":K=oc}var ee=(t&4)!==0,Ae=!ee&&e==="scroll",C=ee?B!==null?B+"Capture":null:B;ee=[];for(var E=j,O;E!==null;){O=E;var $=O.stateNode;if(O.tag===5&&$!==null&&(O=$,C!==null&&($=Er(E,C),$!=null&&ee.push(Br(E,$,O)))),Ae)break;E=E.return}0<ee.length&&(B=new K(B,Z,null,n,W),q.push({event:B,listeners:ee}))}}if((t&7)===0){e:{if(B=e==="mouseover"||e==="pointerover",K=e==="mouseout"||e==="pointerout",B&&n!==rs&&(Z=n.relatedTarget||n.fromElement)&&(En(Z)||Z[Ft]))break e;if((K||B)&&(B=W.window===W?W:(B=W.ownerDocument)?B.defaultView||B.parentWindow:window,K?(Z=n.relatedTarget||n.toElement,K=j,Z=Z?En(Z):null,Z!==null&&(Ae=Sn(Z),Z!==Ae||Z.tag!==5&&Z.tag!==6)&&(Z=null)):(K=null,Z=j),K!==Z)){if(ee=rc,$="onMouseLeave",C="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(ee=oc,$="onPointerLeave",C="onPointerEnter",E="pointer"),Ae=K==null?B:Xn(K),O=Z==null?B:Xn(Z),B=new ee($,E+"leave",K,n,W),B.target=Ae,B.relatedTarget=O,$=null,En(W)===j&&(ee=new ee(C,E+"enter",Z,n,W),ee.target=O,ee.relatedTarget=Ae,$=ee),Ae=$,K&&Z)t:{for(ee=K,C=Z,E=0,O=ee;O;O=$n(O))E++;for(O=0,$=C;$;$=$n($))O++;for(;0<E-O;)ee=$n(ee),E--;for(;0<O-E;)C=$n(C),O--;for(;E--;){if(ee===C||C!==null&&ee===C.alternate)break t;ee=$n(ee),C=$n(C)}ee=null}else ee=null;K!==null&&Lc(q,B,K,ee,!1),Z!==null&&Ae!==null&&Lc(q,Ae,Z,ee,!0)}}e:{if(B=j?Xn(j):window,K=B.nodeName&&B.nodeName.toLowerCase(),K==="select"||K==="input"&&B.type==="file")var te=Gm;else if(fc(B))if(hc)te=tv;else{te=Zm;var re=Ym}else(K=B.nodeName)&&K.toLowerCase()==="input"&&(B.type==="checkbox"||B.type==="radio")&&(te=ev);if(te&&(te=te(e,j))){dc(q,te,n,W);break e}re&&re(e,B,j),e==="focusout"&&(re=B._wrapperState)&&re.controlled&&B.type==="number"&&Yo(B,"number",B.value)}switch(re=j?Xn(j):window,e){case"focusin":(fc(re)||re.contentEditable==="true")&&(Wn=re,Ns=j,Dr=null);break;case"focusout":Dr=Ns=Wn=null;break;case"mousedown":Ts=!0;break;case"contextmenu":case"mouseup":case"dragend":Ts=!1,Ec(q,n,W);break;case"selectionchange":if(iv)break;case"keydown":case"keyup":Ec(q,n,W)}var ie;if(xs)e:{switch(e){case"compositionstart":var se="onCompositionStart";break e;case"compositionend":se="onCompositionEnd";break e;case"compositionupdate":se="onCompositionUpdate";break e}se=void 0}else Hn?uc(e,n)&&(se="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(se="onCompositionStart");se&&(sc&&n.locale!=="ko"&&(Hn||se!=="onCompositionStart"?se==="onCompositionEnd"&&Hn&&(ie=tc()):(Gt=W,gs="value"in Gt?Gt.value:Gt.textContent,Hn=!0)),re=Di(j,se),0<re.length&&(se=new ic(se,e,null,n,W),q.push({event:se,listeners:re}),ie?se.data=ie:(ie=cc(n),ie!==null&&(se.data=ie)))),(ie=Vm?Xm(e,n):Jm(e,n))&&(j=Di(j,"onBeforeInput"),0<j.length&&(W=new ic("onBeforeInput","beforeinput",null,n,W),q.push({event:W,listeners:j}),W.data=ie))}Oc(q,t)})}function Br(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Di(e,t){for(var n=t+"Capture",o=[];e!==null;){var a=e,c=a.stateNode;a.tag===5&&c!==null&&(a=c,c=Er(e,n),c!=null&&o.unshift(Br(e,c,a)),c=Er(e,t),c!=null&&o.push(Br(e,c,a))),e=e.return}return o}function $n(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Lc(e,t,n,o,a){for(var c=t._reactName,h=[];n!==null&&n!==o;){var w=n,S=w.alternate,j=w.stateNode;if(S!==null&&S===o)break;w.tag===5&&j!==null&&(w=j,a?(S=Er(n,c),S!=null&&h.unshift(Br(n,S,w))):a||(S=Er(n,c),S!=null&&h.push(Br(n,S,w)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var av=/\r\n?/g,uv=/\u0000|\uFFFD/g;function Ic(e){return(typeof e=="string"?e:""+e).replace(av,`
`).replace(uv,"")}function Fi(e,t,n){if(t=Ic(t),Ic(e)!==t&&n)throw Error(s(425))}function Mi(){}var bs=null,As=null;function Us(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ds=typeof setTimeout=="function"?setTimeout:void 0,cv=typeof clearTimeout=="function"?clearTimeout:void 0,bc=typeof Promise=="function"?Promise:void 0,fv=typeof queueMicrotask=="function"?queueMicrotask:typeof bc<"u"?function(e){return bc.resolve(null).then(e).catch(dv)}:Ds;function dv(e){setTimeout(function(){throw e})}function Fs(e,t){var n=t,o=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&a.nodeType===8)if(n=a.data,n==="/$"){if(o===0){e.removeChild(a),Or(t);return}o--}else n!=="$"&&n!=="$?"&&n!=="$!"||o++;n=a}while(n);Or(t)}function Zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ac(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Vn=Math.random().toString(36).slice(2),Lt="__reactFiber$"+Vn,zr="__reactProps$"+Vn,Ft="__reactContainer$"+Vn,Ms="__reactEvents$"+Vn,hv="__reactListeners$"+Vn,pv="__reactHandles$"+Vn;function En(e){var t=e[Lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ft]||n[Lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ac(e);e!==null;){if(n=e[Lt])return n;e=Ac(e)}return t}e=n,n=e.parentNode}return null}function Hr(e){return e=e[Lt]||e[Ft],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Xn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function Bi(e){return e[zr]||null}var Bs=[],Jn=-1;function en(e){return{current:e}}function Re(e){0>Jn||(e.current=Bs[Jn],Bs[Jn]=null,Jn--)}function _e(e,t){Jn++,Bs[Jn]=e.current,e.current=t}var tn={},$e=en(tn),tt=en(!1),_n=tn;function Qn(e,t){var n=e.type.contextTypes;if(!n)return tn;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var a={},c;for(c in n)a[c]=t[c];return o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function nt(e){return e=e.childContextTypes,e!=null}function zi(){Re(tt),Re($e)}function Uc(e,t,n){if($e.current!==tn)throw Error(s(168));_e($e,t),_e(tt,n)}function Dc(e,t,n){var o=e.stateNode;if(t=t.childContextTypes,typeof o.getChildContext!="function")return n;o=o.getChildContext();for(var a in o)if(!(a in t))throw Error(s(108,Ee(e)||"Unknown",a));return Y({},n,o)}function Hi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tn,_n=$e.current,_e($e,e),_e(tt,tt.current),!0}function Fc(e,t,n){var o=e.stateNode;if(!o)throw Error(s(169));n?(e=Dc(e,t,_n),o.__reactInternalMemoizedMergedChildContext=e,Re(tt),Re($e),_e($e,e)):Re(tt),_e(tt,n)}var Mt=null,Wi=!1,zs=!1;function Mc(e){Mt===null?Mt=[e]:Mt.push(e)}function mv(e){Wi=!0,Mc(e)}function nn(){if(!zs&&Mt!==null){zs=!0;var e=0,t=we;try{var n=Mt;for(we=1;e<n.length;e++){var o=n[e];do o=o(!0);while(o!==null)}Mt=null,Wi=!1}catch(a){throw Mt!==null&&(Mt=Mt.slice(e+1)),zu(us,nn),a}finally{we=t,zs=!1}}return null}var Kn=[],Gn=0,qi=null,$i=0,pt=[],mt=0,xn=null,Bt=1,zt="";function kn(e,t){Kn[Gn++]=$i,Kn[Gn++]=qi,qi=e,$i=t}function Bc(e,t,n){pt[mt++]=Bt,pt[mt++]=zt,pt[mt++]=xn,xn=e;var o=Bt;e=zt;var a=32-_t(o)-1;o&=~(1<<a),n+=1;var c=32-_t(t)+a;if(30<c){var h=a-a%5;c=(o&(1<<h)-1).toString(32),o>>=h,a-=h,Bt=1<<32-_t(t)+a|n<<a|o,zt=c+e}else Bt=1<<c|n<<a|o,zt=e}function Hs(e){e.return!==null&&(kn(e,1),Bc(e,1,0))}function Ws(e){for(;e===qi;)qi=Kn[--Gn],Kn[Gn]=null,$i=Kn[--Gn],Kn[Gn]=null;for(;e===xn;)xn=pt[--mt],pt[mt]=null,zt=pt[--mt],pt[mt]=null,Bt=pt[--mt],pt[mt]=null}var ct=null,ft=null,Te=!1,kt=null;function zc(e,t){var n=wt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Hc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ct=e,ft=Zt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ct=e,ft=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xn!==null?{id:Bt,overflow:zt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=wt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ct=e,ft=null,!0):!1;default:return!1}}function qs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $s(e){if(Te){var t=ft;if(t){var n=t;if(!Hc(e,t)){if(qs(e))throw Error(s(418));t=Zt(n.nextSibling);var o=ct;t&&Hc(e,t)?zc(o,n):(e.flags=e.flags&-4097|2,Te=!1,ct=e)}}else{if(qs(e))throw Error(s(418));e.flags=e.flags&-4097|2,Te=!1,ct=e}}}function Wc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ct=e}function Vi(e){if(e!==ct)return!1;if(!Te)return Wc(e),Te=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Us(e.type,e.memoizedProps)),t&&(t=ft)){if(qs(e))throw qc(),Error(s(418));for(;t;)zc(e,t),t=Zt(t.nextSibling)}if(Wc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ft=Zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ft=null}}else ft=ct?Zt(e.stateNode.nextSibling):null;return!0}function qc(){for(var e=ft;e;)e=Zt(e.nextSibling)}function Yn(){ft=ct=null,Te=!1}function Vs(e){kt===null?kt=[e]:kt.push(e)}var vv=R.ReactCurrentBatchConfig;function Wr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(s(309));var o=n.stateNode}if(!o)throw Error(s(147,e));var a=o,c=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c?t.ref:(t=function(h){var w=a.refs;h===null?delete w[c]:w[c]=h},t._stringRef=c,t)}if(typeof e!="string")throw Error(s(284));if(!n._owner)throw Error(s(290,e))}return e}function Xi(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function $c(e){var t=e._init;return t(e._payload)}function Vc(e){function t(C,E){if(e){var O=C.deletions;O===null?(C.deletions=[E],C.flags|=16):O.push(E)}}function n(C,E){if(!e)return null;for(;E!==null;)t(C,E),E=E.sibling;return null}function o(C,E){for(C=new Map;E!==null;)E.key!==null?C.set(E.key,E):C.set(E.index,E),E=E.sibling;return C}function a(C,E){return C=fn(C,E),C.index=0,C.sibling=null,C}function c(C,E,O){return C.index=O,e?(O=C.alternate,O!==null?(O=O.index,O<E?(C.flags|=2,E):O):(C.flags|=2,E)):(C.flags|=1048576,E)}function h(C){return e&&C.alternate===null&&(C.flags|=2),C}function w(C,E,O,$){return E===null||E.tag!==6?(E=Dl(O,C.mode,$),E.return=C,E):(E=a(E,O),E.return=C,E)}function S(C,E,O,$){var te=O.type;return te===z?W(C,E,O.props.children,$,O.key):E!==null&&(E.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===Le&&$c(te)===E.type)?($=a(E,O.props),$.ref=Wr(C,E,O),$.return=C,$):($=yo(O.type,O.key,O.props,null,C.mode,$),$.ref=Wr(C,E,O),$.return=C,$)}function j(C,E,O,$){return E===null||E.tag!==4||E.stateNode.containerInfo!==O.containerInfo||E.stateNode.implementation!==O.implementation?(E=Fl(O,C.mode,$),E.return=C,E):(E=a(E,O.children||[]),E.return=C,E)}function W(C,E,O,$,te){return E===null||E.tag!==7?(E=Ln(O,C.mode,$,te),E.return=C,E):(E=a(E,O),E.return=C,E)}function q(C,E,O){if(typeof E=="string"&&E!==""||typeof E=="number")return E=Dl(""+E,C.mode,O),E.return=C,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case I:return O=yo(E.type,E.key,E.props,null,C.mode,O),O.ref=Wr(C,null,E),O.return=C,O;case J:return E=Fl(E,C.mode,O),E.return=C,E;case Le:var $=E._init;return q(C,$(E._payload),O)}if(yr(E)||oe(E))return E=Ln(E,C.mode,O,null),E.return=C,E;Xi(C,E)}return null}function B(C,E,O,$){var te=E!==null?E.key:null;if(typeof O=="string"&&O!==""||typeof O=="number")return te!==null?null:w(C,E,""+O,$);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case I:return O.key===te?S(C,E,O,$):null;case J:return O.key===te?j(C,E,O,$):null;case Le:return te=O._init,B(C,E,te(O._payload),$)}if(yr(O)||oe(O))return te!==null?null:W(C,E,O,$,null);Xi(C,O)}return null}function K(C,E,O,$,te){if(typeof $=="string"&&$!==""||typeof $=="number")return C=C.get(O)||null,w(E,C,""+$,te);if(typeof $=="object"&&$!==null){switch($.$$typeof){case I:return C=C.get($.key===null?O:$.key)||null,S(E,C,$,te);case J:return C=C.get($.key===null?O:$.key)||null,j(E,C,$,te);case Le:var re=$._init;return K(C,E,O,re($._payload),te)}if(yr($)||oe($))return C=C.get(O)||null,W(E,C,$,te,null);Xi(E,$)}return null}function Z(C,E,O,$){for(var te=null,re=null,ie=E,se=E=0,He=null;ie!==null&&se<O.length;se++){ie.index>se?(He=ie,ie=null):He=ie.sibling;var me=B(C,ie,O[se],$);if(me===null){ie===null&&(ie=He);break}e&&ie&&me.alternate===null&&t(C,ie),E=c(me,E,se),re===null?te=me:re.sibling=me,re=me,ie=He}if(se===O.length)return n(C,ie),Te&&kn(C,se),te;if(ie===null){for(;se<O.length;se++)ie=q(C,O[se],$),ie!==null&&(E=c(ie,E,se),re===null?te=ie:re.sibling=ie,re=ie);return Te&&kn(C,se),te}for(ie=o(C,ie);se<O.length;se++)He=K(ie,C,se,O[se],$),He!==null&&(e&&He.alternate!==null&&ie.delete(He.key===null?se:He.key),E=c(He,E,se),re===null?te=He:re.sibling=He,re=He);return e&&ie.forEach(function(dn){return t(C,dn)}),Te&&kn(C,se),te}function ee(C,E,O,$){var te=oe(O);if(typeof te!="function")throw Error(s(150));if(O=te.call(O),O==null)throw Error(s(151));for(var re=te=null,ie=E,se=E=0,He=null,me=O.next();ie!==null&&!me.done;se++,me=O.next()){ie.index>se?(He=ie,ie=null):He=ie.sibling;var dn=B(C,ie,me.value,$);if(dn===null){ie===null&&(ie=He);break}e&&ie&&dn.alternate===null&&t(C,ie),E=c(dn,E,se),re===null?te=dn:re.sibling=dn,re=dn,ie=He}if(me.done)return n(C,ie),Te&&kn(C,se),te;if(ie===null){for(;!me.done;se++,me=O.next())me=q(C,me.value,$),me!==null&&(E=c(me,E,se),re===null?te=me:re.sibling=me,re=me);return Te&&kn(C,se),te}for(ie=o(C,ie);!me.done;se++,me=O.next())me=K(ie,C,se,me.value,$),me!==null&&(e&&me.alternate!==null&&ie.delete(me.key===null?se:me.key),E=c(me,E,se),re===null?te=me:re.sibling=me,re=me);return e&&ie.forEach(function(Qv){return t(C,Qv)}),Te&&kn(C,se),te}function Ae(C,E,O,$){if(typeof O=="object"&&O!==null&&O.type===z&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case I:e:{for(var te=O.key,re=E;re!==null;){if(re.key===te){if(te=O.type,te===z){if(re.tag===7){n(C,re.sibling),E=a(re,O.props.children),E.return=C,C=E;break e}}else if(re.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===Le&&$c(te)===re.type){n(C,re.sibling),E=a(re,O.props),E.ref=Wr(C,re,O),E.return=C,C=E;break e}n(C,re);break}else t(C,re);re=re.sibling}O.type===z?(E=Ln(O.props.children,C.mode,$,O.key),E.return=C,C=E):($=yo(O.type,O.key,O.props,null,C.mode,$),$.ref=Wr(C,E,O),$.return=C,C=$)}return h(C);case J:e:{for(re=O.key;E!==null;){if(E.key===re)if(E.tag===4&&E.stateNode.containerInfo===O.containerInfo&&E.stateNode.implementation===O.implementation){n(C,E.sibling),E=a(E,O.children||[]),E.return=C,C=E;break e}else{n(C,E);break}else t(C,E);E=E.sibling}E=Fl(O,C.mode,$),E.return=C,C=E}return h(C);case Le:return re=O._init,Ae(C,E,re(O._payload),$)}if(yr(O))return Z(C,E,O,$);if(oe(O))return ee(C,E,O,$);Xi(C,O)}return typeof O=="string"&&O!==""||typeof O=="number"?(O=""+O,E!==null&&E.tag===6?(n(C,E.sibling),E=a(E,O),E.return=C,C=E):(n(C,E),E=Dl(O,C.mode,$),E.return=C,C=E),h(C)):n(C,E)}return Ae}var Zn=Vc(!0),Xc=Vc(!1),Ji=en(null),Qi=null,er=null,Xs=null;function Js(){Xs=er=Qi=null}function Qs(e){var t=Ji.current;Re(Ji),e._currentValue=t}function Ks(e,t,n){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===n)break;e=e.return}}function tr(e,t){Qi=e,Xs=er=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(rt=!0),e.firstContext=null)}function vt(e){var t=e._currentValue;if(Xs!==e)if(e={context:e,memoizedValue:t,next:null},er===null){if(Qi===null)throw Error(s(308));er=e,Qi.dependencies={lanes:0,firstContext:e}}else er=er.next=e;return t}var Cn=null;function Gs(e){Cn===null?Cn=[e]:Cn.push(e)}function Jc(e,t,n,o){var a=t.interleaved;return a===null?(n.next=n,Gs(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ht(e,o)}function Ht(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var rn=!1;function Ys(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Qc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Wt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function on(e,t,n){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(de&2)!==0){var a=o.pending;return a===null?t.next=t:(t.next=a.next,a.next=t),o.pending=t,Ht(e,n)}return a=o.interleaved,a===null?(t.next=t,Gs(o)):(t.next=a.next,a.next=t),o.interleaved=t,Ht(e,n)}function Ki(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var o=t.lanes;o&=e.pendingLanes,n|=o,t.lanes=n,ds(e,n)}}function Kc(e,t){var n=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,n===o)){var a=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var h={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};c===null?a=c=h:c=c.next=h,n=n.next}while(n!==null);c===null?a=c=t:c=c.next=t}else a=c=t;n={baseState:o.baseState,firstBaseUpdate:a,lastBaseUpdate:c,shared:o.shared,effects:o.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Gi(e,t,n,o){var a=e.updateQueue;rn=!1;var c=a.firstBaseUpdate,h=a.lastBaseUpdate,w=a.shared.pending;if(w!==null){a.shared.pending=null;var S=w,j=S.next;S.next=null,h===null?c=j:h.next=j,h=S;var W=e.alternate;W!==null&&(W=W.updateQueue,w=W.lastBaseUpdate,w!==h&&(w===null?W.firstBaseUpdate=j:w.next=j,W.lastBaseUpdate=S))}if(c!==null){var q=a.baseState;h=0,W=j=S=null,w=c;do{var B=w.lane,K=w.eventTime;if((o&B)===B){W!==null&&(W=W.next={eventTime:K,lane:0,tag:w.tag,payload:w.payload,callback:w.callback,next:null});e:{var Z=e,ee=w;switch(B=t,K=n,ee.tag){case 1:if(Z=ee.payload,typeof Z=="function"){q=Z.call(K,q,B);break e}q=Z;break e;case 3:Z.flags=Z.flags&-65537|128;case 0:if(Z=ee.payload,B=typeof Z=="function"?Z.call(K,q,B):Z,B==null)break e;q=Y({},q,B);break e;case 2:rn=!0}}w.callback!==null&&w.lane!==0&&(e.flags|=64,B=a.effects,B===null?a.effects=[w]:B.push(w))}else K={eventTime:K,lane:B,tag:w.tag,payload:w.payload,callback:w.callback,next:null},W===null?(j=W=K,S=q):W=W.next=K,h|=B;if(w=w.next,w===null){if(w=a.shared.pending,w===null)break;B=w,w=B.next,B.next=null,a.lastBaseUpdate=B,a.shared.pending=null}}while(!0);if(W===null&&(S=q),a.baseState=S,a.firstBaseUpdate=j,a.lastBaseUpdate=W,t=a.shared.interleaved,t!==null){a=t;do h|=a.lane,a=a.next;while(a!==t)}else c===null&&(a.shared.lanes=0);Tn|=h,e.lanes=h,e.memoizedState=q}}function Gc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var o=e[t],a=o.callback;if(a!==null){if(o.callback=null,o=n,typeof a!="function")throw Error(s(191,a));a.call(o)}}}var qr={},It=en(qr),$r=en(qr),Vr=en(qr);function Rn(e){if(e===qr)throw Error(s(174));return e}function Zs(e,t){switch(_e(Vr,t),_e($r,e),_e(It,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:es(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=es(t,e)}Re(It),_e(It,t)}function nr(){Re(It),Re($r),Re(Vr)}function Yc(e){Rn(Vr.current);var t=Rn(It.current),n=es(t,e.type);t!==n&&(_e($r,e),_e(It,n))}function el(e){$r.current===e&&(Re(It),Re($r))}var Pe=en(0);function Yi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var tl=[];function nl(){for(var e=0;e<tl.length;e++)tl[e]._workInProgressVersionPrimary=null;tl.length=0}var Zi=R.ReactCurrentDispatcher,rl=R.ReactCurrentBatchConfig,Nn=0,Oe=null,Fe=null,Be=null,eo=!1,Xr=!1,Jr=0,gv=0;function Ve(){throw Error(s(321))}function il(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!xt(e[n],t[n]))return!1;return!0}function ol(e,t,n,o,a,c){if(Nn=c,Oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Zi.current=e===null||e.memoizedState===null?Ev:_v,e=n(o,a),Xr){c=0;do{if(Xr=!1,Jr=0,25<=c)throw Error(s(301));c+=1,Be=Fe=null,t.updateQueue=null,Zi.current=xv,e=n(o,a)}while(Xr)}if(Zi.current=ro,t=Fe!==null&&Fe.next!==null,Nn=0,Be=Fe=Oe=null,eo=!1,t)throw Error(s(300));return e}function sl(){var e=Jr!==0;return Jr=0,e}function bt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Be===null?Oe.memoizedState=Be=e:Be=Be.next=e,Be}function gt(){if(Fe===null){var e=Oe.alternate;e=e!==null?e.memoizedState:null}else e=Fe.next;var t=Be===null?Oe.memoizedState:Be.next;if(t!==null)Be=t,Fe=e;else{if(e===null)throw Error(s(310));Fe=e,e={memoizedState:Fe.memoizedState,baseState:Fe.baseState,baseQueue:Fe.baseQueue,queue:Fe.queue,next:null},Be===null?Oe.memoizedState=Be=e:Be=Be.next=e}return Be}function Qr(e,t){return typeof t=="function"?t(e):t}function ll(e){var t=gt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var o=Fe,a=o.baseQueue,c=n.pending;if(c!==null){if(a!==null){var h=a.next;a.next=c.next,c.next=h}o.baseQueue=a=c,n.pending=null}if(a!==null){c=a.next,o=o.baseState;var w=h=null,S=null,j=c;do{var W=j.lane;if((Nn&W)===W)S!==null&&(S=S.next={lane:0,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null}),o=j.hasEagerState?j.eagerState:e(o,j.action);else{var q={lane:W,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null};S===null?(w=S=q,h=o):S=S.next=q,Oe.lanes|=W,Tn|=W}j=j.next}while(j!==null&&j!==c);S===null?h=o:S.next=w,xt(o,t.memoizedState)||(rt=!0),t.memoizedState=o,t.baseState=h,t.baseQueue=S,n.lastRenderedState=o}if(e=n.interleaved,e!==null){a=e;do c=a.lane,Oe.lanes|=c,Tn|=c,a=a.next;while(a!==e)}else a===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function al(e){var t=gt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var o=n.dispatch,a=n.pending,c=t.memoizedState;if(a!==null){n.pending=null;var h=a=a.next;do c=e(c,h.action),h=h.next;while(h!==a);xt(c,t.memoizedState)||(rt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,o]}function Zc(){}function ef(e,t){var n=Oe,o=gt(),a=t(),c=!xt(o.memoizedState,a);if(c&&(o.memoizedState=a,rt=!0),o=o.queue,ul(rf.bind(null,n,o,e),[e]),o.getSnapshot!==t||c||Be!==null&&Be.memoizedState.tag&1){if(n.flags|=2048,Kr(9,nf.bind(null,n,o,a,t),void 0,null),ze===null)throw Error(s(349));(Nn&30)!==0||tf(n,t,a)}return a}function tf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function nf(e,t,n,o){t.value=n,t.getSnapshot=o,of(t)&&sf(e)}function rf(e,t,n){return n(function(){of(t)&&sf(e)})}function of(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!xt(e,n)}catch{return!0}}function sf(e){var t=Ht(e,1);t!==null&&Tt(t,e,1,-1)}function lf(e){var t=bt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Qr,lastRenderedState:e},t.queue=e,e=e.dispatch=Sv.bind(null,Oe,e),[t.memoizedState,e]}function Kr(e,t,n,o){return e={tag:e,create:t,destroy:n,deps:o,next:null},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(o=n.next,n.next=e,e.next=o,t.lastEffect=e)),e}function af(){return gt().memoizedState}function to(e,t,n,o){var a=bt();Oe.flags|=e,a.memoizedState=Kr(1|t,n,void 0,o===void 0?null:o)}function no(e,t,n,o){var a=gt();o=o===void 0?null:o;var c=void 0;if(Fe!==null){var h=Fe.memoizedState;if(c=h.destroy,o!==null&&il(o,h.deps)){a.memoizedState=Kr(t,n,c,o);return}}Oe.flags|=e,a.memoizedState=Kr(1|t,n,c,o)}function uf(e,t){return to(8390656,8,e,t)}function ul(e,t){return no(2048,8,e,t)}function cf(e,t){return no(4,2,e,t)}function ff(e,t){return no(4,4,e,t)}function df(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function hf(e,t,n){return n=n!=null?n.concat([e]):null,no(4,4,df.bind(null,t,e),n)}function cl(){}function pf(e,t){var n=gt();t=t===void 0?null:t;var o=n.memoizedState;return o!==null&&t!==null&&il(t,o[1])?o[0]:(n.memoizedState=[e,t],e)}function mf(e,t){var n=gt();t=t===void 0?null:t;var o=n.memoizedState;return o!==null&&t!==null&&il(t,o[1])?o[0]:(e=e(),n.memoizedState=[e,t],e)}function vf(e,t,n){return(Nn&21)===0?(e.baseState&&(e.baseState=!1,rt=!0),e.memoizedState=n):(xt(n,t)||(n=$u(),Oe.lanes|=n,Tn|=n,e.baseState=!0),t)}function yv(e,t){var n=we;we=n!==0&&4>n?n:4,e(!0);var o=rl.transition;rl.transition={};try{e(!1),t()}finally{we=n,rl.transition=o}}function gf(){return gt().memoizedState}function wv(e,t,n){var o=un(e);if(n={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null},yf(e))wf(t,n);else if(n=Jc(e,t,n,o),n!==null){var a=Ye();Tt(n,e,o,a),Sf(n,t,o)}}function Sv(e,t,n){var o=un(e),a={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null};if(yf(e))wf(t,a);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,w=c(h,n);if(a.hasEagerState=!0,a.eagerState=w,xt(w,h)){var S=t.interleaved;S===null?(a.next=a,Gs(t)):(a.next=S.next,S.next=a),t.interleaved=a;return}}catch{}finally{}n=Jc(e,t,a,o),n!==null&&(a=Ye(),Tt(n,e,o,a),Sf(n,t,o))}}function yf(e){var t=e.alternate;return e===Oe||t!==null&&t===Oe}function wf(e,t){Xr=eo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Sf(e,t,n){if((n&4194240)!==0){var o=t.lanes;o&=e.pendingLanes,n|=o,t.lanes=n,ds(e,n)}}var ro={readContext:vt,useCallback:Ve,useContext:Ve,useEffect:Ve,useImperativeHandle:Ve,useInsertionEffect:Ve,useLayoutEffect:Ve,useMemo:Ve,useReducer:Ve,useRef:Ve,useState:Ve,useDebugValue:Ve,useDeferredValue:Ve,useTransition:Ve,useMutableSource:Ve,useSyncExternalStore:Ve,useId:Ve,unstable_isNewReconciler:!1},Ev={readContext:vt,useCallback:function(e,t){return bt().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:uf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,to(4194308,4,df.bind(null,t,e),n)},useLayoutEffect:function(e,t){return to(4194308,4,e,t)},useInsertionEffect:function(e,t){return to(4,2,e,t)},useMemo:function(e,t){var n=bt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var o=bt();return t=n!==void 0?n(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=wv.bind(null,Oe,e),[o.memoizedState,e]},useRef:function(e){var t=bt();return e={current:e},t.memoizedState=e},useState:lf,useDebugValue:cl,useDeferredValue:function(e){return bt().memoizedState=e},useTransition:function(){var e=lf(!1),t=e[0];return e=yv.bind(null,e[1]),bt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var o=Oe,a=bt();if(Te){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),ze===null)throw Error(s(349));(Nn&30)!==0||tf(o,t,n)}a.memoizedState=n;var c={value:n,getSnapshot:t};return a.queue=c,uf(rf.bind(null,o,c,e),[e]),o.flags|=2048,Kr(9,nf.bind(null,o,c,n,t),void 0,null),n},useId:function(){var e=bt(),t=ze.identifierPrefix;if(Te){var n=zt,o=Bt;n=(o&~(1<<32-_t(o)-1)).toString(32)+n,t=":"+t+"R"+n,n=Jr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=gv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},_v={readContext:vt,useCallback:pf,useContext:vt,useEffect:ul,useImperativeHandle:hf,useInsertionEffect:cf,useLayoutEffect:ff,useMemo:mf,useReducer:ll,useRef:af,useState:function(){return ll(Qr)},useDebugValue:cl,useDeferredValue:function(e){var t=gt();return vf(t,Fe.memoizedState,e)},useTransition:function(){var e=ll(Qr)[0],t=gt().memoizedState;return[e,t]},useMutableSource:Zc,useSyncExternalStore:ef,useId:gf,unstable_isNewReconciler:!1},xv={readContext:vt,useCallback:pf,useContext:vt,useEffect:ul,useImperativeHandle:hf,useInsertionEffect:cf,useLayoutEffect:ff,useMemo:mf,useReducer:al,useRef:af,useState:function(){return al(Qr)},useDebugValue:cl,useDeferredValue:function(e){var t=gt();return Fe===null?t.memoizedState=e:vf(t,Fe.memoizedState,e)},useTransition:function(){var e=al(Qr)[0],t=gt().memoizedState;return[e,t]},useMutableSource:Zc,useSyncExternalStore:ef,useId:gf,unstable_isNewReconciler:!1};function Ct(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function fl(e,t,n,o){t=e.memoizedState,n=n(o,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var io={isMounted:function(e){return(e=e._reactInternals)?Sn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var o=Ye(),a=un(e),c=Wt(o,a);c.payload=t,n!=null&&(c.callback=n),t=on(e,c,a),t!==null&&(Tt(t,e,a,o),Ki(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var o=Ye(),a=un(e),c=Wt(o,a);c.tag=1,c.payload=t,n!=null&&(c.callback=n),t=on(e,c,a),t!==null&&(Tt(t,e,a,o),Ki(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ye(),o=un(e),a=Wt(n,o);a.tag=2,t!=null&&(a.callback=t),t=on(e,a,o),t!==null&&(Tt(t,e,o,n),Ki(t,e,o))}};function Ef(e,t,n,o,a,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,c,h):t.prototype&&t.prototype.isPureReactComponent?!Ur(n,o)||!Ur(a,c):!0}function _f(e,t,n){var o=!1,a=tn,c=t.contextType;return typeof c=="object"&&c!==null?c=vt(c):(a=nt(t)?_n:$e.current,o=t.contextTypes,c=(o=o!=null)?Qn(e,a):tn),t=new t(n,c),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=io,e.stateNode=t,t._reactInternals=e,o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=c),t}function xf(e,t,n,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,o),t.state!==e&&io.enqueueReplaceState(t,t.state,null)}function dl(e,t,n,o){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ys(e);var c=t.contextType;typeof c=="object"&&c!==null?a.context=vt(c):(c=nt(t)?_n:$e.current,a.context=Qn(e,c)),a.state=e.memoizedState,c=t.getDerivedStateFromProps,typeof c=="function"&&(fl(e,t,c,n),a.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(t=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),t!==a.state&&io.enqueueReplaceState(a,a.state,null),Gi(e,n,a,o),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308)}function rr(e,t){try{var n="",o=t;do n+=he(o),o=o.return;while(o);var a=n}catch(c){a=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:t,stack:a,digest:null}}function hl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function pl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var kv=typeof WeakMap=="function"?WeakMap:Map;function kf(e,t,n){n=Wt(-1,n),n.tag=3,n.payload={element:null};var o=t.value;return n.callback=function(){fo||(fo=!0,Pl=o),pl(e,t)},n}function Cf(e,t,n){n=Wt(-1,n),n.tag=3;var o=e.type.getDerivedStateFromError;if(typeof o=="function"){var a=t.value;n.payload=function(){return o(a)},n.callback=function(){pl(e,t)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(n.callback=function(){pl(e,t),typeof o!="function"&&(ln===null?ln=new Set([this]):ln.add(this));var h=t.stack;this.componentDidCatch(t.value,{componentStack:h!==null?h:""})}),n}function Rf(e,t,n){var o=e.pingCache;if(o===null){o=e.pingCache=new kv;var a=new Set;o.set(t,a)}else a=o.get(t),a===void 0&&(a=new Set,o.set(t,a));a.has(n)||(a.add(n),e=Fv.bind(null,e,t,n),t.then(e,e))}function Nf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Tf(e,t,n,o,a){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Wt(-1,1),t.tag=2,on(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var Cv=R.ReactCurrentOwner,rt=!1;function Ge(e,t,n,o){t.child=e===null?Xc(t,null,n,o):Zn(t,e.child,n,o)}function Pf(e,t,n,o,a){n=n.render;var c=t.ref;return tr(t,a),o=ol(e,t,n,o,c,a),n=sl(),e!==null&&!rt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,qt(e,t,a)):(Te&&n&&Hs(t),t.flags|=1,Ge(e,t,o,a),t.child)}function Of(e,t,n,o,a){if(e===null){var c=n.type;return typeof c=="function"&&!Ul(c)&&c.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=c,jf(e,t,c,o,a)):(e=yo(n.type,null,o,t,t.mode,a),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,(e.lanes&a)===0){var h=c.memoizedProps;if(n=n.compare,n=n!==null?n:Ur,n(h,o)&&e.ref===t.ref)return qt(e,t,a)}return t.flags|=1,e=fn(c,o),e.ref=t.ref,e.return=t,t.child=e}function jf(e,t,n,o,a){if(e!==null){var c=e.memoizedProps;if(Ur(c,o)&&e.ref===t.ref)if(rt=!1,t.pendingProps=o=c,(e.lanes&a)!==0)(e.flags&131072)!==0&&(rt=!0);else return t.lanes=e.lanes,qt(e,t,a)}return ml(e,t,n,o,a)}function Lf(e,t,n){var o=t.pendingProps,a=o.children,c=e!==null?e.memoizedState:null;if(o.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_e(or,dt),dt|=n;else{if((n&1073741824)===0)return e=c!==null?c.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_e(or,dt),dt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=c!==null?c.baseLanes:n,_e(or,dt),dt|=o}else c!==null?(o=c.baseLanes|n,t.memoizedState=null):o=n,_e(or,dt),dt|=o;return Ge(e,t,a,n),t.child}function If(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ml(e,t,n,o,a){var c=nt(n)?_n:$e.current;return c=Qn(t,c),tr(t,a),n=ol(e,t,n,o,c,a),o=sl(),e!==null&&!rt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,qt(e,t,a)):(Te&&o&&Hs(t),t.flags|=1,Ge(e,t,n,a),t.child)}function bf(e,t,n,o,a){if(nt(n)){var c=!0;Hi(t)}else c=!1;if(tr(t,a),t.stateNode===null)so(e,t),_f(t,n,o),dl(t,n,o,a),o=!0;else if(e===null){var h=t.stateNode,w=t.memoizedProps;h.props=w;var S=h.context,j=n.contextType;typeof j=="object"&&j!==null?j=vt(j):(j=nt(n)?_n:$e.current,j=Qn(t,j));var W=n.getDerivedStateFromProps,q=typeof W=="function"||typeof h.getSnapshotBeforeUpdate=="function";q||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(w!==o||S!==j)&&xf(t,h,o,j),rn=!1;var B=t.memoizedState;h.state=B,Gi(t,o,h,a),S=t.memoizedState,w!==o||B!==S||tt.current||rn?(typeof W=="function"&&(fl(t,n,W,o),S=t.memoizedState),(w=rn||Ef(t,n,w,o,B,S,j))?(q||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(t.flags|=4194308)):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=S),h.props=o,h.state=S,h.context=j,o=w):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{h=t.stateNode,Qc(e,t),w=t.memoizedProps,j=t.type===t.elementType?w:Ct(t.type,w),h.props=j,q=t.pendingProps,B=h.context,S=n.contextType,typeof S=="object"&&S!==null?S=vt(S):(S=nt(n)?_n:$e.current,S=Qn(t,S));var K=n.getDerivedStateFromProps;(W=typeof K=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(w!==q||B!==S)&&xf(t,h,o,S),rn=!1,B=t.memoizedState,h.state=B,Gi(t,o,h,a);var Z=t.memoizedState;w!==q||B!==Z||tt.current||rn?(typeof K=="function"&&(fl(t,n,K,o),Z=t.memoizedState),(j=rn||Ef(t,n,j,o,B,Z,S)||!1)?(W||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(o,Z,S),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(o,Z,S)),typeof h.componentDidUpdate=="function"&&(t.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof h.componentDidUpdate!="function"||w===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=Z),h.props=o,h.state=Z,h.context=S,o=j):(typeof h.componentDidUpdate!="function"||w===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),o=!1)}return vl(e,t,n,o,c,a)}function vl(e,t,n,o,a,c){If(e,t);var h=(t.flags&128)!==0;if(!o&&!h)return a&&Fc(t,n,!1),qt(e,t,c);o=t.stateNode,Cv.current=t;var w=h&&typeof n.getDerivedStateFromError!="function"?null:o.render();return t.flags|=1,e!==null&&h?(t.child=Zn(t,e.child,null,c),t.child=Zn(t,null,w,c)):Ge(e,t,w,c),t.memoizedState=o.state,a&&Fc(t,n,!0),t.child}function Af(e){var t=e.stateNode;t.pendingContext?Uc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Uc(e,t.context,!1),Zs(e,t.containerInfo)}function Uf(e,t,n,o,a){return Yn(),Vs(a),t.flags|=256,Ge(e,t,n,o),t.child}var gl={dehydrated:null,treeContext:null,retryLane:0};function yl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Df(e,t,n){var o=t.pendingProps,a=Pe.current,c=!1,h=(t.flags&128)!==0,w;if((w=h)||(w=e!==null&&e.memoizedState===null?!1:(a&2)!==0),w?(c=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(a|=1),_e(Pe,a&1),e===null)return $s(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(h=o.children,e=o.fallback,c?(o=t.mode,c=t.child,h={mode:"hidden",children:h},(o&1)===0&&c!==null?(c.childLanes=0,c.pendingProps=h):c=wo(h,o,0,null),e=Ln(e,o,n,null),c.return=t,e.return=t,c.sibling=e,t.child=c,t.child.memoizedState=yl(n),t.memoizedState=gl,e):wl(t,h));if(a=e.memoizedState,a!==null&&(w=a.dehydrated,w!==null))return Rv(e,t,h,o,w,a,n);if(c){c=o.fallback,h=t.mode,a=e.child,w=a.sibling;var S={mode:"hidden",children:o.children};return(h&1)===0&&t.child!==a?(o=t.child,o.childLanes=0,o.pendingProps=S,t.deletions=null):(o=fn(a,S),o.subtreeFlags=a.subtreeFlags&14680064),w!==null?c=fn(w,c):(c=Ln(c,h,n,null),c.flags|=2),c.return=t,o.return=t,o.sibling=c,t.child=o,o=c,c=t.child,h=e.child.memoizedState,h=h===null?yl(n):{baseLanes:h.baseLanes|n,cachePool:null,transitions:h.transitions},c.memoizedState=h,c.childLanes=e.childLanes&~n,t.memoizedState=gl,o}return c=e.child,e=c.sibling,o=fn(c,{mode:"visible",children:o.children}),(t.mode&1)===0&&(o.lanes=n),o.return=t,o.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function wl(e,t){return t=wo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function oo(e,t,n,o){return o!==null&&Vs(o),Zn(t,e.child,null,n),e=wl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Rv(e,t,n,o,a,c,h){if(n)return t.flags&256?(t.flags&=-257,o=hl(Error(s(422))),oo(e,t,h,o)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(c=o.fallback,a=t.mode,o=wo({mode:"visible",children:o.children},a,0,null),c=Ln(c,a,h,null),c.flags|=2,o.return=t,c.return=t,o.sibling=c,t.child=o,(t.mode&1)!==0&&Zn(t,e.child,null,h),t.child.memoizedState=yl(h),t.memoizedState=gl,c);if((t.mode&1)===0)return oo(e,t,h,null);if(a.data==="$!"){if(o=a.nextSibling&&a.nextSibling.dataset,o)var w=o.dgst;return o=w,c=Error(s(419)),o=hl(c,o,void 0),oo(e,t,h,o)}if(w=(h&e.childLanes)!==0,rt||w){if(o=ze,o!==null){switch(h&-h){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}a=(a&(o.suspendedLanes|h))!==0?0:a,a!==0&&a!==c.retryLane&&(c.retryLane=a,Ht(e,a),Tt(o,e,a,-1))}return Al(),o=hl(Error(s(421))),oo(e,t,h,o)}return a.data==="$?"?(t.flags|=128,t.child=e.child,t=Mv.bind(null,e),a._reactRetry=t,null):(e=c.treeContext,ft=Zt(a.nextSibling),ct=t,Te=!0,kt=null,e!==null&&(pt[mt++]=Bt,pt[mt++]=zt,pt[mt++]=xn,Bt=e.id,zt=e.overflow,xn=t),t=wl(t,o.children),t.flags|=4096,t)}function Ff(e,t,n){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),Ks(e.return,t,n)}function Sl(e,t,n,o,a){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:n,tailMode:a}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=o,c.tail=n,c.tailMode=a)}function Mf(e,t,n){var o=t.pendingProps,a=o.revealOrder,c=o.tail;if(Ge(e,t,o.children,n),o=Pe.current,(o&2)!==0)o=o&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ff(e,n,t);else if(e.tag===19)Ff(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(_e(Pe,o),(t.mode&1)===0)t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;n!==null;)e=n.alternate,e!==null&&Yi(e)===null&&(a=n),n=n.sibling;n=a,n===null?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Sl(t,!1,a,n,c);break;case"backwards":for(n=null,a=t.child,t.child=null;a!==null;){if(e=a.alternate,e!==null&&Yi(e)===null){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Sl(t,!0,n,null,c);break;case"together":Sl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function so(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tn|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=fn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=fn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Nv(e,t,n){switch(t.tag){case 3:Af(t),Yn();break;case 5:Yc(t);break;case 1:nt(t.type)&&Hi(t);break;case 4:Zs(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,a=t.memoizedProps.value;_e(Ji,o._currentValue),o._currentValue=a;break;case 13:if(o=t.memoizedState,o!==null)return o.dehydrated!==null?(_e(Pe,Pe.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?Df(e,t,n):(_e(Pe,Pe.current&1),e=qt(e,t,n),e!==null?e.sibling:null);_e(Pe,Pe.current&1);break;case 19:if(o=(n&t.childLanes)!==0,(e.flags&128)!==0){if(o)return Mf(e,t,n);t.flags|=128}if(a=t.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),_e(Pe,Pe.current),o)break;return null;case 22:case 23:return t.lanes=0,Lf(e,t,n)}return qt(e,t,n)}var Bf,El,zf,Hf;Bf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},El=function(){},zf=function(e,t,n,o){var a=e.memoizedProps;if(a!==o){e=t.stateNode,Rn(It.current);var c=null;switch(n){case"input":a=Ko(e,a),o=Ko(e,o),c=[];break;case"select":a=Y({},a,{value:void 0}),o=Y({},o,{value:void 0}),c=[];break;case"textarea":a=Zo(e,a),o=Zo(e,o),c=[];break;default:typeof a.onClick!="function"&&typeof o.onClick=="function"&&(e.onclick=Mi)}ts(n,o);var h;n=null;for(j in a)if(!o.hasOwnProperty(j)&&a.hasOwnProperty(j)&&a[j]!=null)if(j==="style"){var w=a[j];for(h in w)w.hasOwnProperty(h)&&(n||(n={}),n[h]="")}else j!=="dangerouslySetInnerHTML"&&j!=="children"&&j!=="suppressContentEditableWarning"&&j!=="suppressHydrationWarning"&&j!=="autoFocus"&&(u.hasOwnProperty(j)?c||(c=[]):(c=c||[]).push(j,null));for(j in o){var S=o[j];if(w=a!=null?a[j]:void 0,o.hasOwnProperty(j)&&S!==w&&(S!=null||w!=null))if(j==="style")if(w){for(h in w)!w.hasOwnProperty(h)||S&&S.hasOwnProperty(h)||(n||(n={}),n[h]="");for(h in S)S.hasOwnProperty(h)&&w[h]!==S[h]&&(n||(n={}),n[h]=S[h])}else n||(c||(c=[]),c.push(j,n)),n=S;else j==="dangerouslySetInnerHTML"?(S=S?S.__html:void 0,w=w?w.__html:void 0,S!=null&&w!==S&&(c=c||[]).push(j,S)):j==="children"?typeof S!="string"&&typeof S!="number"||(c=c||[]).push(j,""+S):j!=="suppressContentEditableWarning"&&j!=="suppressHydrationWarning"&&(u.hasOwnProperty(j)?(S!=null&&j==="onScroll"&&Ce("scroll",e),c||w===S||(c=[])):(c=c||[]).push(j,S))}n&&(c=c||[]).push("style",n);var j=c;(t.updateQueue=j)&&(t.flags|=4)}},Hf=function(e,t,n,o){n!==o&&(t.flags|=4)};function Gr(e,t){if(!Te)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var o=null;n!==null;)n.alternate!==null&&(o=n),n=n.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function Xe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,o=0;if(t)for(var a=e.child;a!==null;)n|=a.lanes|a.childLanes,o|=a.subtreeFlags&14680064,o|=a.flags&14680064,a.return=e,a=a.sibling;else for(a=e.child;a!==null;)n|=a.lanes|a.childLanes,o|=a.subtreeFlags,o|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=o,e.childLanes=n,t}function Tv(e,t,n){var o=t.pendingProps;switch(Ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xe(t),null;case 1:return nt(t.type)&&zi(),Xe(t),null;case 3:return o=t.stateNode,nr(),Re(tt),Re($e),nl(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),(e===null||e.child===null)&&(Vi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,kt!==null&&(Ll(kt),kt=null))),El(e,t),Xe(t),null;case 5:el(t);var a=Rn(Vr.current);if(n=t.type,e!==null&&t.stateNode!=null)zf(e,t,n,o,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(t.stateNode===null)throw Error(s(166));return Xe(t),null}if(e=Rn(It.current),Vi(t)){o=t.stateNode,n=t.type;var c=t.memoizedProps;switch(o[Lt]=t,o[zr]=c,e=(t.mode&1)!==0,n){case"dialog":Ce("cancel",o),Ce("close",o);break;case"iframe":case"object":case"embed":Ce("load",o);break;case"video":case"audio":for(a=0;a<Fr.length;a++)Ce(Fr[a],o);break;case"source":Ce("error",o);break;case"img":case"image":case"link":Ce("error",o),Ce("load",o);break;case"details":Ce("toggle",o);break;case"input":Eu(o,c),Ce("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!c.multiple},Ce("invalid",o);break;case"textarea":ku(o,c),Ce("invalid",o)}ts(n,c),a=null;for(var h in c)if(c.hasOwnProperty(h)){var w=c[h];h==="children"?typeof w=="string"?o.textContent!==w&&(c.suppressHydrationWarning!==!0&&Fi(o.textContent,w,e),a=["children",w]):typeof w=="number"&&o.textContent!==""+w&&(c.suppressHydrationWarning!==!0&&Fi(o.textContent,w,e),a=["children",""+w]):u.hasOwnProperty(h)&&w!=null&&h==="onScroll"&&Ce("scroll",o)}switch(n){case"input":mi(o),xu(o,c,!0);break;case"textarea":mi(o),Ru(o);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(o.onclick=Mi)}o=a,t.updateQueue=o,o!==null&&(t.flags|=4)}else{h=a.nodeType===9?a:a.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Nu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof o.is=="string"?e=h.createElement(n,{is:o.is}):(e=h.createElement(n),n==="select"&&(h=e,o.multiple?h.multiple=!0:o.size&&(h.size=o.size))):e=h.createElementNS(e,n),e[Lt]=t,e[zr]=o,Bf(e,t,!1,!1),t.stateNode=e;e:{switch(h=ns(n,o),n){case"dialog":Ce("cancel",e),Ce("close",e),a=o;break;case"iframe":case"object":case"embed":Ce("load",e),a=o;break;case"video":case"audio":for(a=0;a<Fr.length;a++)Ce(Fr[a],e);a=o;break;case"source":Ce("error",e),a=o;break;case"img":case"image":case"link":Ce("error",e),Ce("load",e),a=o;break;case"details":Ce("toggle",e),a=o;break;case"input":Eu(e,o),a=Ko(e,o),Ce("invalid",e);break;case"option":a=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},a=Y({},o,{value:void 0}),Ce("invalid",e);break;case"textarea":ku(e,o),a=Zo(e,o),Ce("invalid",e);break;default:a=o}ts(n,a),w=a;for(c in w)if(w.hasOwnProperty(c)){var S=w[c];c==="style"?Ou(e,S):c==="dangerouslySetInnerHTML"?(S=S?S.__html:void 0,S!=null&&Tu(e,S)):c==="children"?typeof S=="string"?(n!=="textarea"||S!=="")&&wr(e,S):typeof S=="number"&&wr(e,""+S):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(u.hasOwnProperty(c)?S!=null&&c==="onScroll"&&Ce("scroll",e):S!=null&&L(e,c,S,h))}switch(n){case"input":mi(e),xu(e,o,!1);break;case"textarea":mi(e),Ru(e);break;case"option":o.value!=null&&e.setAttribute("value",""+ye(o.value));break;case"select":e.multiple=!!o.multiple,c=o.value,c!=null?Dn(e,!!o.multiple,c,!1):o.defaultValue!=null&&Dn(e,!!o.multiple,o.defaultValue,!0);break;default:typeof a.onClick=="function"&&(e.onclick=Mi)}switch(n){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Xe(t),null;case 6:if(e&&t.stateNode!=null)Hf(e,t,e.memoizedProps,o);else{if(typeof o!="string"&&t.stateNode===null)throw Error(s(166));if(n=Rn(Vr.current),Rn(It.current),Vi(t)){if(o=t.stateNode,n=t.memoizedProps,o[Lt]=t,(c=o.nodeValue!==n)&&(e=ct,e!==null))switch(e.tag){case 3:Fi(o.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Fi(o.nodeValue,n,(e.mode&1)!==0)}c&&(t.flags|=4)}else o=(n.nodeType===9?n:n.ownerDocument).createTextNode(o),o[Lt]=t,t.stateNode=o}return Xe(t),null;case 13:if(Re(Pe),o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Te&&ft!==null&&(t.mode&1)!==0&&(t.flags&128)===0)qc(),Yn(),t.flags|=98560,c=!1;else if(c=Vi(t),o!==null&&o.dehydrated!==null){if(e===null){if(!c)throw Error(s(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(s(317));c[Lt]=t}else Yn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Xe(t),c=!1}else kt!==null&&(Ll(kt),kt=null),c=!0;if(!c)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(o=o!==null,o!==(e!==null&&e.memoizedState!==null)&&o&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Pe.current&1)!==0?Me===0&&(Me=3):Al())),t.updateQueue!==null&&(t.flags|=4),Xe(t),null);case 4:return nr(),El(e,t),e===null&&Mr(t.stateNode.containerInfo),Xe(t),null;case 10:return Qs(t.type._context),Xe(t),null;case 17:return nt(t.type)&&zi(),Xe(t),null;case 19:if(Re(Pe),c=t.memoizedState,c===null)return Xe(t),null;if(o=(t.flags&128)!==0,h=c.rendering,h===null)if(o)Gr(c,!1);else{if(Me!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(h=Yi(e),h!==null){for(t.flags|=128,Gr(c,!1),o=h.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=n,n=t.child;n!==null;)c=n,e=o,c.flags&=14680066,h=c.alternate,h===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=h.childLanes,c.lanes=h.lanes,c.child=h.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=h.memoizedProps,c.memoizedState=h.memoizedState,c.updateQueue=h.updateQueue,c.type=h.type,e=h.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _e(Pe,Pe.current&1|2),t.child}e=e.sibling}c.tail!==null&&be()>sr&&(t.flags|=128,o=!0,Gr(c,!1),t.lanes=4194304)}else{if(!o)if(e=Yi(h),e!==null){if(t.flags|=128,o=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Gr(c,!0),c.tail===null&&c.tailMode==="hidden"&&!h.alternate&&!Te)return Xe(t),null}else 2*be()-c.renderingStartTime>sr&&n!==1073741824&&(t.flags|=128,o=!0,Gr(c,!1),t.lanes=4194304);c.isBackwards?(h.sibling=t.child,t.child=h):(n=c.last,n!==null?n.sibling=h:t.child=h,c.last=h)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=be(),t.sibling=null,n=Pe.current,_e(Pe,o?n&1|2:n&1),t):(Xe(t),null);case 22:case 23:return bl(),o=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==o&&(t.flags|=8192),o&&(t.mode&1)!==0?(dt&1073741824)!==0&&(Xe(t),t.subtreeFlags&6&&(t.flags|=8192)):Xe(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function Pv(e,t){switch(Ws(t),t.tag){case 1:return nt(t.type)&&zi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return nr(),Re(tt),Re($e),nl(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return el(t),null;case 13:if(Re(Pe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Yn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Re(Pe),null;case 4:return nr(),null;case 10:return Qs(t.type._context),null;case 22:case 23:return bl(),null;case 24:return null;default:return null}}var lo=!1,Je=!1,Ov=typeof WeakSet=="function"?WeakSet:Set,G=null;function ir(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(o){Ie(e,t,o)}else n.current=null}function _l(e,t,n){try{n()}catch(o){Ie(e,t,o)}}var Wf=!1;function jv(e,t){if(bs=Ni,e=Sc(),Rs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var o=n.getSelection&&n.getSelection();if(o&&o.rangeCount!==0){n=o.anchorNode;var a=o.anchorOffset,c=o.focusNode;o=o.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var h=0,w=-1,S=-1,j=0,W=0,q=e,B=null;t:for(;;){for(var K;q!==n||a!==0&&q.nodeType!==3||(w=h+a),q!==c||o!==0&&q.nodeType!==3||(S=h+o),q.nodeType===3&&(h+=q.nodeValue.length),(K=q.firstChild)!==null;)B=q,q=K;for(;;){if(q===e)break t;if(B===n&&++j===a&&(w=h),B===c&&++W===o&&(S=h),(K=q.nextSibling)!==null)break;q=B,B=q.parentNode}q=K}n=w===-1||S===-1?null:{start:w,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(As={focusedElem:e,selectionRange:n},Ni=!1,G=t;G!==null;)if(t=G,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,G=e;else for(;G!==null;){t=G;try{var Z=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(Z!==null){var ee=Z.memoizedProps,Ae=Z.memoizedState,C=t.stateNode,E=C.getSnapshotBeforeUpdate(t.elementType===t.type?ee:Ct(t.type,ee),Ae);C.__reactInternalSnapshotBeforeUpdate=E}break;case 3:var O=t.stateNode.containerInfo;O.nodeType===1?O.textContent="":O.nodeType===9&&O.documentElement&&O.removeChild(O.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch($){Ie(t,t.return,$)}if(e=t.sibling,e!==null){e.return=t.return,G=e;break}G=t.return}return Z=Wf,Wf=!1,Z}function Yr(e,t,n){var o=t.updateQueue;if(o=o!==null?o.lastEffect:null,o!==null){var a=o=o.next;do{if((a.tag&e)===e){var c=a.destroy;a.destroy=void 0,c!==void 0&&_l(t,n,c)}a=a.next}while(a!==o)}}function ao(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var o=n.create;n.destroy=o()}n=n.next}while(n!==t)}}function xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function qf(e){var t=e.alternate;t!==null&&(e.alternate=null,qf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Lt],delete t[zr],delete t[Ms],delete t[hv],delete t[pv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function $f(e){return e.tag===5||e.tag===3||e.tag===4}function Vf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||$f(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function kl(e,t,n){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Mi));else if(o!==4&&(e=e.child,e!==null))for(kl(e,t,n),e=e.sibling;e!==null;)kl(e,t,n),e=e.sibling}function Cl(e,t,n){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(o!==4&&(e=e.child,e!==null))for(Cl(e,t,n),e=e.sibling;e!==null;)Cl(e,t,n),e=e.sibling}var We=null,Rt=!1;function sn(e,t,n){for(n=n.child;n!==null;)Xf(e,t,n),n=n.sibling}function Xf(e,t,n){if(jt&&typeof jt.onCommitFiberUnmount=="function")try{jt.onCommitFiberUnmount(Ei,n)}catch{}switch(n.tag){case 5:Je||ir(n,t);case 6:var o=We,a=Rt;We=null,sn(e,t,n),We=o,Rt=a,We!==null&&(Rt?(e=We,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):We.removeChild(n.stateNode));break;case 18:We!==null&&(Rt?(e=We,n=n.stateNode,e.nodeType===8?Fs(e.parentNode,n):e.nodeType===1&&Fs(e,n),Or(e)):Fs(We,n.stateNode));break;case 4:o=We,a=Rt,We=n.stateNode.containerInfo,Rt=!0,sn(e,t,n),We=o,Rt=a;break;case 0:case 11:case 14:case 15:if(!Je&&(o=n.updateQueue,o!==null&&(o=o.lastEffect,o!==null))){a=o=o.next;do{var c=a,h=c.destroy;c=c.tag,h!==void 0&&((c&2)!==0||(c&4)!==0)&&_l(n,t,h),a=a.next}while(a!==o)}sn(e,t,n);break;case 1:if(!Je&&(ir(n,t),o=n.stateNode,typeof o.componentWillUnmount=="function"))try{o.props=n.memoizedProps,o.state=n.memoizedState,o.componentWillUnmount()}catch(w){Ie(n,t,w)}sn(e,t,n);break;case 21:sn(e,t,n);break;case 22:n.mode&1?(Je=(o=Je)||n.memoizedState!==null,sn(e,t,n),Je=o):sn(e,t,n);break;default:sn(e,t,n)}}function Jf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ov),t.forEach(function(o){var a=Bv.bind(null,e,o);n.has(o)||(n.add(o),o.then(a,a))})}}function Nt(e,t){var n=t.deletions;if(n!==null)for(var o=0;o<n.length;o++){var a=n[o];try{var c=e,h=t,w=h;e:for(;w!==null;){switch(w.tag){case 5:We=w.stateNode,Rt=!1;break e;case 3:We=w.stateNode.containerInfo,Rt=!0;break e;case 4:We=w.stateNode.containerInfo,Rt=!0;break e}w=w.return}if(We===null)throw Error(s(160));Xf(c,h,a),We=null,Rt=!1;var S=a.alternate;S!==null&&(S.return=null),a.return=null}catch(j){Ie(a,t,j)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Qf(t,e),t=t.sibling}function Qf(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Nt(t,e),At(e),o&4){try{Yr(3,e,e.return),ao(3,e)}catch(ee){Ie(e,e.return,ee)}try{Yr(5,e,e.return)}catch(ee){Ie(e,e.return,ee)}}break;case 1:Nt(t,e),At(e),o&512&&n!==null&&ir(n,n.return);break;case 5:if(Nt(t,e),At(e),o&512&&n!==null&&ir(n,n.return),e.flags&32){var a=e.stateNode;try{wr(a,"")}catch(ee){Ie(e,e.return,ee)}}if(o&4&&(a=e.stateNode,a!=null)){var c=e.memoizedProps,h=n!==null?n.memoizedProps:c,w=e.type,S=e.updateQueue;if(e.updateQueue=null,S!==null)try{w==="input"&&c.type==="radio"&&c.name!=null&&_u(a,c),ns(w,h);var j=ns(w,c);for(h=0;h<S.length;h+=2){var W=S[h],q=S[h+1];W==="style"?Ou(a,q):W==="dangerouslySetInnerHTML"?Tu(a,q):W==="children"?wr(a,q):L(a,W,q,j)}switch(w){case"input":Go(a,c);break;case"textarea":Cu(a,c);break;case"select":var B=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!c.multiple;var K=c.value;K!=null?Dn(a,!!c.multiple,K,!1):B!==!!c.multiple&&(c.defaultValue!=null?Dn(a,!!c.multiple,c.defaultValue,!0):Dn(a,!!c.multiple,c.multiple?[]:"",!1))}a[zr]=c}catch(ee){Ie(e,e.return,ee)}}break;case 6:if(Nt(t,e),At(e),o&4){if(e.stateNode===null)throw Error(s(162));a=e.stateNode,c=e.memoizedProps;try{a.nodeValue=c}catch(ee){Ie(e,e.return,ee)}}break;case 3:if(Nt(t,e),At(e),o&4&&n!==null&&n.memoizedState.isDehydrated)try{Or(t.containerInfo)}catch(ee){Ie(e,e.return,ee)}break;case 4:Nt(t,e),At(e);break;case 13:Nt(t,e),At(e),a=e.child,a.flags&8192&&(c=a.memoizedState!==null,a.stateNode.isHidden=c,!c||a.alternate!==null&&a.alternate.memoizedState!==null||(Tl=be())),o&4&&Jf(e);break;case 22:if(W=n!==null&&n.memoizedState!==null,e.mode&1?(Je=(j=Je)||W,Nt(t,e),Je=j):Nt(t,e),At(e),o&8192){if(j=e.memoizedState!==null,(e.stateNode.isHidden=j)&&!W&&(e.mode&1)!==0)for(G=e,W=e.child;W!==null;){for(q=G=W;G!==null;){switch(B=G,K=B.child,B.tag){case 0:case 11:case 14:case 15:Yr(4,B,B.return);break;case 1:ir(B,B.return);var Z=B.stateNode;if(typeof Z.componentWillUnmount=="function"){o=B,n=B.return;try{t=o,Z.props=t.memoizedProps,Z.state=t.memoizedState,Z.componentWillUnmount()}catch(ee){Ie(o,n,ee)}}break;case 5:ir(B,B.return);break;case 22:if(B.memoizedState!==null){Yf(q);continue}}K!==null?(K.return=B,G=K):Yf(q)}W=W.sibling}e:for(W=null,q=e;;){if(q.tag===5){if(W===null){W=q;try{a=q.stateNode,j?(c=a.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(w=q.stateNode,S=q.memoizedProps.style,h=S!=null&&S.hasOwnProperty("display")?S.display:null,w.style.display=Pu("display",h))}catch(ee){Ie(e,e.return,ee)}}}else if(q.tag===6){if(W===null)try{q.stateNode.nodeValue=j?"":q.memoizedProps}catch(ee){Ie(e,e.return,ee)}}else if((q.tag!==22&&q.tag!==23||q.memoizedState===null||q===e)&&q.child!==null){q.child.return=q,q=q.child;continue}if(q===e)break e;for(;q.sibling===null;){if(q.return===null||q.return===e)break e;W===q&&(W=null),q=q.return}W===q&&(W=null),q.sibling.return=q.return,q=q.sibling}}break;case 19:Nt(t,e),At(e),o&4&&Jf(e);break;case 21:break;default:Nt(t,e),At(e)}}function At(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if($f(n)){var o=n;break e}n=n.return}throw Error(s(160))}switch(o.tag){case 5:var a=o.stateNode;o.flags&32&&(wr(a,""),o.flags&=-33);var c=Vf(e);Cl(e,c,a);break;case 3:case 4:var h=o.stateNode.containerInfo,w=Vf(e);kl(e,w,h);break;default:throw Error(s(161))}}catch(S){Ie(e,e.return,S)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Lv(e,t,n){G=e,Kf(e)}function Kf(e,t,n){for(var o=(e.mode&1)!==0;G!==null;){var a=G,c=a.child;if(a.tag===22&&o){var h=a.memoizedState!==null||lo;if(!h){var w=a.alternate,S=w!==null&&w.memoizedState!==null||Je;w=lo;var j=Je;if(lo=h,(Je=S)&&!j)for(G=a;G!==null;)h=G,S=h.child,h.tag===22&&h.memoizedState!==null?Zf(a):S!==null?(S.return=h,G=S):Zf(a);for(;c!==null;)G=c,Kf(c),c=c.sibling;G=a,lo=w,Je=j}Gf(e)}else(a.subtreeFlags&8772)!==0&&c!==null?(c.return=a,G=c):Gf(e)}}function Gf(e){for(;G!==null;){var t=G;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Je||ao(5,t);break;case 1:var o=t.stateNode;if(t.flags&4&&!Je)if(n===null)o.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:Ct(t.type,n.memoizedProps);o.componentDidUpdate(a,n.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var c=t.updateQueue;c!==null&&Gc(t,c,o);break;case 3:var h=t.updateQueue;if(h!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Gc(t,h,n)}break;case 5:var w=t.stateNode;if(n===null&&t.flags&4){n=w;var S=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":S.autoFocus&&n.focus();break;case"img":S.src&&(n.src=S.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var j=t.alternate;if(j!==null){var W=j.memoizedState;if(W!==null){var q=W.dehydrated;q!==null&&Or(q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}Je||t.flags&512&&xl(t)}catch(B){Ie(t,t.return,B)}}if(t===e){G=null;break}if(n=t.sibling,n!==null){n.return=t.return,G=n;break}G=t.return}}function Yf(e){for(;G!==null;){var t=G;if(t===e){G=null;break}var n=t.sibling;if(n!==null){n.return=t.return,G=n;break}G=t.return}}function Zf(e){for(;G!==null;){var t=G;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ao(4,t)}catch(S){Ie(t,n,S)}break;case 1:var o=t.stateNode;if(typeof o.componentDidMount=="function"){var a=t.return;try{o.componentDidMount()}catch(S){Ie(t,a,S)}}var c=t.return;try{xl(t)}catch(S){Ie(t,c,S)}break;case 5:var h=t.return;try{xl(t)}catch(S){Ie(t,h,S)}}}catch(S){Ie(t,t.return,S)}if(t===e){G=null;break}var w=t.sibling;if(w!==null){w.return=t.return,G=w;break}G=t.return}}var Iv=Math.ceil,uo=R.ReactCurrentDispatcher,Rl=R.ReactCurrentOwner,yt=R.ReactCurrentBatchConfig,de=0,ze=null,De=null,qe=0,dt=0,or=en(0),Me=0,Zr=null,Tn=0,co=0,Nl=0,ei=null,it=null,Tl=0,sr=1/0,$t=null,fo=!1,Pl=null,ln=null,ho=!1,an=null,po=0,ti=0,Ol=null,mo=-1,vo=0;function Ye(){return(de&6)!==0?be():mo!==-1?mo:mo=be()}function un(e){return(e.mode&1)===0?1:(de&2)!==0&&qe!==0?qe&-qe:vv.transition!==null?(vo===0&&(vo=$u()),vo):(e=we,e!==0||(e=window.event,e=e===void 0?16:ec(e.type)),e)}function Tt(e,t,n,o){if(50<ti)throw ti=0,Ol=null,Error(s(185));Cr(e,n,o),((de&2)===0||e!==ze)&&(e===ze&&((de&2)===0&&(co|=n),Me===4&&cn(e,qe)),ot(e,o),n===1&&de===0&&(t.mode&1)===0&&(sr=be()+500,Wi&&nn()))}function ot(e,t){var n=e.callbackNode;vm(e,t);var o=ki(e,e===ze?qe:0);if(o===0)n!==null&&Hu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(n!=null&&Hu(n),t===1)e.tag===0?mv(td.bind(null,e)):Mc(td.bind(null,e)),fv(function(){(de&6)===0&&nn()}),n=null;else{switch(Vu(o)){case 1:n=us;break;case 4:n=Wu;break;case 16:n=Si;break;case 536870912:n=qu;break;default:n=Si}n=ud(n,ed.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ed(e,t){if(mo=-1,vo=0,(de&6)!==0)throw Error(s(327));var n=e.callbackNode;if(lr()&&e.callbackNode!==n)return null;var o=ki(e,e===ze?qe:0);if(o===0)return null;if((o&30)!==0||(o&e.expiredLanes)!==0||t)t=go(e,o);else{t=o;var a=de;de|=2;var c=rd();(ze!==e||qe!==t)&&($t=null,sr=be()+500,On(e,t));do try{Uv();break}catch(w){nd(e,w)}while(!0);Js(),uo.current=c,de=a,De!==null?t=0:(ze=null,qe=0,t=Me)}if(t!==0){if(t===2&&(a=cs(e),a!==0&&(o=a,t=jl(e,a))),t===1)throw n=Zr,On(e,0),cn(e,o),ot(e,be()),n;if(t===6)cn(e,o);else{if(a=e.current.alternate,(o&30)===0&&!bv(a)&&(t=go(e,o),t===2&&(c=cs(e),c!==0&&(o=c,t=jl(e,c))),t===1))throw n=Zr,On(e,0),cn(e,o),ot(e,be()),n;switch(e.finishedWork=a,e.finishedLanes=o,t){case 0:case 1:throw Error(s(345));case 2:jn(e,it,$t);break;case 3:if(cn(e,o),(o&130023424)===o&&(t=Tl+500-be(),10<t)){if(ki(e,0)!==0)break;if(a=e.suspendedLanes,(a&o)!==o){Ye(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=Ds(jn.bind(null,e,it,$t),t);break}jn(e,it,$t);break;case 4:if(cn(e,o),(o&4194240)===o)break;for(t=e.eventTimes,a=-1;0<o;){var h=31-_t(o);c=1<<h,h=t[h],h>a&&(a=h),o&=~c}if(o=a,o=be()-o,o=(120>o?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*Iv(o/1960))-o,10<o){e.timeoutHandle=Ds(jn.bind(null,e,it,$t),o);break}jn(e,it,$t);break;case 5:jn(e,it,$t);break;default:throw Error(s(329))}}}return ot(e,be()),e.callbackNode===n?ed.bind(null,e):null}function jl(e,t){var n=ei;return e.current.memoizedState.isDehydrated&&(On(e,t).flags|=256),e=go(e,t),e!==2&&(t=it,it=n,t!==null&&Ll(t)),e}function Ll(e){it===null?it=e:it.push.apply(it,e)}function bv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var o=0;o<n.length;o++){var a=n[o],c=a.getSnapshot;a=a.value;try{if(!xt(c(),a))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function cn(e,t){for(t&=~Nl,t&=~co,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-_t(t),o=1<<n;e[n]=-1,t&=~o}}function td(e){if((de&6)!==0)throw Error(s(327));lr();var t=ki(e,0);if((t&1)===0)return ot(e,be()),null;var n=go(e,t);if(e.tag!==0&&n===2){var o=cs(e);o!==0&&(t=o,n=jl(e,o))}if(n===1)throw n=Zr,On(e,0),cn(e,t),ot(e,be()),n;if(n===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jn(e,it,$t),ot(e,be()),null}function Il(e,t){var n=de;de|=1;try{return e(t)}finally{de=n,de===0&&(sr=be()+500,Wi&&nn())}}function Pn(e){an!==null&&an.tag===0&&(de&6)===0&&lr();var t=de;de|=1;var n=yt.transition,o=we;try{if(yt.transition=null,we=1,e)return e()}finally{we=o,yt.transition=n,de=t,(de&6)===0&&nn()}}function bl(){dt=or.current,Re(or)}function On(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,cv(n)),De!==null)for(n=De.return;n!==null;){var o=n;switch(Ws(o),o.tag){case 1:o=o.type.childContextTypes,o!=null&&zi();break;case 3:nr(),Re(tt),Re($e),nl();break;case 5:el(o);break;case 4:nr();break;case 13:Re(Pe);break;case 19:Re(Pe);break;case 10:Qs(o.type._context);break;case 22:case 23:bl()}n=n.return}if(ze=e,De=e=fn(e.current,null),qe=dt=t,Me=0,Zr=null,Nl=co=Tn=0,it=ei=null,Cn!==null){for(t=0;t<Cn.length;t++)if(n=Cn[t],o=n.interleaved,o!==null){n.interleaved=null;var a=o.next,c=n.pending;if(c!==null){var h=c.next;c.next=a,o.next=h}n.pending=o}Cn=null}return e}function nd(e,t){do{var n=De;try{if(Js(),Zi.current=ro,eo){for(var o=Oe.memoizedState;o!==null;){var a=o.queue;a!==null&&(a.pending=null),o=o.next}eo=!1}if(Nn=0,Be=Fe=Oe=null,Xr=!1,Jr=0,Rl.current=null,n===null||n.return===null){Me=1,Zr=t,De=null;break}e:{var c=e,h=n.return,w=n,S=t;if(t=qe,w.flags|=32768,S!==null&&typeof S=="object"&&typeof S.then=="function"){var j=S,W=w,q=W.tag;if((W.mode&1)===0&&(q===0||q===11||q===15)){var B=W.alternate;B?(W.updateQueue=B.updateQueue,W.memoizedState=B.memoizedState,W.lanes=B.lanes):(W.updateQueue=null,W.memoizedState=null)}var K=Nf(h);if(K!==null){K.flags&=-257,Tf(K,h,w,c,t),K.mode&1&&Rf(c,j,t),t=K,S=j;var Z=t.updateQueue;if(Z===null){var ee=new Set;ee.add(S),t.updateQueue=ee}else Z.add(S);break e}else{if((t&1)===0){Rf(c,j,t),Al();break e}S=Error(s(426))}}else if(Te&&w.mode&1){var Ae=Nf(h);if(Ae!==null){(Ae.flags&65536)===0&&(Ae.flags|=256),Tf(Ae,h,w,c,t),Vs(rr(S,w));break e}}c=S=rr(S,w),Me!==4&&(Me=2),ei===null?ei=[c]:ei.push(c),c=h;do{switch(c.tag){case 3:c.flags|=65536,t&=-t,c.lanes|=t;var C=kf(c,S,t);Kc(c,C);break e;case 1:w=S;var E=c.type,O=c.stateNode;if((c.flags&128)===0&&(typeof E.getDerivedStateFromError=="function"||O!==null&&typeof O.componentDidCatch=="function"&&(ln===null||!ln.has(O)))){c.flags|=65536,t&=-t,c.lanes|=t;var $=Cf(c,w,t);Kc(c,$);break e}}c=c.return}while(c!==null)}od(n)}catch(te){t=te,De===n&&n!==null&&(De=n=n.return);continue}break}while(!0)}function rd(){var e=uo.current;return uo.current=ro,e===null?ro:e}function Al(){(Me===0||Me===3||Me===2)&&(Me=4),ze===null||(Tn&268435455)===0&&(co&268435455)===0||cn(ze,qe)}function go(e,t){var n=de;de|=2;var o=rd();(ze!==e||qe!==t)&&($t=null,On(e,t));do try{Av();break}catch(a){nd(e,a)}while(!0);if(Js(),de=n,uo.current=o,De!==null)throw Error(s(261));return ze=null,qe=0,Me}function Av(){for(;De!==null;)id(De)}function Uv(){for(;De!==null&&!lm();)id(De)}function id(e){var t=ad(e.alternate,e,dt);e.memoizedProps=e.pendingProps,t===null?od(e):De=t,Rl.current=null}function od(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=Tv(n,t,dt),n!==null){De=n;return}}else{if(n=Pv(n,t),n!==null){n.flags&=32767,De=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Me=6,De=null;return}}if(t=t.sibling,t!==null){De=t;return}De=t=e}while(t!==null);Me===0&&(Me=5)}function jn(e,t,n){var o=we,a=yt.transition;try{yt.transition=null,we=1,Dv(e,t,n,o)}finally{yt.transition=a,we=o}return null}function Dv(e,t,n,o){do lr();while(an!==null);if((de&6)!==0)throw Error(s(327));n=e.finishedWork;var a=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var c=n.lanes|n.childLanes;if(gm(e,c),e===ze&&(De=ze=null,qe=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||ho||(ho=!0,ud(Si,function(){return lr(),null})),c=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||c){c=yt.transition,yt.transition=null;var h=we;we=1;var w=de;de|=4,Rl.current=null,jv(e,n),Qf(n,e),rv(As),Ni=!!bs,As=bs=null,e.current=n,Lv(n),am(),de=w,we=h,yt.transition=c}else e.current=n;if(ho&&(ho=!1,an=e,po=a),c=e.pendingLanes,c===0&&(ln=null),fm(n.stateNode),ot(e,be()),t!==null)for(o=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],o(a.value,{componentStack:a.stack,digest:a.digest});if(fo)throw fo=!1,e=Pl,Pl=null,e;return(po&1)!==0&&e.tag!==0&&lr(),c=e.pendingLanes,(c&1)!==0?e===Ol?ti++:(ti=0,Ol=e):ti=0,nn(),null}function lr(){if(an!==null){var e=Vu(po),t=yt.transition,n=we;try{if(yt.transition=null,we=16>e?16:e,an===null)var o=!1;else{if(e=an,an=null,po=0,(de&6)!==0)throw Error(s(331));var a=de;for(de|=4,G=e.current;G!==null;){var c=G,h=c.child;if((G.flags&16)!==0){var w=c.deletions;if(w!==null){for(var S=0;S<w.length;S++){var j=w[S];for(G=j;G!==null;){var W=G;switch(W.tag){case 0:case 11:case 15:Yr(8,W,c)}var q=W.child;if(q!==null)q.return=W,G=q;else for(;G!==null;){W=G;var B=W.sibling,K=W.return;if(qf(W),W===j){G=null;break}if(B!==null){B.return=K,G=B;break}G=K}}}var Z=c.alternate;if(Z!==null){var ee=Z.child;if(ee!==null){Z.child=null;do{var Ae=ee.sibling;ee.sibling=null,ee=Ae}while(ee!==null)}}G=c}}if((c.subtreeFlags&2064)!==0&&h!==null)h.return=c,G=h;else e:for(;G!==null;){if(c=G,(c.flags&2048)!==0)switch(c.tag){case 0:case 11:case 15:Yr(9,c,c.return)}var C=c.sibling;if(C!==null){C.return=c.return,G=C;break e}G=c.return}}var E=e.current;for(G=E;G!==null;){h=G;var O=h.child;if((h.subtreeFlags&2064)!==0&&O!==null)O.return=h,G=O;else e:for(h=E;G!==null;){if(w=G,(w.flags&2048)!==0)try{switch(w.tag){case 0:case 11:case 15:ao(9,w)}}catch(te){Ie(w,w.return,te)}if(w===h){G=null;break e}var $=w.sibling;if($!==null){$.return=w.return,G=$;break e}G=w.return}}if(de=a,nn(),jt&&typeof jt.onPostCommitFiberRoot=="function")try{jt.onPostCommitFiberRoot(Ei,e)}catch{}o=!0}return o}finally{we=n,yt.transition=t}}return!1}function sd(e,t,n){t=rr(n,t),t=kf(e,t,1),e=on(e,t,1),t=Ye(),e!==null&&(Cr(e,1,t),ot(e,t))}function Ie(e,t,n){if(e.tag===3)sd(e,e,n);else for(;t!==null;){if(t.tag===3){sd(t,e,n);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(ln===null||!ln.has(o))){e=rr(n,e),e=Cf(t,e,1),t=on(t,e,1),e=Ye(),t!==null&&(Cr(t,1,e),ot(t,e));break}}t=t.return}}function Fv(e,t,n){var o=e.pingCache;o!==null&&o.delete(t),t=Ye(),e.pingedLanes|=e.suspendedLanes&n,ze===e&&(qe&n)===n&&(Me===4||Me===3&&(qe&130023424)===qe&&500>be()-Tl?On(e,0):Nl|=n),ot(e,t)}function ld(e,t){t===0&&((e.mode&1)===0?t=1:(t=xi,xi<<=1,(xi&130023424)===0&&(xi=4194304)));var n=Ye();e=Ht(e,t),e!==null&&(Cr(e,t,n),ot(e,n))}function Mv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ld(e,n)}function Bv(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,a=e.memoizedState;a!==null&&(n=a.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(s(314))}o!==null&&o.delete(t),ld(e,n)}var ad;ad=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||tt.current)rt=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return rt=!1,Nv(e,t,n);rt=(e.flags&131072)!==0}else rt=!1,Te&&(t.flags&1048576)!==0&&Bc(t,$i,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;so(e,t),e=t.pendingProps;var a=Qn(t,$e.current);tr(t,n),a=ol(null,t,o,e,a,n);var c=sl();return t.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,nt(o)?(c=!0,Hi(t)):c=!1,t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,Ys(t),a.updater=io,t.stateNode=a,a._reactInternals=t,dl(t,o,e,n),t=vl(null,t,o,!0,c,n)):(t.tag=0,Te&&c&&Hs(t),Ge(null,t,a,n),t=t.child),t;case 16:o=t.elementType;e:{switch(so(e,t),e=t.pendingProps,a=o._init,o=a(o._payload),t.type=o,a=t.tag=Hv(o),e=Ct(o,e),a){case 0:t=ml(null,t,o,e,n);break e;case 1:t=bf(null,t,o,e,n);break e;case 11:t=Pf(null,t,o,e,n);break e;case 14:t=Of(null,t,o,Ct(o.type,e),n);break e}throw Error(s(306,o,""))}return t;case 0:return o=t.type,a=t.pendingProps,a=t.elementType===o?a:Ct(o,a),ml(e,t,o,a,n);case 1:return o=t.type,a=t.pendingProps,a=t.elementType===o?a:Ct(o,a),bf(e,t,o,a,n);case 3:e:{if(Af(t),e===null)throw Error(s(387));o=t.pendingProps,c=t.memoizedState,a=c.element,Qc(e,t),Gi(t,o,null,n);var h=t.memoizedState;if(o=h.element,c.isDehydrated)if(c={element:o,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){a=rr(Error(s(423)),t),t=Uf(e,t,o,n,a);break e}else if(o!==a){a=rr(Error(s(424)),t),t=Uf(e,t,o,n,a);break e}else for(ft=Zt(t.stateNode.containerInfo.firstChild),ct=t,Te=!0,kt=null,n=Xc(t,null,o,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Yn(),o===a){t=qt(e,t,n);break e}Ge(e,t,o,n)}t=t.child}return t;case 5:return Yc(t),e===null&&$s(t),o=t.type,a=t.pendingProps,c=e!==null?e.memoizedProps:null,h=a.children,Us(o,a)?h=null:c!==null&&Us(o,c)&&(t.flags|=32),If(e,t),Ge(e,t,h,n),t.child;case 6:return e===null&&$s(t),null;case 13:return Df(e,t,n);case 4:return Zs(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=Zn(t,null,o,n):Ge(e,t,o,n),t.child;case 11:return o=t.type,a=t.pendingProps,a=t.elementType===o?a:Ct(o,a),Pf(e,t,o,a,n);case 7:return Ge(e,t,t.pendingProps,n),t.child;case 8:return Ge(e,t,t.pendingProps.children,n),t.child;case 12:return Ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(o=t.type._context,a=t.pendingProps,c=t.memoizedProps,h=a.value,_e(Ji,o._currentValue),o._currentValue=h,c!==null)if(xt(c.value,h)){if(c.children===a.children&&!tt.current){t=qt(e,t,n);break e}}else for(c=t.child,c!==null&&(c.return=t);c!==null;){var w=c.dependencies;if(w!==null){h=c.child;for(var S=w.firstContext;S!==null;){if(S.context===o){if(c.tag===1){S=Wt(-1,n&-n),S.tag=2;var j=c.updateQueue;if(j!==null){j=j.shared;var W=j.pending;W===null?S.next=S:(S.next=W.next,W.next=S),j.pending=S}}c.lanes|=n,S=c.alternate,S!==null&&(S.lanes|=n),Ks(c.return,n,t),w.lanes|=n;break}S=S.next}}else if(c.tag===10)h=c.type===t.type?null:c.child;else if(c.tag===18){if(h=c.return,h===null)throw Error(s(341));h.lanes|=n,w=h.alternate,w!==null&&(w.lanes|=n),Ks(h,n,t),h=c.sibling}else h=c.child;if(h!==null)h.return=c;else for(h=c;h!==null;){if(h===t){h=null;break}if(c=h.sibling,c!==null){c.return=h.return,h=c;break}h=h.return}c=h}Ge(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,o=t.pendingProps.children,tr(t,n),a=vt(a),o=o(a),t.flags|=1,Ge(e,t,o,n),t.child;case 14:return o=t.type,a=Ct(o,t.pendingProps),a=Ct(o.type,a),Of(e,t,o,a,n);case 15:return jf(e,t,t.type,t.pendingProps,n);case 17:return o=t.type,a=t.pendingProps,a=t.elementType===o?a:Ct(o,a),so(e,t),t.tag=1,nt(o)?(e=!0,Hi(t)):e=!1,tr(t,n),_f(t,o,a),dl(t,o,a,n),vl(null,t,o,!0,e,n);case 19:return Mf(e,t,n);case 22:return Lf(e,t,n)}throw Error(s(156,t.tag))};function ud(e,t){return zu(e,t)}function zv(e,t,n,o){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wt(e,t,n,o){return new zv(e,t,n,o)}function Ul(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Hv(e){if(typeof e=="function")return Ul(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ge)return 11;if(e===Se)return 14}return 2}function fn(e,t){var n=e.alternate;return n===null?(n=wt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function yo(e,t,n,o,a,c){var h=2;if(o=e,typeof e=="function")Ul(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case z:return Ln(n.children,a,c,t);case b:h=8,a|=8;break;case V:return e=wt(12,n,t,a|2),e.elementType=V,e.lanes=c,e;case X:return e=wt(13,n,t,a),e.elementType=X,e.lanes=c,e;case je:return e=wt(19,n,t,a),e.elementType=je,e.lanes=c,e;case ke:return wo(n,a,c,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ne:h=10;break e;case ae:h=9;break e;case ge:h=11;break e;case Se:h=14;break e;case Le:h=16,o=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=wt(h,n,t,a),t.elementType=e,t.type=o,t.lanes=c,t}function Ln(e,t,n,o){return e=wt(7,e,o,t),e.lanes=n,e}function wo(e,t,n,o){return e=wt(22,e,o,t),e.elementType=ke,e.lanes=n,e.stateNode={isHidden:!1},e}function Dl(e,t,n){return e=wt(6,e,null,t),e.lanes=n,e}function Fl(e,t,n){return t=wt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Wv(e,t,n,o,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=fs(0),this.expirationTimes=fs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=fs(0),this.identifierPrefix=o,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Ml(e,t,n,o,a,c,h,w,S){return e=new Wv(e,t,n,w,S),t===1?(t=1,c===!0&&(t|=8)):t=0,c=wt(3,null,null,t),e.current=c,c.stateNode=e,c.memoizedState={element:o,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ys(c),e}function qv(e,t,n){var o=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:J,key:o==null?null:""+o,children:e,containerInfo:t,implementation:n}}function cd(e){if(!e)return tn;e=e._reactInternals;e:{if(Sn(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(nt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var n=e.type;if(nt(n))return Dc(e,n,t)}return t}function fd(e,t,n,o,a,c,h,w,S){return e=Ml(n,o,!0,e,a,c,h,w,S),e.context=cd(null),n=e.current,o=Ye(),a=un(n),c=Wt(o,a),c.callback=t??null,on(n,c,a),e.current.lanes=a,Cr(e,a,o),ot(e,o),e}function So(e,t,n,o){var a=t.current,c=Ye(),h=un(a);return n=cd(n),t.context===null?t.context=n:t.pendingContext=n,t=Wt(c,h),t.payload={element:e},o=o===void 0?null:o,o!==null&&(t.callback=o),e=on(a,t,h),e!==null&&(Tt(e,a,h,c),Ki(e,a,h)),h}function Eo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function dd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bl(e,t){dd(e,t),(e=e.alternate)&&dd(e,t)}function $v(){return null}var hd=typeof reportError=="function"?reportError:function(e){console.error(e)};function zl(e){this._internalRoot=e}_o.prototype.render=zl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));So(e,t,null,null)},_o.prototype.unmount=zl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Pn(function(){So(null,e,null,null)}),t[Ft]=null}};function _o(e){this._internalRoot=e}_o.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Kt.length&&t!==0&&t<Kt[n].priority;n++);Kt.splice(n,0,e),n===0&&Yu(e)}};function Hl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function xo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function pd(){}function Vv(e,t,n,o,a){if(a){if(typeof o=="function"){var c=o;o=function(){var j=Eo(h);c.call(j)}}var h=fd(t,o,e,0,null,!1,!1,"",pd);return e._reactRootContainer=h,e[Ft]=h.current,Mr(e.nodeType===8?e.parentNode:e),Pn(),h}for(;a=e.lastChild;)e.removeChild(a);if(typeof o=="function"){var w=o;o=function(){var j=Eo(S);w.call(j)}}var S=Ml(e,0,!1,null,null,!1,!1,"",pd);return e._reactRootContainer=S,e[Ft]=S.current,Mr(e.nodeType===8?e.parentNode:e),Pn(function(){So(t,S,n,o)}),S}function ko(e,t,n,o,a){var c=n._reactRootContainer;if(c){var h=c;if(typeof a=="function"){var w=a;a=function(){var S=Eo(h);w.call(S)}}So(t,h,e,a)}else h=Vv(n,t,e,a,o);return Eo(h)}Xu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=kr(t.pendingLanes);n!==0&&(ds(t,n|1),ot(t,be()),(de&6)===0&&(sr=be()+500,nn()))}break;case 13:Pn(function(){var o=Ht(e,1);if(o!==null){var a=Ye();Tt(o,e,1,a)}}),Bl(e,1)}},hs=function(e){if(e.tag===13){var t=Ht(e,134217728);if(t!==null){var n=Ye();Tt(t,e,134217728,n)}Bl(e,134217728)}},Ju=function(e){if(e.tag===13){var t=un(e),n=Ht(e,t);if(n!==null){var o=Ye();Tt(n,e,t,o)}Bl(e,t)}},Qu=function(){return we},Ku=function(e,t){var n=we;try{return we=e,t()}finally{we=n}},os=function(e,t,n){switch(t){case"input":if(Go(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var a=Bi(o);if(!a)throw Error(s(90));Su(o),Go(o,a)}}}break;case"textarea":Cu(e,n);break;case"select":t=n.value,t!=null&&Dn(e,!!n.multiple,t,!1)}},bu=Il,Au=Pn;var Xv={usingClientEntryPoint:!1,Events:[Hr,Xn,Bi,Lu,Iu,Il]},ni={findFiberByHostInstance:En,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Jv={bundleType:ni.bundleType,version:ni.version,rendererPackageName:ni.rendererPackageName,rendererConfig:ni.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:R.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Mu(e),e===null?null:e.stateNode},findFiberByHostInstance:ni.findFiberByHostInstance||$v,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Co=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Co.isDisabled&&Co.supportsFiber)try{Ei=Co.inject(Jv),jt=Co}catch{}}return st.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Xv,st.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hl(t))throw Error(s(200));return qv(e,t,null,n)},st.createRoot=function(e,t){if(!Hl(e))throw Error(s(299));var n=!1,o="",a=hd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onRecoverableError!==void 0&&(a=t.onRecoverableError)),t=Ml(e,1,!1,null,null,n,!1,o,a),e[Ft]=t.current,Mr(e.nodeType===8?e.parentNode:e),new zl(t)},st.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=Mu(t),e=e===null?null:e.stateNode,e},st.flushSync=function(e){return Pn(e)},st.hydrate=function(e,t,n){if(!xo(t))throw Error(s(200));return ko(null,e,t,!0,n)},st.hydrateRoot=function(e,t,n){if(!Hl(e))throw Error(s(405));var o=n!=null&&n.hydratedSources||null,a=!1,c="",h=hd;if(n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(c=n.identifierPrefix),n.onRecoverableError!==void 0&&(h=n.onRecoverableError)),t=fd(t,null,e,1,n??null,a,!1,c,h),e[Ft]=t.current,Mr(e),o)for(e=0;e<o.length;e++)n=o[e],a=n._getVersion,a=a(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new _o(t)},st.render=function(e,t,n){if(!xo(t))throw Error(s(200));return ko(null,e,t,!1,n)},st.unmountComponentAtNode=function(e){if(!xo(e))throw Error(s(40));return e._reactRootContainer?(Pn(function(){ko(null,null,e,!1,function(){e._reactRootContainer=null,e[Ft]=null})}),!0):!1},st.unstable_batchedUpdates=Il,st.unstable_renderSubtreeIntoContainer=function(e,t,n,o){if(!xo(n))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return ko(e,t,n,!1,o)},st.version="18.3.1-next-f1338f8080-20240426",st}var _d;function ip(){if(_d)return $l.exports;_d=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),$l.exports=rg(),$l.exports}var xd;function ig(){if(xd)return Ro;xd=1;var i=ip();return Ro.createRoot=i.createRoot,Ro.hydrateRoot=i.hydrateRoot,Ro}var og=ig();ip();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function si(){return si=Object.assign?Object.assign.bind():function(i){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(i[l]=s[l])}return i},si.apply(this,arguments)}var mn;(function(i){i.Pop="POP",i.Push="PUSH",i.Replace="REPLACE"})(mn||(mn={}));const kd="popstate";function sg(i){i===void 0&&(i={});function r(l,u){let{pathname:f,search:d,hash:p}=l.location;return Ga("",{pathname:f,search:d,hash:p},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function s(l,u){return typeof u=="string"?u:Uo(u)}return ag(r,s,null,i)}function Ue(i,r){if(i===!1||i===null||typeof i>"u")throw new Error(r)}function op(i,r){if(!i){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function lg(){return Math.random().toString(36).substr(2,8)}function Cd(i,r){return{usr:i.state,key:i.key,idx:r}}function Ga(i,r,s,l){return s===void 0&&(s=null),si({pathname:typeof i=="string"?i:i.pathname,search:"",hash:""},typeof r=="string"?ur(r):r,{state:s,key:r&&r.key||l||lg()})}function Uo(i){let{pathname:r="/",search:s="",hash:l=""}=i;return s&&s!=="?"&&(r+=s.charAt(0)==="?"?s:"?"+s),l&&l!=="#"&&(r+=l.charAt(0)==="#"?l:"#"+l),r}function ur(i){let r={};if(i){let s=i.indexOf("#");s>=0&&(r.hash=i.substr(s),i=i.substr(0,s));let l=i.indexOf("?");l>=0&&(r.search=i.substr(l),i=i.substr(0,l)),i&&(r.pathname=i)}return r}function ag(i,r,s,l){l===void 0&&(l={});let{window:u=document.defaultView,v5Compat:f=!1}=l,d=u.history,p=mn.Pop,m=null,v=g();v==null&&(v=0,d.replaceState(si({},d.state,{idx:v}),""));function g(){return(d.state||{idx:null}).idx}function y(){p=mn.Pop;let N=g(),M=N==null?null:N-v;v=N,m&&m({action:p,location:x.location,delta:M})}function _(N,M){p=mn.Push;let T=Ga(x.location,N,M);v=g()+1;let L=Cd(T,v),R=x.createHref(T);try{d.pushState(L,"",R)}catch(I){if(I instanceof DOMException&&I.name==="DataCloneError")throw I;u.location.assign(R)}f&&m&&m({action:p,location:x.location,delta:1})}function D(N,M){p=mn.Replace;let T=Ga(x.location,N,M);v=g();let L=Cd(T,v),R=x.createHref(T);d.replaceState(L,"",R),f&&m&&m({action:p,location:x.location,delta:0})}function U(N){let M=u.location.origin!=="null"?u.location.origin:u.location.href,T=typeof N=="string"?N:Uo(N);return T=T.replace(/ $/,"%20"),Ue(M,"No window.location.(origin|href) available to create URL for href: "+T),new URL(T,M)}let x={get action(){return p},get location(){return i(u,d)},listen(N){if(m)throw new Error("A history only accepts one active listener");return u.addEventListener(kd,y),m=N,()=>{u.removeEventListener(kd,y),m=null}},createHref(N){return r(u,N)},createURL:U,encodeLocation(N){let M=U(N);return{pathname:M.pathname,search:M.search,hash:M.hash}},push:_,replace:D,go(N){return d.go(N)}};return x}var Rd;(function(i){i.data="data",i.deferred="deferred",i.redirect="redirect",i.error="error"})(Rd||(Rd={}));function ug(i,r,s){return s===void 0&&(s="/"),cg(i,r,s)}function cg(i,r,s,l){let u=typeof r=="string"?ur(r):r,f=cu(u.pathname||"/",s);if(f==null)return null;let d=sp(i);fg(d);let p=null;for(let m=0;p==null&&m<d.length;++m){let v=xg(f);p=Sg(d[m],v)}return p}function sp(i,r,s,l){r===void 0&&(r=[]),s===void 0&&(s=[]),l===void 0&&(l="");let u=(f,d,p)=>{let m={relativePath:p===void 0?f.path||"":p,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};m.relativePath.startsWith("/")&&(Ue(m.relativePath.startsWith(l),'Absolute route path "'+m.relativePath+'" nested under path '+('"'+l+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),m.relativePath=m.relativePath.slice(l.length));let v=vn([l,m.relativePath]),g=s.concat(m);f.children&&f.children.length>0&&(Ue(f.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+v+'".')),sp(f.children,r,g,v)),!(f.path==null&&!f.index)&&r.push({path:v,score:yg(v,f.index),routesMeta:g})};return i.forEach((f,d)=>{var p;if(f.path===""||!((p=f.path)!=null&&p.includes("?")))u(f,d);else for(let m of lp(f.path))u(f,d,m)}),r}function lp(i){let r=i.split("/");if(r.length===0)return[];let[s,...l]=r,u=s.endsWith("?"),f=s.replace(/\?$/,"");if(l.length===0)return u?[f,""]:[f];let d=lp(l.join("/")),p=[];return p.push(...d.map(m=>m===""?f:[f,m].join("/"))),u&&p.push(...d),p.map(m=>i.startsWith("/")&&m===""?"/":m)}function fg(i){i.sort((r,s)=>r.score!==s.score?s.score-r.score:wg(r.routesMeta.map(l=>l.childrenIndex),s.routesMeta.map(l=>l.childrenIndex)))}const dg=/^:[\w-]+$/,hg=3,pg=2,mg=1,vg=10,gg=-2,Nd=i=>i==="*";function yg(i,r){let s=i.split("/"),l=s.length;return s.some(Nd)&&(l+=gg),r&&(l+=pg),s.filter(u=>!Nd(u)).reduce((u,f)=>u+(dg.test(f)?hg:f===""?mg:vg),l)}function wg(i,r){return i.length===r.length&&i.slice(0,-1).every((l,u)=>l===r[u])?i[i.length-1]-r[r.length-1]:0}function Sg(i,r,s){let{routesMeta:l}=i,u={},f="/",d=[];for(let p=0;p<l.length;++p){let m=l[p],v=p===l.length-1,g=f==="/"?r:r.slice(f.length)||"/",y=Eg({path:m.relativePath,caseSensitive:m.caseSensitive,end:v},g),_=m.route;if(!y)return null;Object.assign(u,y.params),d.push({params:u,pathname:vn([f,y.pathname]),pathnameBase:Ng(vn([f,y.pathnameBase])),route:_}),y.pathnameBase!=="/"&&(f=vn([f,y.pathnameBase]))}return d}function Eg(i,r){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[s,l]=_g(i.path,i.caseSensitive,i.end),u=r.match(s);if(!u)return null;let f=u[0],d=f.replace(/(.)\/+$/,"$1"),p=u.slice(1);return{params:l.reduce((v,g,y)=>{let{paramName:_,isOptional:D}=g;if(_==="*"){let x=p[y]||"";d=f.slice(0,f.length-x.length).replace(/(.)\/+$/,"$1")}const U=p[y];return D&&!U?v[_]=void 0:v[_]=(U||"").replace(/%2F/g,"/"),v},{}),pathname:f,pathnameBase:d,pattern:i}}function _g(i,r,s){r===void 0&&(r=!1),s===void 0&&(s=!0),op(i==="*"||!i.endsWith("*")||i.endsWith("/*"),'Route path "'+i+'" will be treated as if it were '+('"'+i.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+i.replace(/\*$/,"/*")+'".'));let l=[],u="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,p,m)=>(l.push({paramName:p,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(l.push({paramName:"*"}),u+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?u+="\\/*$":i!==""&&i!=="/"&&(u+="(?:(?=\\/|$))"),[new RegExp(u,r?void 0:"i"),l]}function xg(i){try{return i.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return op(!1,'The URL path "'+i+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),i}}function cu(i,r){if(r==="/")return i;if(!i.toLowerCase().startsWith(r.toLowerCase()))return null;let s=r.endsWith("/")?r.length-1:r.length,l=i.charAt(s);return l&&l!=="/"?null:i.slice(s)||"/"}function kg(i,r){r===void 0&&(r="/");let{pathname:s,search:l="",hash:u=""}=typeof i=="string"?ur(i):i;return{pathname:s?s.startsWith("/")?s:Cg(s,r):r,search:Tg(l),hash:Pg(u)}}function Cg(i,r){let s=r.replace(/\/+$/,"").split("/");return i.split("/").forEach(u=>{u===".."?s.length>1&&s.pop():u!=="."&&s.push(u)}),s.length>1?s.join("/"):"/"}function Jl(i,r,s,l){return"Cannot include a '"+i+"' character in a manually specified "+("`to."+r+"` field ["+JSON.stringify(l)+"].  Please separate it out to the ")+("`to."+s+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Rg(i){return i.filter((r,s)=>s===0||r.route.path&&r.route.path.length>0)}function fu(i,r){let s=Rg(i);return r?s.map((l,u)=>u===s.length-1?l.pathname:l.pathnameBase):s.map(l=>l.pathnameBase)}function du(i,r,s,l){l===void 0&&(l=!1);let u;typeof i=="string"?u=ur(i):(u=si({},i),Ue(!u.pathname||!u.pathname.includes("?"),Jl("?","pathname","search",u)),Ue(!u.pathname||!u.pathname.includes("#"),Jl("#","pathname","hash",u)),Ue(!u.search||!u.search.includes("#"),Jl("#","search","hash",u)));let f=i===""||u.pathname==="",d=f?"/":u.pathname,p;if(d==null)p=s;else{let y=r.length-1;if(!l&&d.startsWith("..")){let _=d.split("/");for(;_[0]==="..";)_.shift(),y-=1;u.pathname=_.join("/")}p=y>=0?r[y]:"/"}let m=kg(u,p),v=d&&d!=="/"&&d.endsWith("/"),g=(f||d===".")&&s.endsWith("/");return!m.pathname.endsWith("/")&&(v||g)&&(m.pathname+="/"),m}const vn=i=>i.join("/").replace(/\/\/+/g,"/"),Ng=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),Tg=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,Pg=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function Og(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}const ap=["post","put","patch","delete"];new Set(ap);const jg=["get",...ap];new Set(jg);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function li(){return li=Object.assign?Object.assign.bind():function(i){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(i[l]=s[l])}return i},li.apply(this,arguments)}const hu=F.createContext(null),Lg=F.createContext(null),gn=F.createContext(null),Bo=F.createContext(null),yn=F.createContext({outlet:null,matches:[],isDataRoute:!1}),up=F.createContext(null);function Ig(i,r){let{relative:s}=r===void 0?{}:r;cr()||Ue(!1);let{basename:l,navigator:u}=F.useContext(gn),{hash:f,pathname:d,search:p}=fp(i,{relative:s}),m=d;return l!=="/"&&(m=d==="/"?l:vn([l,d])),u.createHref({pathname:m,search:p,hash:f})}function cr(){return F.useContext(Bo)!=null}function fr(){return cr()||Ue(!1),F.useContext(Bo).location}function cp(i){F.useContext(gn).static||F.useLayoutEffect(i)}function dr(){let{isDataRoute:i}=F.useContext(yn);return i?Vg():bg()}function bg(){cr()||Ue(!1);let i=F.useContext(hu),{basename:r,future:s,navigator:l}=F.useContext(gn),{matches:u}=F.useContext(yn),{pathname:f}=fr(),d=JSON.stringify(fu(u,s.v7_relativeSplatPath)),p=F.useRef(!1);return cp(()=>{p.current=!0}),F.useCallback(function(v,g){if(g===void 0&&(g={}),!p.current)return;if(typeof v=="number"){l.go(v);return}let y=du(v,JSON.parse(d),f,g.relative==="path");i==null&&r!=="/"&&(y.pathname=y.pathname==="/"?r:vn([r,y.pathname])),(g.replace?l.replace:l.push)(y,g.state,g)},[r,l,d,f,i])}function fp(i,r){let{relative:s}=r===void 0?{}:r,{future:l}=F.useContext(gn),{matches:u}=F.useContext(yn),{pathname:f}=fr(),d=JSON.stringify(fu(u,l.v7_relativeSplatPath));return F.useMemo(()=>du(i,JSON.parse(d),f,s==="path"),[i,d,f,s])}function Ag(i,r){return Ug(i,r)}function Ug(i,r,s,l){cr()||Ue(!1);let{navigator:u,static:f}=F.useContext(gn),{matches:d}=F.useContext(yn),p=d[d.length-1],m=p?p.params:{};p&&p.pathname;let v=p?p.pathnameBase:"/";p&&p.route;let g=fr(),y;if(r){var _;let M=typeof r=="string"?ur(r):r;v==="/"||(_=M.pathname)!=null&&_.startsWith(v)||Ue(!1),y=M}else y=g;let D=y.pathname||"/",U=D;if(v!=="/"){let M=v.replace(/^\//,"").split("/");U="/"+D.replace(/^\//,"").split("/").slice(M.length).join("/")}let x=ug(i,{pathname:U}),N=zg(x&&x.map(M=>Object.assign({},M,{params:Object.assign({},m,M.params),pathname:vn([v,u.encodeLocation?u.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?v:vn([v,u.encodeLocation?u.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),d,s,l);return r&&N?F.createElement(Bo.Provider,{value:{location:li({pathname:"/",search:"",hash:"",state:null,key:"default"},y),navigationType:mn.Pop}},N):N}function Dg(){let i=$g(),r=Og(i)?i.status+" "+i.statusText:i instanceof Error?i.message:JSON.stringify(i),s=i instanceof Error?i.stack:null,u={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return F.createElement(F.Fragment,null,F.createElement("h2",null,"Unexpected Application Error!"),F.createElement("h3",{style:{fontStyle:"italic"}},r),s?F.createElement("pre",{style:u},s):null,null)}const Fg=F.createElement(Dg,null);class Mg extends F.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,s){return s.location!==r.location||s.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:s.error,location:s.location,revalidation:r.revalidation||s.revalidation}}componentDidCatch(r,s){console.error("React Router caught the following error during render",r,s)}render(){return this.state.error!==void 0?F.createElement(yn.Provider,{value:this.props.routeContext},F.createElement(up.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Bg(i){let{routeContext:r,match:s,children:l}=i,u=F.useContext(hu);return u&&u.static&&u.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=s.route.id),F.createElement(yn.Provider,{value:r},l)}function zg(i,r,s,l){var u;if(r===void 0&&(r=[]),s===void 0&&(s=null),l===void 0&&(l=null),i==null){var f;if(!s)return null;if(s.errors)i=s.matches;else if((f=l)!=null&&f.v7_partialHydration&&r.length===0&&!s.initialized&&s.matches.length>0)i=s.matches;else return null}let d=i,p=(u=s)==null?void 0:u.errors;if(p!=null){let g=d.findIndex(y=>y.route.id&&(p==null?void 0:p[y.route.id])!==void 0);g>=0||Ue(!1),d=d.slice(0,Math.min(d.length,g+1))}let m=!1,v=-1;if(s&&l&&l.v7_partialHydration)for(let g=0;g<d.length;g++){let y=d[g];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(v=g),y.route.id){let{loaderData:_,errors:D}=s,U=y.route.loader&&_[y.route.id]===void 0&&(!D||D[y.route.id]===void 0);if(y.route.lazy||U){m=!0,v>=0?d=d.slice(0,v+1):d=[d[0]];break}}}return d.reduceRight((g,y,_)=>{let D,U=!1,x=null,N=null;s&&(D=p&&y.route.id?p[y.route.id]:void 0,x=y.route.errorElement||Fg,m&&(v<0&&_===0?(Xg("route-fallback"),U=!0,N=null):v===_&&(U=!0,N=y.route.hydrateFallbackElement||null)));let M=r.concat(d.slice(0,_+1)),T=()=>{let L;return D?L=x:U?L=N:y.route.Component?L=F.createElement(y.route.Component,null):y.route.element?L=y.route.element:L=g,F.createElement(Bg,{match:y,routeContext:{outlet:g,matches:M,isDataRoute:s!=null},children:L})};return s&&(y.route.ErrorBoundary||y.route.errorElement||_===0)?F.createElement(Mg,{location:s.location,revalidation:s.revalidation,component:x,error:D,children:T(),routeContext:{outlet:null,matches:M,isDataRoute:!0}}):T()},null)}var dp=function(i){return i.UseBlocker="useBlocker",i.UseRevalidator="useRevalidator",i.UseNavigateStable="useNavigate",i}(dp||{}),hp=function(i){return i.UseBlocker="useBlocker",i.UseLoaderData="useLoaderData",i.UseActionData="useActionData",i.UseRouteError="useRouteError",i.UseNavigation="useNavigation",i.UseRouteLoaderData="useRouteLoaderData",i.UseMatches="useMatches",i.UseRevalidator="useRevalidator",i.UseNavigateStable="useNavigate",i.UseRouteId="useRouteId",i}(hp||{});function Hg(i){let r=F.useContext(hu);return r||Ue(!1),r}function Wg(i){let r=F.useContext(Lg);return r||Ue(!1),r}function qg(i){let r=F.useContext(yn);return r||Ue(!1),r}function pp(i){let r=qg(),s=r.matches[r.matches.length-1];return s.route.id||Ue(!1),s.route.id}function $g(){var i;let r=F.useContext(up),s=Wg(),l=pp();return r!==void 0?r:(i=s.errors)==null?void 0:i[l]}function Vg(){let{router:i}=Hg(dp.UseNavigateStable),r=pp(hp.UseNavigateStable),s=F.useRef(!1);return cp(()=>{s.current=!0}),F.useCallback(function(u,f){f===void 0&&(f={}),s.current&&(typeof u=="number"?i.navigate(u):i.navigate(u,li({fromRouteId:r},f)))},[i,r])}const Td={};function Xg(i,r,s){Td[i]||(Td[i]=!0)}function Jg(i,r){i==null||i.v7_startTransition,i==null||i.v7_relativeSplatPath}function Qg(i){let{to:r,replace:s,state:l,relative:u}=i;cr()||Ue(!1);let{future:f,static:d}=F.useContext(gn),{matches:p}=F.useContext(yn),{pathname:m}=fr(),v=dr(),g=du(r,fu(p,f.v7_relativeSplatPath),m,u==="path"),y=JSON.stringify(g);return F.useEffect(()=>v(JSON.parse(y),{replace:s,state:l,relative:u}),[v,y,u,s,l]),null}function In(i){Ue(!1)}function Kg(i){let{basename:r="/",children:s=null,location:l,navigationType:u=mn.Pop,navigator:f,static:d=!1,future:p}=i;cr()&&Ue(!1);let m=r.replace(/^\/*/,"/"),v=F.useMemo(()=>({basename:m,navigator:f,static:d,future:li({v7_relativeSplatPath:!1},p)}),[m,p,f,d]);typeof l=="string"&&(l=ur(l));let{pathname:g="/",search:y="",hash:_="",state:D=null,key:U="default"}=l,x=F.useMemo(()=>{let N=cu(g,m);return N==null?null:{location:{pathname:N,search:y,hash:_,state:D,key:U},navigationType:u}},[m,g,y,_,D,U,u]);return x==null?null:F.createElement(gn.Provider,{value:v},F.createElement(Bo.Provider,{children:s,value:x}))}function Gg(i){let{children:r,location:s}=i;return Ag(Ya(r),s)}new Promise(()=>{});function Ya(i,r){r===void 0&&(r=[]);let s=[];return F.Children.forEach(i,(l,u)=>{if(!F.isValidElement(l))return;let f=[...r,u];if(l.type===F.Fragment){s.push.apply(s,Ya(l.props.children,f));return}l.type!==In&&Ue(!1),!l.props.index||!l.props.children||Ue(!1);let d={id:l.props.id||f.join("-"),caseSensitive:l.props.caseSensitive,element:l.props.element,Component:l.props.Component,index:l.props.index,path:l.props.path,loader:l.props.loader,action:l.props.action,errorElement:l.props.errorElement,ErrorBoundary:l.props.ErrorBoundary,hasErrorBoundary:l.props.ErrorBoundary!=null||l.props.errorElement!=null,shouldRevalidate:l.props.shouldRevalidate,handle:l.props.handle,lazy:l.props.lazy};l.props.children&&(d.children=Ya(l.props.children,f)),s.push(d)}),s}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Za(){return Za=Object.assign?Object.assign.bind():function(i){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(i[l]=s[l])}return i},Za.apply(this,arguments)}function Yg(i,r){if(i==null)return{};var s={},l=Object.keys(i),u,f;for(f=0;f<l.length;f++)u=l[f],!(r.indexOf(u)>=0)&&(s[u]=i[u]);return s}function Zg(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function ey(i,r){return i.button===0&&(!r||r==="_self")&&!Zg(i)}const ty=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ny="6";try{window.__reactRouterVersion=ny}catch{}const ry="startTransition",Pd=eg[ry];function iy(i){let{basename:r,children:s,future:l,window:u}=i,f=F.useRef();f.current==null&&(f.current=sg({window:u,v5Compat:!0}));let d=f.current,[p,m]=F.useState({action:d.action,location:d.location}),{v7_startTransition:v}=l||{},g=F.useCallback(y=>{v&&Pd?Pd(()=>m(y)):m(y)},[m,v]);return F.useLayoutEffect(()=>d.listen(g),[d,g]),F.useEffect(()=>Jg(l),[l]),F.createElement(Kg,{basename:r,children:s,location:p.location,navigationType:p.action,navigator:d,future:l})}const oy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",sy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,St=F.forwardRef(function(r,s){let{onClick:l,relative:u,reloadDocument:f,replace:d,state:p,target:m,to:v,preventScrollReset:g,viewTransition:y}=r,_=Yg(r,ty),{basename:D}=F.useContext(gn),U,x=!1;if(typeof v=="string"&&sy.test(v)&&(U=v,oy))try{let L=new URL(window.location.href),R=v.startsWith("//")?new URL(L.protocol+v):new URL(v),I=cu(R.pathname,D);R.origin===L.origin&&I!=null?v=I+R.search+R.hash:x=!0}catch{}let N=Ig(v,{relative:u}),M=ly(v,{replace:d,state:p,target:m,preventScrollReset:g,relative:u,viewTransition:y});function T(L){l&&l(L),L.defaultPrevented||M(L)}return F.createElement("a",Za({},_,{href:U||N,onClick:x||f?l:T,ref:s,target:m}))});var Od;(function(i){i.UseScrollRestoration="useScrollRestoration",i.UseSubmit="useSubmit",i.UseSubmitFetcher="useSubmitFetcher",i.UseFetcher="useFetcher",i.useViewTransitionState="useViewTransitionState"})(Od||(Od={}));var jd;(function(i){i.UseFetcher="useFetcher",i.UseFetchers="useFetchers",i.UseScrollRestoration="useScrollRestoration"})(jd||(jd={}));function ly(i,r){let{target:s,replace:l,state:u,preventScrollReset:f,relative:d,viewTransition:p}=r===void 0?{}:r,m=dr(),v=fr(),g=fp(i,{relative:d});return F.useCallback(y=>{if(ey(y,s)){y.preventDefault();let _=l!==void 0?l:Uo(v)===Uo(g);m(i,{replace:_,state:u,preventScrollReset:f,relative:d,viewTransition:p})}},[v,m,g,l,u,s,i,f,d,p])}function mp(i,r){return function(){return i.apply(r,arguments)}}const{toString:ay}=Object.prototype,{getPrototypeOf:pu}=Object,zo=(i=>r=>{const s=ay.call(r);return i[s]||(i[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Ot=i=>(i=i.toLowerCase(),r=>zo(r)===i),Ho=i=>r=>typeof r===i,{isArray:hr}=Array,ai=Ho("undefined");function uy(i){return i!==null&&!ai(i)&&i.constructor!==null&&!ai(i.constructor)&&ht(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const vp=Ot("ArrayBuffer");function cy(i){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(i):r=i&&i.buffer&&vp(i.buffer),r}const fy=Ho("string"),ht=Ho("function"),gp=Ho("number"),Wo=i=>i!==null&&typeof i=="object",dy=i=>i===!0||i===!1,Io=i=>{if(zo(i)!=="object")return!1;const r=pu(i);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in i)&&!(Symbol.iterator in i)},hy=Ot("Date"),py=Ot("File"),my=Ot("Blob"),vy=Ot("FileList"),gy=i=>Wo(i)&&ht(i.pipe),yy=i=>{let r;return i&&(typeof FormData=="function"&&i instanceof FormData||ht(i.append)&&((r=zo(i))==="formdata"||r==="object"&&ht(i.toString)&&i.toString()==="[object FormData]"))},wy=Ot("URLSearchParams"),[Sy,Ey,_y,xy]=["ReadableStream","Request","Response","Headers"].map(Ot),ky=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ci(i,r,{allOwnKeys:s=!1}={}){if(i===null||typeof i>"u")return;let l,u;if(typeof i!="object"&&(i=[i]),hr(i))for(l=0,u=i.length;l<u;l++)r.call(null,i[l],l,i);else{const f=s?Object.getOwnPropertyNames(i):Object.keys(i),d=f.length;let p;for(l=0;l<d;l++)p=f[l],r.call(null,i[p],p,i)}}function yp(i,r){r=r.toLowerCase();const s=Object.keys(i);let l=s.length,u;for(;l-- >0;)if(u=s[l],r===u.toLowerCase())return u;return null}const bn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:window,wp=i=>!ai(i)&&i!==bn;function eu(){const{caseless:i}=wp(this)&&this||{},r={},s=(l,u)=>{const f=i&&yp(r,u)||u;Io(r[f])&&Io(l)?r[f]=eu(r[f],l):Io(l)?r[f]=eu({},l):hr(l)?r[f]=l.slice():r[f]=l};for(let l=0,u=arguments.length;l<u;l++)arguments[l]&&ci(arguments[l],s);return r}const Cy=(i,r,s,{allOwnKeys:l}={})=>(ci(r,(u,f)=>{s&&ht(u)?i[f]=mp(u,s):i[f]=u},{allOwnKeys:l}),i),Ry=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),Ny=(i,r,s,l)=>{i.prototype=Object.create(r.prototype,l),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:r.prototype}),s&&Object.assign(i.prototype,s)},Ty=(i,r,s,l)=>{let u,f,d;const p={};if(r=r||{},i==null)return r;do{for(u=Object.getOwnPropertyNames(i),f=u.length;f-- >0;)d=u[f],(!l||l(d,i,r))&&!p[d]&&(r[d]=i[d],p[d]=!0);i=s!==!1&&pu(i)}while(i&&(!s||s(i,r))&&i!==Object.prototype);return r},Py=(i,r,s)=>{i=String(i),(s===void 0||s>i.length)&&(s=i.length),s-=r.length;const l=i.indexOf(r,s);return l!==-1&&l===s},Oy=i=>{if(!i)return null;if(hr(i))return i;let r=i.length;if(!gp(r))return null;const s=new Array(r);for(;r-- >0;)s[r]=i[r];return s},jy=(i=>r=>i&&r instanceof i)(typeof Uint8Array<"u"&&pu(Uint8Array)),Ly=(i,r)=>{const l=(i&&i[Symbol.iterator]).call(i);let u;for(;(u=l.next())&&!u.done;){const f=u.value;r.call(i,f[0],f[1])}},Iy=(i,r)=>{let s;const l=[];for(;(s=i.exec(r))!==null;)l.push(s);return l},by=Ot("HTMLFormElement"),Ay=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,l,u){return l.toUpperCase()+u}),Ld=(({hasOwnProperty:i})=>(r,s)=>i.call(r,s))(Object.prototype),Uy=Ot("RegExp"),Sp=(i,r)=>{const s=Object.getOwnPropertyDescriptors(i),l={};ci(s,(u,f)=>{let d;(d=r(u,f,i))!==!1&&(l[f]=d||u)}),Object.defineProperties(i,l)},Dy=i=>{Sp(i,(r,s)=>{if(ht(i)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const l=i[s];if(ht(l)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},Fy=(i,r)=>{const s={},l=u=>{u.forEach(f=>{s[f]=!0})};return hr(i)?l(i):l(String(i).split(r)),s},My=()=>{},By=(i,r)=>i!=null&&Number.isFinite(i=+i)?i:r;function zy(i){return!!(i&&ht(i.append)&&i[Symbol.toStringTag]==="FormData"&&i[Symbol.iterator])}const Hy=i=>{const r=new Array(10),s=(l,u)=>{if(Wo(l)){if(r.indexOf(l)>=0)return;if(!("toJSON"in l)){r[u]=l;const f=hr(l)?[]:{};return ci(l,(d,p)=>{const m=s(d,u+1);!ai(m)&&(f[p]=m)}),r[u]=void 0,f}}return l};return s(i,0)},Wy=Ot("AsyncFunction"),qy=i=>i&&(Wo(i)||ht(i))&&ht(i.then)&&ht(i.catch),Ep=((i,r)=>i?setImmediate:r?((s,l)=>(bn.addEventListener("message",({source:u,data:f})=>{u===bn&&f===s&&l.length&&l.shift()()},!1),u=>{l.push(u),bn.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",ht(bn.postMessage)),$y=typeof queueMicrotask<"u"?queueMicrotask.bind(bn):typeof process<"u"&&process.nextTick||Ep,A={isArray:hr,isArrayBuffer:vp,isBuffer:uy,isFormData:yy,isArrayBufferView:cy,isString:fy,isNumber:gp,isBoolean:dy,isObject:Wo,isPlainObject:Io,isReadableStream:Sy,isRequest:Ey,isResponse:_y,isHeaders:xy,isUndefined:ai,isDate:hy,isFile:py,isBlob:my,isRegExp:Uy,isFunction:ht,isStream:gy,isURLSearchParams:wy,isTypedArray:jy,isFileList:vy,forEach:ci,merge:eu,extend:Cy,trim:ky,stripBOM:Ry,inherits:Ny,toFlatObject:Ty,kindOf:zo,kindOfTest:Ot,endsWith:Py,toArray:Oy,forEachEntry:Ly,matchAll:Iy,isHTMLForm:by,hasOwnProperty:Ld,hasOwnProp:Ld,reduceDescriptors:Sp,freezeMethods:Dy,toObjectSet:Fy,toCamelCase:Ay,noop:My,toFiniteNumber:By,findKey:yp,global:bn,isContextDefined:wp,isSpecCompliantForm:zy,toJSONObject:Hy,isAsyncFn:Wy,isThenable:qy,setImmediate:Ep,asap:$y};function le(i,r,s,l,u){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",r&&(this.code=r),s&&(this.config=s),l&&(this.request=l),u&&(this.response=u,this.status=u.status?u.status:null)}A.inherits(le,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:A.toJSONObject(this.config),code:this.code,status:this.status}}});const _p=le.prototype,xp={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{xp[i]={value:i}});Object.defineProperties(le,xp);Object.defineProperty(_p,"isAxiosError",{value:!0});le.from=(i,r,s,l,u,f)=>{const d=Object.create(_p);return A.toFlatObject(i,d,function(m){return m!==Error.prototype},p=>p!=="isAxiosError"),le.call(d,i.message,r,s,l,u),d.cause=i,d.name=i.name,f&&Object.assign(d,f),d};const Vy=null;function tu(i){return A.isPlainObject(i)||A.isArray(i)}function kp(i){return A.endsWith(i,"[]")?i.slice(0,-2):i}function Id(i,r,s){return i?i.concat(r).map(function(u,f){return u=kp(u),!s&&f?"["+u+"]":u}).join(s?".":""):r}function Xy(i){return A.isArray(i)&&!i.some(tu)}const Jy=A.toFlatObject(A,{},null,function(r){return/^is[A-Z]/.test(r)});function qo(i,r,s){if(!A.isObject(i))throw new TypeError("target must be an object");r=r||new FormData,s=A.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,N){return!A.isUndefined(N[x])});const l=s.metaTokens,u=s.visitor||g,f=s.dots,d=s.indexes,m=(s.Blob||typeof Blob<"u"&&Blob)&&A.isSpecCompliantForm(r);if(!A.isFunction(u))throw new TypeError("visitor must be a function");function v(U){if(U===null)return"";if(A.isDate(U))return U.toISOString();if(!m&&A.isBlob(U))throw new le("Blob is not supported. Use a Buffer instead.");return A.isArrayBuffer(U)||A.isTypedArray(U)?m&&typeof Blob=="function"?new Blob([U]):Buffer.from(U):U}function g(U,x,N){let M=U;if(U&&!N&&typeof U=="object"){if(A.endsWith(x,"{}"))x=l?x:x.slice(0,-2),U=JSON.stringify(U);else if(A.isArray(U)&&Xy(U)||(A.isFileList(U)||A.endsWith(x,"[]"))&&(M=A.toArray(U)))return x=kp(x),M.forEach(function(L,R){!(A.isUndefined(L)||L===null)&&r.append(d===!0?Id([x],R,f):d===null?x:x+"[]",v(L))}),!1}return tu(U)?!0:(r.append(Id(N,x,f),v(U)),!1)}const y=[],_=Object.assign(Jy,{defaultVisitor:g,convertValue:v,isVisitable:tu});function D(U,x){if(!A.isUndefined(U)){if(y.indexOf(U)!==-1)throw Error("Circular reference detected in "+x.join("."));y.push(U),A.forEach(U,function(M,T){(!(A.isUndefined(M)||M===null)&&u.call(r,M,A.isString(T)?T.trim():T,x,_))===!0&&D(M,x?x.concat(T):[T])}),y.pop()}}if(!A.isObject(i))throw new TypeError("data must be an object");return D(i),r}function bd(i){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(l){return r[l]})}function mu(i,r){this._pairs=[],i&&qo(i,this,r)}const Cp=mu.prototype;Cp.append=function(r,s){this._pairs.push([r,s])};Cp.toString=function(r){const s=r?function(l){return r.call(this,l,bd)}:bd;return this._pairs.map(function(u){return s(u[0])+"="+s(u[1])},"").join("&")};function Qy(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Rp(i,r,s){if(!r)return i;const l=s&&s.encode||Qy;A.isFunction(s)&&(s={serialize:s});const u=s&&s.serialize;let f;if(u?f=u(r,s):f=A.isURLSearchParams(r)?r.toString():new mu(r,s).toString(l),f){const d=i.indexOf("#");d!==-1&&(i=i.slice(0,d)),i+=(i.indexOf("?")===-1?"?":"&")+f}return i}class Ad{constructor(){this.handlers=[]}use(r,s,l){return this.handlers.push({fulfilled:r,rejected:s,synchronous:l?l.synchronous:!1,runWhen:l?l.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){A.forEach(this.handlers,function(l){l!==null&&r(l)})}}const Np={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ky=typeof URLSearchParams<"u"?URLSearchParams:mu,Gy=typeof FormData<"u"?FormData:null,Yy=typeof Blob<"u"?Blob:null,Zy={isBrowser:!0,classes:{URLSearchParams:Ky,FormData:Gy,Blob:Yy},protocols:["http","https","file","blob","url","data"]},vu=typeof window<"u"&&typeof document<"u",nu=typeof navigator=="object"&&navigator||void 0,ew=vu&&(!nu||["ReactNative","NativeScript","NS"].indexOf(nu.product)<0),tw=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",nw=vu&&window.location.href||"http://localhost",rw=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:vu,hasStandardBrowserEnv:ew,hasStandardBrowserWebWorkerEnv:tw,navigator:nu,origin:nw},Symbol.toStringTag,{value:"Module"})),Qe={...rw,...Zy};function iw(i,r){return qo(i,new Qe.classes.URLSearchParams,Object.assign({visitor:function(s,l,u,f){return Qe.isNode&&A.isBuffer(s)?(this.append(l,s.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},r))}function ow(i){return A.matchAll(/\w+|\[(\w*)]/g,i).map(r=>r[0]==="[]"?"":r[1]||r[0])}function sw(i){const r={},s=Object.keys(i);let l;const u=s.length;let f;for(l=0;l<u;l++)f=s[l],r[f]=i[f];return r}function Tp(i){function r(s,l,u,f){let d=s[f++];if(d==="__proto__")return!0;const p=Number.isFinite(+d),m=f>=s.length;return d=!d&&A.isArray(u)?u.length:d,m?(A.hasOwnProp(u,d)?u[d]=[u[d],l]:u[d]=l,!p):((!u[d]||!A.isObject(u[d]))&&(u[d]=[]),r(s,l,u[d],f)&&A.isArray(u[d])&&(u[d]=sw(u[d])),!p)}if(A.isFormData(i)&&A.isFunction(i.entries)){const s={};return A.forEachEntry(i,(l,u)=>{r(ow(l),u,s,0)}),s}return null}function lw(i,r,s){if(A.isString(i))try{return(r||JSON.parse)(i),A.trim(i)}catch(l){if(l.name!=="SyntaxError")throw l}return(s||JSON.stringify)(i)}const fi={transitional:Np,adapter:["xhr","http","fetch"],transformRequest:[function(r,s){const l=s.getContentType()||"",u=l.indexOf("application/json")>-1,f=A.isObject(r);if(f&&A.isHTMLForm(r)&&(r=new FormData(r)),A.isFormData(r))return u?JSON.stringify(Tp(r)):r;if(A.isArrayBuffer(r)||A.isBuffer(r)||A.isStream(r)||A.isFile(r)||A.isBlob(r)||A.isReadableStream(r))return r;if(A.isArrayBufferView(r))return r.buffer;if(A.isURLSearchParams(r))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let p;if(f){if(l.indexOf("application/x-www-form-urlencoded")>-1)return iw(r,this.formSerializer).toString();if((p=A.isFileList(r))||l.indexOf("multipart/form-data")>-1){const m=this.env&&this.env.FormData;return qo(p?{"files[]":r}:r,m&&new m,this.formSerializer)}}return f||u?(s.setContentType("application/json",!1),lw(r)):r}],transformResponse:[function(r){const s=this.transitional||fi.transitional,l=s&&s.forcedJSONParsing,u=this.responseType==="json";if(A.isResponse(r)||A.isReadableStream(r))return r;if(r&&A.isString(r)&&(l&&!this.responseType||u)){const d=!(s&&s.silentJSONParsing)&&u;try{return JSON.parse(r)}catch(p){if(d)throw p.name==="SyntaxError"?le.from(p,le.ERR_BAD_RESPONSE,this,null,this.response):p}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Qe.classes.FormData,Blob:Qe.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};A.forEach(["delete","get","head","post","put","patch"],i=>{fi.headers[i]={}});const aw=A.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),uw=i=>{const r={};let s,l,u;return i&&i.split(`
`).forEach(function(d){u=d.indexOf(":"),s=d.substring(0,u).trim().toLowerCase(),l=d.substring(u+1).trim(),!(!s||r[s]&&aw[s])&&(s==="set-cookie"?r[s]?r[s].push(l):r[s]=[l]:r[s]=r[s]?r[s]+", "+l:l)}),r},Ud=Symbol("internals");function ii(i){return i&&String(i).trim().toLowerCase()}function bo(i){return i===!1||i==null?i:A.isArray(i)?i.map(bo):String(i)}function cw(i){const r=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let l;for(;l=s.exec(i);)r[l[1]]=l[2];return r}const fw=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function Ql(i,r,s,l,u){if(A.isFunction(l))return l.call(this,r,s);if(u&&(r=s),!!A.isString(r)){if(A.isString(l))return r.indexOf(l)!==-1;if(A.isRegExp(l))return l.test(r)}}function dw(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,s,l)=>s.toUpperCase()+l)}function hw(i,r){const s=A.toCamelCase(" "+r);["get","set","has"].forEach(l=>{Object.defineProperty(i,l+s,{value:function(u,f,d){return this[l].call(this,r,u,f,d)},configurable:!0})})}let lt=class{constructor(r){r&&this.set(r)}set(r,s,l){const u=this;function f(p,m,v){const g=ii(m);if(!g)throw new Error("header name must be a non-empty string");const y=A.findKey(u,g);(!y||u[y]===void 0||v===!0||v===void 0&&u[y]!==!1)&&(u[y||m]=bo(p))}const d=(p,m)=>A.forEach(p,(v,g)=>f(v,g,m));if(A.isPlainObject(r)||r instanceof this.constructor)d(r,s);else if(A.isString(r)&&(r=r.trim())&&!fw(r))d(uw(r),s);else if(A.isHeaders(r))for(const[p,m]of r.entries())f(m,p,l);else r!=null&&f(s,r,l);return this}get(r,s){if(r=ii(r),r){const l=A.findKey(this,r);if(l){const u=this[l];if(!s)return u;if(s===!0)return cw(u);if(A.isFunction(s))return s.call(this,u,l);if(A.isRegExp(s))return s.exec(u);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,s){if(r=ii(r),r){const l=A.findKey(this,r);return!!(l&&this[l]!==void 0&&(!s||Ql(this,this[l],l,s)))}return!1}delete(r,s){const l=this;let u=!1;function f(d){if(d=ii(d),d){const p=A.findKey(l,d);p&&(!s||Ql(l,l[p],p,s))&&(delete l[p],u=!0)}}return A.isArray(r)?r.forEach(f):f(r),u}clear(r){const s=Object.keys(this);let l=s.length,u=!1;for(;l--;){const f=s[l];(!r||Ql(this,this[f],f,r,!0))&&(delete this[f],u=!0)}return u}normalize(r){const s=this,l={};return A.forEach(this,(u,f)=>{const d=A.findKey(l,f);if(d){s[d]=bo(u),delete s[f];return}const p=r?dw(f):String(f).trim();p!==f&&delete s[f],s[p]=bo(u),l[p]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const s=Object.create(null);return A.forEach(this,(l,u)=>{l!=null&&l!==!1&&(s[u]=r&&A.isArray(l)?l.join(", "):l)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,s])=>r+": "+s).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...s){const l=new this(r);return s.forEach(u=>l.set(u)),l}static accessor(r){const l=(this[Ud]=this[Ud]={accessors:{}}).accessors,u=this.prototype;function f(d){const p=ii(d);l[p]||(hw(u,d),l[p]=!0)}return A.isArray(r)?r.forEach(f):f(r),this}};lt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);A.reduceDescriptors(lt.prototype,({value:i},r)=>{let s=r[0].toUpperCase()+r.slice(1);return{get:()=>i,set(l){this[s]=l}}});A.freezeMethods(lt);function Kl(i,r){const s=this||fi,l=r||s,u=lt.from(l.headers);let f=l.data;return A.forEach(i,function(p){f=p.call(s,f,u.normalize(),r?r.status:void 0)}),u.normalize(),f}function Pp(i){return!!(i&&i.__CANCEL__)}function pr(i,r,s){le.call(this,i??"canceled",le.ERR_CANCELED,r,s),this.name="CanceledError"}A.inherits(pr,le,{__CANCEL__:!0});function Op(i,r,s){const l=s.config.validateStatus;!s.status||!l||l(s.status)?i(s):r(new le("Request failed with status code "+s.status,[le.ERR_BAD_REQUEST,le.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function pw(i){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return r&&r[1]||""}function mw(i,r){i=i||10;const s=new Array(i),l=new Array(i);let u=0,f=0,d;return r=r!==void 0?r:1e3,function(m){const v=Date.now(),g=l[f];d||(d=v),s[u]=m,l[u]=v;let y=f,_=0;for(;y!==u;)_+=s[y++],y=y%i;if(u=(u+1)%i,u===f&&(f=(f+1)%i),v-d<r)return;const D=g&&v-g;return D?Math.round(_*1e3/D):void 0}}function vw(i,r){let s=0,l=1e3/r,u,f;const d=(v,g=Date.now())=>{s=g,u=null,f&&(clearTimeout(f),f=null),i.apply(null,v)};return[(...v)=>{const g=Date.now(),y=g-s;y>=l?d(v,g):(u=v,f||(f=setTimeout(()=>{f=null,d(u)},l-y)))},()=>u&&d(u)]}const Do=(i,r,s=3)=>{let l=0;const u=mw(50,250);return vw(f=>{const d=f.loaded,p=f.lengthComputable?f.total:void 0,m=d-l,v=u(m),g=d<=p;l=d;const y={loaded:d,total:p,progress:p?d/p:void 0,bytes:m,rate:v||void 0,estimated:v&&p&&g?(p-d)/v:void 0,event:f,lengthComputable:p!=null,[r?"download":"upload"]:!0};i(y)},s)},Dd=(i,r)=>{const s=i!=null;return[l=>r[0]({lengthComputable:s,total:i,loaded:l}),r[1]]},Fd=i=>(...r)=>A.asap(()=>i(...r)),gw=Qe.hasStandardBrowserEnv?((i,r)=>s=>(s=new URL(s,Qe.origin),i.protocol===s.protocol&&i.host===s.host&&(r||i.port===s.port)))(new URL(Qe.origin),Qe.navigator&&/(msie|trident)/i.test(Qe.navigator.userAgent)):()=>!0,yw=Qe.hasStandardBrowserEnv?{write(i,r,s,l,u,f){const d=[i+"="+encodeURIComponent(r)];A.isNumber(s)&&d.push("expires="+new Date(s).toGMTString()),A.isString(l)&&d.push("path="+l),A.isString(u)&&d.push("domain="+u),f===!0&&d.push("secure"),document.cookie=d.join("; ")},read(i){const r=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ww(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function Sw(i,r){return r?i.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):i}function jp(i,r,s){let l=!ww(r);return i&&(l||s==!1)?Sw(i,r):r}const Md=i=>i instanceof lt?{...i}:i;function Un(i,r){r=r||{};const s={};function l(v,g,y,_){return A.isPlainObject(v)&&A.isPlainObject(g)?A.merge.call({caseless:_},v,g):A.isPlainObject(g)?A.merge({},g):A.isArray(g)?g.slice():g}function u(v,g,y,_){if(A.isUndefined(g)){if(!A.isUndefined(v))return l(void 0,v,y,_)}else return l(v,g,y,_)}function f(v,g){if(!A.isUndefined(g))return l(void 0,g)}function d(v,g){if(A.isUndefined(g)){if(!A.isUndefined(v))return l(void 0,v)}else return l(void 0,g)}function p(v,g,y){if(y in r)return l(v,g);if(y in i)return l(void 0,v)}const m={url:f,method:f,data:f,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:p,headers:(v,g,y)=>u(Md(v),Md(g),y,!0)};return A.forEach(Object.keys(Object.assign({},i,r)),function(g){const y=m[g]||u,_=y(i[g],r[g],g);A.isUndefined(_)&&y!==p||(s[g]=_)}),s}const Lp=i=>{const r=Un({},i);let{data:s,withXSRFToken:l,xsrfHeaderName:u,xsrfCookieName:f,headers:d,auth:p}=r;r.headers=d=lt.from(d),r.url=Rp(jp(r.baseURL,r.url,r.allowAbsoluteUrls),i.params,i.paramsSerializer),p&&d.set("Authorization","Basic "+btoa((p.username||"")+":"+(p.password?unescape(encodeURIComponent(p.password)):"")));let m;if(A.isFormData(s)){if(Qe.hasStandardBrowserEnv||Qe.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((m=d.getContentType())!==!1){const[v,...g]=m?m.split(";").map(y=>y.trim()).filter(Boolean):[];d.setContentType([v||"multipart/form-data",...g].join("; "))}}if(Qe.hasStandardBrowserEnv&&(l&&A.isFunction(l)&&(l=l(r)),l||l!==!1&&gw(r.url))){const v=u&&f&&yw.read(f);v&&d.set(u,v)}return r},Ew=typeof XMLHttpRequest<"u",_w=Ew&&function(i){return new Promise(function(s,l){const u=Lp(i);let f=u.data;const d=lt.from(u.headers).normalize();let{responseType:p,onUploadProgress:m,onDownloadProgress:v}=u,g,y,_,D,U;function x(){D&&D(),U&&U(),u.cancelToken&&u.cancelToken.unsubscribe(g),u.signal&&u.signal.removeEventListener("abort",g)}let N=new XMLHttpRequest;N.open(u.method.toUpperCase(),u.url,!0),N.timeout=u.timeout;function M(){if(!N)return;const L=lt.from("getAllResponseHeaders"in N&&N.getAllResponseHeaders()),I={data:!p||p==="text"||p==="json"?N.responseText:N.response,status:N.status,statusText:N.statusText,headers:L,config:i,request:N};Op(function(z){s(z),x()},function(z){l(z),x()},I),N=null}"onloadend"in N?N.onloadend=M:N.onreadystatechange=function(){!N||N.readyState!==4||N.status===0&&!(N.responseURL&&N.responseURL.indexOf("file:")===0)||setTimeout(M)},N.onabort=function(){N&&(l(new le("Request aborted",le.ECONNABORTED,i,N)),N=null)},N.onerror=function(){l(new le("Network Error",le.ERR_NETWORK,i,N)),N=null},N.ontimeout=function(){let R=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded";const I=u.transitional||Np;u.timeoutErrorMessage&&(R=u.timeoutErrorMessage),l(new le(R,I.clarifyTimeoutError?le.ETIMEDOUT:le.ECONNABORTED,i,N)),N=null},f===void 0&&d.setContentType(null),"setRequestHeader"in N&&A.forEach(d.toJSON(),function(R,I){N.setRequestHeader(I,R)}),A.isUndefined(u.withCredentials)||(N.withCredentials=!!u.withCredentials),p&&p!=="json"&&(N.responseType=u.responseType),v&&([_,U]=Do(v,!0),N.addEventListener("progress",_)),m&&N.upload&&([y,D]=Do(m),N.upload.addEventListener("progress",y),N.upload.addEventListener("loadend",D)),(u.cancelToken||u.signal)&&(g=L=>{N&&(l(!L||L.type?new pr(null,i,N):L),N.abort(),N=null)},u.cancelToken&&u.cancelToken.subscribe(g),u.signal&&(u.signal.aborted?g():u.signal.addEventListener("abort",g)));const T=pw(u.url);if(T&&Qe.protocols.indexOf(T)===-1){l(new le("Unsupported protocol "+T+":",le.ERR_BAD_REQUEST,i));return}N.send(f||null)})},xw=(i,r)=>{const{length:s}=i=i?i.filter(Boolean):[];if(r||s){let l=new AbortController,u;const f=function(v){if(!u){u=!0,p();const g=v instanceof Error?v:this.reason;l.abort(g instanceof le?g:new pr(g instanceof Error?g.message:g))}};let d=r&&setTimeout(()=>{d=null,f(new le(`timeout ${r} of ms exceeded`,le.ETIMEDOUT))},r);const p=()=>{i&&(d&&clearTimeout(d),d=null,i.forEach(v=>{v.unsubscribe?v.unsubscribe(f):v.removeEventListener("abort",f)}),i=null)};i.forEach(v=>v.addEventListener("abort",f));const{signal:m}=l;return m.unsubscribe=()=>A.asap(p),m}},kw=function*(i,r){let s=i.byteLength;if(s<r){yield i;return}let l=0,u;for(;l<s;)u=l+r,yield i.slice(l,u),l=u},Cw=async function*(i,r){for await(const s of Rw(i))yield*kw(s,r)},Rw=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const r=i.getReader();try{for(;;){const{done:s,value:l}=await r.read();if(s)break;yield l}}finally{await r.cancel()}},Bd=(i,r,s,l)=>{const u=Cw(i,r);let f=0,d,p=m=>{d||(d=!0,l&&l(m))};return new ReadableStream({async pull(m){try{const{done:v,value:g}=await u.next();if(v){p(),m.close();return}let y=g.byteLength;if(s){let _=f+=y;s(_)}m.enqueue(new Uint8Array(g))}catch(v){throw p(v),v}},cancel(m){return p(m),u.return()}},{highWaterMark:2})},$o=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ip=$o&&typeof ReadableStream=="function",Nw=$o&&(typeof TextEncoder=="function"?(i=>r=>i.encode(r))(new TextEncoder):async i=>new Uint8Array(await new Response(i).arrayBuffer())),bp=(i,...r)=>{try{return!!i(...r)}catch{return!1}},Tw=Ip&&bp(()=>{let i=!1;const r=new Request(Qe.origin,{body:new ReadableStream,method:"POST",get duplex(){return i=!0,"half"}}).headers.has("Content-Type");return i&&!r}),zd=64*1024,ru=Ip&&bp(()=>A.isReadableStream(new Response("").body)),Fo={stream:ru&&(i=>i.body)};$o&&(i=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Fo[r]&&(Fo[r]=A.isFunction(i[r])?s=>s[r]():(s,l)=>{throw new le(`Response type '${r}' is not supported`,le.ERR_NOT_SUPPORT,l)})})})(new Response);const Pw=async i=>{if(i==null)return 0;if(A.isBlob(i))return i.size;if(A.isSpecCompliantForm(i))return(await new Request(Qe.origin,{method:"POST",body:i}).arrayBuffer()).byteLength;if(A.isArrayBufferView(i)||A.isArrayBuffer(i))return i.byteLength;if(A.isURLSearchParams(i)&&(i=i+""),A.isString(i))return(await Nw(i)).byteLength},Ow=async(i,r)=>{const s=A.toFiniteNumber(i.getContentLength());return s??Pw(r)},jw=$o&&(async i=>{let{url:r,method:s,data:l,signal:u,cancelToken:f,timeout:d,onDownloadProgress:p,onUploadProgress:m,responseType:v,headers:g,withCredentials:y="same-origin",fetchOptions:_}=Lp(i);v=v?(v+"").toLowerCase():"text";let D=xw([u,f&&f.toAbortSignal()],d),U;const x=D&&D.unsubscribe&&(()=>{D.unsubscribe()});let N;try{if(m&&Tw&&s!=="get"&&s!=="head"&&(N=await Ow(g,l))!==0){let I=new Request(r,{method:"POST",body:l,duplex:"half"}),J;if(A.isFormData(l)&&(J=I.headers.get("content-type"))&&g.setContentType(J),I.body){const[z,b]=Dd(N,Do(Fd(m)));l=Bd(I.body,zd,z,b)}}A.isString(y)||(y=y?"include":"omit");const M="credentials"in Request.prototype;U=new Request(r,{..._,signal:D,method:s.toUpperCase(),headers:g.normalize().toJSON(),body:l,duplex:"half",credentials:M?y:void 0});let T=await fetch(U);const L=ru&&(v==="stream"||v==="response");if(ru&&(p||L&&x)){const I={};["status","statusText","headers"].forEach(V=>{I[V]=T[V]});const J=A.toFiniteNumber(T.headers.get("content-length")),[z,b]=p&&Dd(J,Do(Fd(p),!0))||[];T=new Response(Bd(T.body,zd,z,()=>{b&&b(),x&&x()}),I)}v=v||"text";let R=await Fo[A.findKey(Fo,v)||"text"](T,i);return!L&&x&&x(),await new Promise((I,J)=>{Op(I,J,{data:R,headers:lt.from(T.headers),status:T.status,statusText:T.statusText,config:i,request:U})})}catch(M){throw x&&x(),M&&M.name==="TypeError"&&/fetch/i.test(M.message)?Object.assign(new le("Network Error",le.ERR_NETWORK,i,U),{cause:M.cause||M}):le.from(M,M&&M.code,i,U)}}),iu={http:Vy,xhr:_w,fetch:jw};A.forEach(iu,(i,r)=>{if(i){try{Object.defineProperty(i,"name",{value:r})}catch{}Object.defineProperty(i,"adapterName",{value:r})}});const Hd=i=>`- ${i}`,Lw=i=>A.isFunction(i)||i===null||i===!1,Ap={getAdapter:i=>{i=A.isArray(i)?i:[i];const{length:r}=i;let s,l;const u={};for(let f=0;f<r;f++){s=i[f];let d;if(l=s,!Lw(s)&&(l=iu[(d=String(s)).toLowerCase()],l===void 0))throw new le(`Unknown adapter '${d}'`);if(l)break;u[d||"#"+f]=l}if(!l){const f=Object.entries(u).map(([p,m])=>`adapter ${p} `+(m===!1?"is not supported by the environment":"is not available in the build"));let d=r?f.length>1?`since :
`+f.map(Hd).join(`
`):" "+Hd(f[0]):"as no adapter specified";throw new le("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return l},adapters:iu};function Gl(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new pr(null,i)}function Wd(i){return Gl(i),i.headers=lt.from(i.headers),i.data=Kl.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),Ap.getAdapter(i.adapter||fi.adapter)(i).then(function(l){return Gl(i),l.data=Kl.call(i,i.transformResponse,l),l.headers=lt.from(l.headers),l},function(l){return Pp(l)||(Gl(i),l&&l.response&&(l.response.data=Kl.call(i,i.transformResponse,l.response),l.response.headers=lt.from(l.response.headers))),Promise.reject(l)})}const Up="1.8.4",Vo={};["object","boolean","number","function","string","symbol"].forEach((i,r)=>{Vo[i]=function(l){return typeof l===i||"a"+(r<1?"n ":" ")+i}});const qd={};Vo.transitional=function(r,s,l){function u(f,d){return"[Axios v"+Up+"] Transitional option '"+f+"'"+d+(l?". "+l:"")}return(f,d,p)=>{if(r===!1)throw new le(u(d," has been removed"+(s?" in "+s:"")),le.ERR_DEPRECATED);return s&&!qd[d]&&(qd[d]=!0,console.warn(u(d," has been deprecated since v"+s+" and will be removed in the near future"))),r?r(f,d,p):!0}};Vo.spelling=function(r){return(s,l)=>(console.warn(`${l} is likely a misspelling of ${r}`),!0)};function Iw(i,r,s){if(typeof i!="object")throw new le("options must be an object",le.ERR_BAD_OPTION_VALUE);const l=Object.keys(i);let u=l.length;for(;u-- >0;){const f=l[u],d=r[f];if(d){const p=i[f],m=p===void 0||d(p,f,i);if(m!==!0)throw new le("option "+f+" must be "+m,le.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new le("Unknown option "+f,le.ERR_BAD_OPTION)}}const Ao={assertOptions:Iw,validators:Vo},Ut=Ao.validators;let An=class{constructor(r){this.defaults=r,this.interceptors={request:new Ad,response:new Ad}}async request(r,s){try{return await this._request(r,s)}catch(l){if(l instanceof Error){let u={};Error.captureStackTrace?Error.captureStackTrace(u):u=new Error;const f=u.stack?u.stack.replace(/^.+\n/,""):"";try{l.stack?f&&!String(l.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(l.stack+=`
`+f):l.stack=f}catch{}}throw l}}_request(r,s){typeof r=="string"?(s=s||{},s.url=r):s=r||{},s=Un(this.defaults,s);const{transitional:l,paramsSerializer:u,headers:f}=s;l!==void 0&&Ao.assertOptions(l,{silentJSONParsing:Ut.transitional(Ut.boolean),forcedJSONParsing:Ut.transitional(Ut.boolean),clarifyTimeoutError:Ut.transitional(Ut.boolean)},!1),u!=null&&(A.isFunction(u)?s.paramsSerializer={serialize:u}:Ao.assertOptions(u,{encode:Ut.function,serialize:Ut.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Ao.assertOptions(s,{baseUrl:Ut.spelling("baseURL"),withXsrfToken:Ut.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let d=f&&A.merge(f.common,f[s.method]);f&&A.forEach(["delete","get","head","post","put","patch","common"],U=>{delete f[U]}),s.headers=lt.concat(d,f);const p=[];let m=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(s)===!1||(m=m&&x.synchronous,p.unshift(x.fulfilled,x.rejected))});const v=[];this.interceptors.response.forEach(function(x){v.push(x.fulfilled,x.rejected)});let g,y=0,_;if(!m){const U=[Wd.bind(this),void 0];for(U.unshift.apply(U,p),U.push.apply(U,v),_=U.length,g=Promise.resolve(s);y<_;)g=g.then(U[y++],U[y++]);return g}_=p.length;let D=s;for(y=0;y<_;){const U=p[y++],x=p[y++];try{D=U(D)}catch(N){x.call(this,N);break}}try{g=Wd.call(this,D)}catch(U){return Promise.reject(U)}for(y=0,_=v.length;y<_;)g=g.then(v[y++],v[y++]);return g}getUri(r){r=Un(this.defaults,r);const s=jp(r.baseURL,r.url,r.allowAbsoluteUrls);return Rp(s,r.params,r.paramsSerializer)}};A.forEach(["delete","get","head","options"],function(r){An.prototype[r]=function(s,l){return this.request(Un(l||{},{method:r,url:s,data:(l||{}).data}))}});A.forEach(["post","put","patch"],function(r){function s(l){return function(f,d,p){return this.request(Un(p||{},{method:r,headers:l?{"Content-Type":"multipart/form-data"}:{},url:f,data:d}))}}An.prototype[r]=s(),An.prototype[r+"Form"]=s(!0)});let bw=class Dp{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(f){s=f});const l=this;this.promise.then(u=>{if(!l._listeners)return;let f=l._listeners.length;for(;f-- >0;)l._listeners[f](u);l._listeners=null}),this.promise.then=u=>{let f;const d=new Promise(p=>{l.subscribe(p),f=p}).then(u);return d.cancel=function(){l.unsubscribe(f)},d},r(function(f,d,p){l.reason||(l.reason=new pr(f,d,p),s(l.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const s=this._listeners.indexOf(r);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const r=new AbortController,s=l=>{r.abort(l)};return this.subscribe(s),r.signal.unsubscribe=()=>this.unsubscribe(s),r.signal}static source(){let r;return{token:new Dp(function(u){r=u}),cancel:r}}};function Aw(i){return function(s){return i.apply(null,s)}}function Uw(i){return A.isObject(i)&&i.isAxiosError===!0}const ou={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ou).forEach(([i,r])=>{ou[r]=i});function Fp(i){const r=new An(i),s=mp(An.prototype.request,r);return A.extend(s,An.prototype,r,{allOwnKeys:!0}),A.extend(s,r,null,{allOwnKeys:!0}),s.create=function(u){return Fp(Un(i,u))},s}const xe=Fp(fi);xe.Axios=An;xe.CanceledError=pr;xe.CancelToken=bw;xe.isCancel=Pp;xe.VERSION=Up;xe.toFormData=qo;xe.AxiosError=le;xe.Cancel=xe.CanceledError;xe.all=function(r){return Promise.all(r)};xe.spread=Aw;xe.isAxiosError=Uw;xe.mergeConfig=Un;xe.AxiosHeaders=lt;xe.formToJSON=i=>Tp(A.isHTMLForm(i)?new FormData(i):i);xe.getAdapter=Ap.getAdapter;xe.HttpStatusCode=ou;xe.default=xe;const{Axios:W0,AxiosError:q0,CanceledError:$0,isCancel:V0,CancelToken:X0,VERSION:J0,all:Q0,Cancel:K0,isAxiosError:G0,spread:Y0,toFormData:Z0,AxiosHeaders:e1,HttpStatusCode:t1,formToJSON:n1,getAdapter:r1,mergeConfig:i1}=xe;let Mp=!1;try{Mp=localStorage.getItem("useMockServices")==="true"}catch(i){console.error("Error accessing localStorage:",i)}const Ke=()=>Mp||!1,su=[{id:1,name:"Test User",email:"<EMAIL>",username:"testuser"},{id:2,name:"Jane Doe",email:"<EMAIL>",username:"janedoe"},{id:3,name:"John Smith",email:"<EMAIL>",username:"johnsmith"}],lu=[{id:1,sender:"janedoe",content:"Hello everyone!",timestamp:new Date(Date.now()-36e5).toISOString()},{id:2,sender:"johnsmith",content:"Hi Jane, how are you doing?",timestamp:new Date(Date.now()-35e5).toISOString()},{id:3,sender:"testuser",content:"Hello Jane and John! Nice to meet you both.",timestamp:new Date(Date.now()-34e5).toISOString()},{id:4,sender:"janedoe",content:"I'm doing great, thanks for asking!",timestamp:new Date(Date.now()-33e5).toISOString()},{id:5,sender:"johnsmith",content:"What are you all working on today?",timestamp:new Date(Date.now()-32e5).toISOString()},{id:6,sender:"testuser",content:"I'm building this chat application. It's coming along nicely!",timestamp:new Date(Date.now()-31e5).toISOString()}],Dw=i=>su.find(r=>r.username===i)||su[0],$d=()=>[...lu],Vd=i=>{const r={...i,id:lu.length+1};return lu.push(r),r},Xo="/api/auth",Fw=async(i,r)=>{try{if(Ke()){console.log("Using mock login service"),await new Promise(u=>setTimeout(u,500));const l=Dw(i);return{token:"mock-jwt-token-"+Math.random().toString(36).substring(2),user:{...l,username:i}}}return(await xe.post(`${Xo}/login`,{username:i,password:r})).data}catch(s){throw console.error("Login error:",s),s}},Mw=async(i,r,s,l)=>{try{return Ke()?(console.log("Using mock register service"),await new Promise(f=>setTimeout(f,800)),{token:"mock-jwt-token-"+Math.random().toString(36).substring(2),user:{id:Math.floor(Math.random()*1e3)+10,name:i,email:r,username:s}}):(await xe.post(`${Xo}/register`,{name:i,email:r,username:s,password:l})).data}catch(u){throw console.error("Registration error:",u),u}},Bw=async i=>{try{if(Ke()){console.log("Using mock password reset request service"),await new Promise(r=>setTimeout(r,600));return}await xe.post(`${Xo}/forgot-password`,{email:i})}catch(r){throw console.error("Password reset request error:",r),r}},zw=async(i,r)=>{try{if(Ke()){console.log("Using mock password reset service"),await new Promise(s=>setTimeout(s,700));return}await xe.post(`${Xo}/reset-password`,{token:i,newPassword:r})}catch(s){throw console.error("Password reset error:",s),s}},Hw={user:null,token:null,isAuthenticated:!1,loading:!0,login:async()=>{},register:async()=>{},logout:()=>{},error:null},mr=F.createContext(Hw),Ww=({children:i})=>{const[r,s]=F.useState(null),[l,u]=F.useState(null),[f,d]=F.useState(!0),[p,m]=F.useState(null);F.useEffect(()=>{const D=localStorage.getItem("token"),U=localStorage.getItem("user");if(D&&U)try{u(D),s(JSON.parse(U))}catch(x){console.error("Error parsing saved user:",x),localStorage.removeItem("token"),localStorage.removeItem("user")}d(!1)},[]);const _={user:r,token:l,isAuthenticated:!!l,loading:f,login:async(D,U)=>{try{d(!0),m(null);const x=await Fw(D,U);s(x.user),u(x.token),localStorage.setItem("token",x.token),localStorage.setItem("user",JSON.stringify(x.user))}catch(x){m("Login failed. Please check your credentials."),console.error("Login error:",x)}finally{d(!1)}},register:async(D,U,x,N)=>{try{d(!0),m(null);const M=await Mw(D,U,x,N);s(M.user),u(M.token),localStorage.setItem("token",M.token),localStorage.setItem("user",JSON.stringify(M.user))}catch(M){m("Registration failed. Please try again."),console.error("Registration error:",M)}finally{d(!1)}},logout:()=>{s(null),u(null),localStorage.removeItem("token"),localStorage.removeItem("user")},error:p};return P.jsx(mr.Provider,{value:_,children:i})},qw=()=>{const[i,r]=F.useState(""),[s,l]=F.useState(""),[u,f]=F.useState(""),{login:d,error:p,loading:m,isAuthenticated:v}=F.useContext(mr),g=dr();uu.useEffect(()=>{v&&g("/chat")},[v,g]);const y=async _=>{if(_.preventDefault(),f(""),!i.trim()||!s.trim()){f("Please enter both username and password");return}await d(i,s)};return P.jsx("div",{className:"auth-container",children:P.jsxs("div",{className:"auth-card",children:[P.jsx("h2",{children:"Login to MyChatApp"}),P.jsxs("form",{onSubmit:y,children:[P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"username",children:"Username"}),P.jsx("input",{type:"text",id:"username",value:i,onChange:_=>r(_.target.value),disabled:m})]}),P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"password",children:"Password"}),P.jsx("input",{type:"password",id:"password",value:s,onChange:_=>l(_.target.value),disabled:m})]}),(u||p)&&P.jsx("div",{className:"error-message",children:u||p}),P.jsx("button",{type:"submit",disabled:m,className:"auth-button",children:m?"Logging in...":"Login"})]}),P.jsxs("div",{className:"auth-footer",children:["Don't have an account? ",P.jsx(St,{to:"/register",children:"Register"})]}),P.jsx("div",{className:"auth-footer forgot-password",children:P.jsx(St,{to:"/forgot-password",children:"Forgot Password?"})})]})})},$w=()=>{const[i,r]=F.useState(""),[s,l]=F.useState(""),[u,f]=F.useState(""),[d,p]=F.useState(""),[m,v]=F.useState(""),[g,y]=F.useState(""),{register:_,error:D,loading:U,isAuthenticated:x}=F.useContext(mr),N=dr();uu.useEffect(()=>{x&&N("/chat")},[x,N]);const M=L=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(L),T=async L=>{if(L.preventDefault(),y(""),!i.trim()||!s.trim()||!u.trim()||!d.trim()){y("All fields are required");return}if(!M(s)){y("Please enter a valid email address");return}if(d!==m){y("Passwords do not match");return}if(d.length<6){y("Password must be at least 6 characters long");return}await _(i,s,u,d)};return P.jsx("div",{className:"auth-container",children:P.jsxs("div",{className:"auth-card",children:[P.jsx("h2",{children:"Create an Account"}),P.jsxs("form",{onSubmit:T,children:[P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"name",children:"Full Name"}),P.jsx("input",{type:"text",id:"name",value:i,onChange:L=>r(L.target.value),disabled:U})]}),P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"email",children:"Email"}),P.jsx("input",{type:"email",id:"email",value:s,onChange:L=>l(L.target.value),disabled:U})]}),P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"username",children:"Username"}),P.jsx("input",{type:"text",id:"username",value:u,onChange:L=>f(L.target.value),disabled:U})]}),P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"password",children:"Password"}),P.jsx("input",{type:"password",id:"password",value:d,onChange:L=>p(L.target.value),disabled:U})]}),P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"confirmPassword",children:"Confirm Password"}),P.jsx("input",{type:"password",id:"confirmPassword",value:m,onChange:L=>v(L.target.value),disabled:U})]}),(g||D)&&P.jsx("div",{className:"error-message",children:g||D}),P.jsx("button",{type:"submit",disabled:U,className:"auth-button",children:U?"Creating Account...":"Register"})]}),P.jsxs("div",{className:"auth-footer",children:["Already have an account? ",P.jsx(St,{to:"/login",children:"Login"})]})]})})},Vw=()=>{const[i,r]=F.useState(""),[s,l]=F.useState(!1),[u,f]=F.useState(null),[d,p]=F.useState(!1),m=g=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g),v=async g=>{if(g.preventDefault(),f(null),!i.trim()){f("Please enter your email address");return}if(!m(i)){f("Please enter a valid email address");return}try{l(!0),await Bw(i),p(!0)}catch(y){console.error("Password reset request error:",y),f("Failed to send password reset email. Please try again later.")}finally{l(!1)}};return P.jsx("div",{className:"auth-container",children:P.jsxs("div",{className:"auth-card",children:[P.jsx("h2",{children:"Forgot Password"}),d?P.jsxs("div",{className:"success-message",children:[P.jsx("p",{children:"Password reset instructions have been sent to your email."}),P.jsx("p",{children:"Please check your inbox and follow the instructions to reset your password."}),P.jsx("div",{className:"auth-footer",children:P.jsx(St,{to:"/login",children:"Return to Login"})})]}):P.jsxs("form",{onSubmit:v,children:[P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"email",children:"Email Address"}),P.jsx("input",{type:"email",id:"email",value:i,onChange:g=>r(g.target.value),disabled:s,placeholder:"Enter your registered email"})]}),u&&P.jsx("div",{className:"error-message",children:u}),P.jsx("button",{type:"submit",disabled:s,className:"auth-button",children:s?"Sending...":"Send Reset Link"}),P.jsx("div",{className:"auth-footer",children:P.jsx(St,{to:"/login",children:"Back to Login"})})]})]})})},Xw=()=>{const[i,r]=F.useState(""),[s,l]=F.useState(""),[u,f]=F.useState(""),[d,p]=F.useState(!1),[m,v]=F.useState(null),[g,y]=F.useState(!1),_=dr(),D=fr();F.useEffect(()=>{const N=new URLSearchParams(D.search).get("token");N?f(N):v("Invalid or missing reset token. Please request a new password reset link.")},[D]);const U=async x=>{if(x.preventDefault(),v(null),!i.trim()||!s.trim()){v("Please enter both password fields");return}if(i!==s){v("Passwords do not match");return}if(i.length<6){v("Password must be at least 6 characters long");return}if(!u){v("Invalid reset token. Please request a new password reset link.");return}try{p(!0),await zw(u,i),y(!0),setTimeout(()=>{_("/login")},3e3)}catch(N){console.error("Password reset error:",N),v("Failed to reset password. The link may have expired or is invalid.")}finally{p(!1)}};return P.jsx("div",{className:"auth-container",children:P.jsxs("div",{className:"auth-card",children:[P.jsx("h2",{children:"Reset Password"}),g?P.jsxs("div",{className:"success-message",children:[P.jsx("p",{children:"Your password has been successfully reset!"}),P.jsx("p",{children:"You will be redirected to the login page shortly..."}),P.jsx("div",{className:"auth-footer",children:P.jsx(St,{to:"/login",children:"Go to Login"})})]}):P.jsxs("form",{onSubmit:U,children:[P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"password",children:"New Password"}),P.jsx("input",{type:"password",id:"password",value:i,onChange:x=>r(x.target.value),disabled:d||!u,placeholder:"Enter new password"})]}),P.jsxs("div",{className:"form-group",children:[P.jsx("label",{htmlFor:"confirmPassword",children:"Confirm New Password"}),P.jsx("input",{type:"password",id:"confirmPassword",value:s,onChange:x=>l(x.target.value),disabled:d||!u,placeholder:"Confirm new password"})]}),m&&P.jsx("div",{className:"error-message",children:m}),P.jsx("button",{type:"submit",disabled:d||!u,className:"auth-button",children:d?"Resetting...":"Reset Password"}),P.jsx("div",{className:"auth-footer",children:P.jsx(St,{to:"/login",children:"Back to Login"})})]})]})})},Bp="/messages",Jw=async()=>{try{return Ke()?(console.log("Using mock messages service"),await new Promise(r=>setTimeout(r,600)),$d()):(await xe.get(Bp)).data}catch(i){return console.error("Error fetching messages:",i),Ke()?$d():[]}},Qw=async i=>{try{return Ke()?(console.log("Using mock send message service"),await new Promise(s=>setTimeout(s,300)),Vd(i)):(await xe.post(Bp,i)).data}catch(r){if(console.error("Error sending message:",r),Ke())return Vd(i);throw r}};var Yl={exports:{}},No={},Xd;function Kw(){return Xd||(Xd=1,window.crypto&&window.crypto.getRandomValues?No.randomBytes=function(i){var r=new Uint8Array(i);return window.crypto.getRandomValues(r),r}:No.randomBytes=function(i){for(var r=new Array(i),s=0;s<i;s++)r[s]=Math.floor(Math.random()*256);return r}),No}var Zl,Jd;function vr(){if(Jd)return Zl;Jd=1;var i=Kw(),r="abcdefghijklmnopqrstuvwxyz012345";return Zl={string:function(s){for(var l=r.length,u=i.randomBytes(s),f=[],d=0;d<s;d++)f.push(r.substr(u[d]%l,1));return f.join("")},number:function(s){return Math.floor(Math.random()*s)},numberString:function(s){var l=(""+(s-1)).length,u=new Array(l+1).join("0");return(u+this.number(s)).slice(-l)}},Zl}var Qd;function wn(){return Qd||(Qd=1,function(i){var r=vr(),s={},l=!1,u=window.chrome&&window.chrome.app&&window.chrome.app.runtime;i.exports={attachEvent:function(d,p){typeof window.addEventListener<"u"?window.addEventListener(d,p,!1):window.document&&window.attachEvent&&(window.document.attachEvent("on"+d,p),window.attachEvent("on"+d,p))},detachEvent:function(d,p){typeof window.addEventListener<"u"?window.removeEventListener(d,p,!1):window.document&&window.detachEvent&&(window.document.detachEvent("on"+d,p),window.detachEvent("on"+d,p))},unloadAdd:function(d){if(u)return null;var p=r.string(8);return s[p]=d,l&&setTimeout(this.triggerUnloadCallbacks,0),p},unloadDel:function(d){d in s&&delete s[d]},triggerUnloadCallbacks:function(){for(var d in s)s[d](),delete s[d]}};var f=function(){l||(l=!0,i.exports.triggerUnloadCallbacks())};u||i.exports.attachEvent("unload",f)}(Yl)),Yl.exports}var ea,Kd;function Gw(){return Kd||(Kd=1,ea=function(r,s){if(s=s.split(":")[0],r=+r,!r)return!1;switch(s){case"http":case"ws":return r!==80;case"https":case"wss":return r!==443;case"ftp":return r!==21;case"gopher":return r!==70;case"file":return!1}return r!==0}),ea}var To={},Gd;function Yw(){if(Gd)return To;Gd=1;var i=Object.prototype.hasOwnProperty,r;function s(d){try{return decodeURIComponent(d.replace(/\+/g," "))}catch{return null}}function l(d){try{return encodeURIComponent(d)}catch{return null}}function u(d){for(var p=/([^=?#&]+)=?([^&]*)/g,m={},v;v=p.exec(d);){var g=s(v[1]),y=s(v[2]);g===null||y===null||g in m||(m[g]=y)}return m}function f(d,p){p=p||"";var m=[],v,g;typeof p!="string"&&(p="?");for(g in d)if(i.call(d,g)){if(v=d[g],!v&&(v===null||v===r||isNaN(v))&&(v=""),g=l(g),v=l(v),g===null||v===null)continue;m.push(g+"="+v)}return m.length?p+m.join("&"):""}return To.stringify=f,To.parse=u,To}var ta,Yd;function zp(){if(Yd)return ta;Yd=1;var i=Gw(),r=Yw(),s=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,l=/[\n\r\t]/g,u=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,f=/:\d+$/,d=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,p=/^[a-zA-Z]:/;function m(T){return(T||"").toString().replace(s,"")}var v=[["#","hash"],["?","query"],function(L,R){return _(R.protocol)?L.replace(/\\/g,"/"):L},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],g={hash:1,query:1};function y(T){var L;typeof window<"u"||typeof window<"u"?L=window:typeof self<"u"?L=self:L={};var R=L.location||{};T=T||R;var I={},J=typeof T,z;if(T.protocol==="blob:")I=new x(unescape(T.pathname),{});else if(J==="string"){I=new x(T,{});for(z in g)delete I[z]}else if(J==="object"){for(z in T)z in g||(I[z]=T[z]);I.slashes===void 0&&(I.slashes=u.test(T.href))}return I}function _(T){return T==="file:"||T==="ftp:"||T==="http:"||T==="https:"||T==="ws:"||T==="wss:"}function D(T,L){T=m(T),T=T.replace(l,""),L=L||{};var R=d.exec(T),I=R[1]?R[1].toLowerCase():"",J=!!R[2],z=!!R[3],b=0,V;return J?z?(V=R[2]+R[3]+R[4],b=R[2].length+R[3].length):(V=R[2]+R[4],b=R[2].length):z?(V=R[3]+R[4],b=R[3].length):V=R[4],I==="file:"?b>=2&&(V=V.slice(2)):_(I)?V=R[4]:I?J&&(V=V.slice(2)):b>=2&&_(L.protocol)&&(V=R[4]),{protocol:I,slashes:J||_(I),slashesCount:b,rest:V}}function U(T,L){if(T==="")return L;for(var R=(L||"/").split("/").slice(0,-1).concat(T.split("/")),I=R.length,J=R[I-1],z=!1,b=0;I--;)R[I]==="."?R.splice(I,1):R[I]===".."?(R.splice(I,1),b++):b&&(I===0&&(z=!0),R.splice(I,1),b--);return z&&R.unshift(""),(J==="."||J==="..")&&R.push(""),R.join("/")}function x(T,L,R){if(T=m(T),T=T.replace(l,""),!(this instanceof x))return new x(T,L,R);var I,J,z,b,V,ne,ae=v.slice(),ge=typeof L,X=this,je=0;for(ge!=="object"&&ge!=="string"&&(R=L,L=null),R&&typeof R!="function"&&(R=r.parse),L=y(L),J=D(T||"",L),I=!J.protocol&&!J.slashes,X.slashes=J.slashes||I&&L.slashes,X.protocol=J.protocol||L.protocol||"",T=J.rest,(J.protocol==="file:"&&(J.slashesCount!==2||p.test(T))||!J.slashes&&(J.protocol||J.slashesCount<2||!_(X.protocol)))&&(ae[3]=[/(.*)/,"pathname"]);je<ae.length;je++){if(b=ae[je],typeof b=="function"){T=b(T,X);continue}z=b[0],ne=b[1],z!==z?X[ne]=T:typeof z=="string"?(V=z==="@"?T.lastIndexOf(z):T.indexOf(z),~V&&(typeof b[2]=="number"?(X[ne]=T.slice(0,V),T=T.slice(V+b[2])):(X[ne]=T.slice(V),T=T.slice(0,V)))):(V=z.exec(T))&&(X[ne]=V[1],T=T.slice(0,V.index)),X[ne]=X[ne]||I&&b[3]&&L[ne]||"",b[4]&&(X[ne]=X[ne].toLowerCase())}R&&(X.query=R(X.query)),I&&L.slashes&&X.pathname.charAt(0)!=="/"&&(X.pathname!==""||L.pathname!=="")&&(X.pathname=U(X.pathname,L.pathname)),X.pathname.charAt(0)!=="/"&&_(X.protocol)&&(X.pathname="/"+X.pathname),i(X.port,X.protocol)||(X.host=X.hostname,X.port=""),X.username=X.password="",X.auth&&(V=X.auth.indexOf(":"),~V?(X.username=X.auth.slice(0,V),X.username=encodeURIComponent(decodeURIComponent(X.username)),X.password=X.auth.slice(V+1),X.password=encodeURIComponent(decodeURIComponent(X.password))):X.username=encodeURIComponent(decodeURIComponent(X.auth)),X.auth=X.password?X.username+":"+X.password:X.username),X.origin=X.protocol!=="file:"&&_(X.protocol)&&X.host?X.protocol+"//"+X.host:"null",X.href=X.toString()}function N(T,L,R){var I=this;switch(T){case"query":typeof L=="string"&&L.length&&(L=(R||r.parse)(L)),I[T]=L;break;case"port":I[T]=L,i(L,I.protocol)?L&&(I.host=I.hostname+":"+L):(I.host=I.hostname,I[T]="");break;case"hostname":I[T]=L,I.port&&(L+=":"+I.port),I.host=L;break;case"host":I[T]=L,f.test(L)?(L=L.split(":"),I.port=L.pop(),I.hostname=L.join(":")):(I.hostname=L,I.port="");break;case"protocol":I.protocol=L.toLowerCase(),I.slashes=!R;break;case"pathname":case"hash":if(L){var J=T==="pathname"?"/":"#";I[T]=L.charAt(0)!==J?J+L:L}else I[T]=L;break;case"username":case"password":I[T]=encodeURIComponent(L);break;case"auth":var z=L.indexOf(":");~z?(I.username=L.slice(0,z),I.username=encodeURIComponent(decodeURIComponent(I.username)),I.password=L.slice(z+1),I.password=encodeURIComponent(decodeURIComponent(I.password))):I.username=encodeURIComponent(decodeURIComponent(L))}for(var b=0;b<v.length;b++){var V=v[b];V[4]&&(I[V[1]]=I[V[1]].toLowerCase())}return I.auth=I.password?I.username+":"+I.password:I.username,I.origin=I.protocol!=="file:"&&_(I.protocol)&&I.host?I.protocol+"//"+I.host:"null",I.href=I.toString(),I}function M(T){(!T||typeof T!="function")&&(T=r.stringify);var L,R=this,I=R.host,J=R.protocol;J&&J.charAt(J.length-1)!==":"&&(J+=":");var z=J+(R.protocol&&R.slashes||_(R.protocol)?"//":"");return R.username?(z+=R.username,R.password&&(z+=":"+R.password),z+="@"):R.password?(z+=":"+R.password,z+="@"):R.protocol!=="file:"&&_(R.protocol)&&!I&&R.pathname!=="/"&&(z+="@"),(I[I.length-1]===":"||f.test(R.hostname)&&!R.port)&&(I+=":"),z+=I+R.pathname,L=typeof R.query=="object"?T(R.query):R.query,L&&(z+=L.charAt(0)!=="?"?"?"+L:L),R.hash&&(z+=R.hash),z}return x.prototype={set:N,toString:M},x.extractProtocol=D,x.location=y,x.trimLeft=m,x.qs=r,ta=x,ta}var na,Zd;function Et(){if(Zd)return na;Zd=1;var i=zp();return na={getOrigin:function(r){if(!r)return null;var s=new i(r);if(s.protocol==="file:")return null;var l=s.port;return l||(l=s.protocol==="https:"?"443":"80"),s.protocol+"//"+s.hostname+":"+l},isOriginEqual:function(r,s){var l=this.getOrigin(r)===this.getOrigin(s);return l},isSchemeEqual:function(r,s){return r.split(":")[0]===s.split(":")[0]},addPath:function(r,s){var l=r.split("?");return l[0]+s+(l[1]?"?"+l[1]:"")},addQuery:function(r,s){return r+(r.indexOf("?")===-1?"?"+s:"&"+s)},isLoopbackAddr:function(r){return/^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(r)||/^\[::1\]$/.test(r)}},na}var Po={exports:{}},eh;function ve(){return eh||(eh=1,typeof Object.create=="function"?Po.exports=function(r,s){s&&(r.super_=s,r.prototype=Object.create(s.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}))}:Po.exports=function(r,s){if(s){r.super_=s;var l=function(){};l.prototype=s.prototype,r.prototype=new l,r.prototype.constructor=r}}),Po.exports}var ra={},ia,th;function Hp(){if(th)return ia;th=1;function i(){this._listeners={}}return i.prototype.addEventListener=function(r,s){r in this._listeners||(this._listeners[r]=[]);var l=this._listeners[r];l.indexOf(s)===-1&&(l=l.concat([s])),this._listeners[r]=l},i.prototype.removeEventListener=function(r,s){var l=this._listeners[r];if(l){var u=l.indexOf(s);if(u!==-1){l.length>1?this._listeners[r]=l.slice(0,u).concat(l.slice(u+1)):delete this._listeners[r];return}}},i.prototype.dispatchEvent=function(){var r=arguments[0],s=r.type,l=arguments.length===1?[r]:Array.apply(null,arguments);if(this["on"+s]&&this["on"+s].apply(this,l),s in this._listeners)for(var u=this._listeners[s],f=0;f<u.length;f++)u[f].apply(this,l)},ia=i,ia}var nh;function et(){if(nh)return ra;nh=1;var i=ve(),r=Hp();function s(){r.call(this)}return i(s,r),s.prototype.removeAllListeners=function(l){l?delete this._listeners[l]:this._listeners={}},s.prototype.once=function(l,u){var f=this,d=!1;function p(){f.removeListener(l,p),d||(d=!0,u.apply(this,arguments))}this.on(l,p)},s.prototype.emit=function(){var l=arguments[0],u=this._listeners[l];if(u){for(var f=arguments.length,d=new Array(f-1),p=1;p<f;p++)d[p-1]=arguments[p];for(var m=0;m<u.length;m++)u[m].apply(this,d)}},s.prototype.on=s.prototype.addListener=r.prototype.addEventListener,s.prototype.removeListener=r.prototype.removeEventListener,ra.EventEmitter=s,ra}var Oo={exports:{}},rh;function Zw(){if(rh)return Oo.exports;rh=1;var i=window.WebSocket||window.MozWebSocket;return i?Oo.exports=function(s){return new i(s)}:Oo.exports=void 0,Oo.exports}var oa,ih;function e0(){if(ih)return oa;ih=1;var i=wn(),r=Et(),s=ve(),l=et().EventEmitter,u=Zw(),f=function(){};function d(p,m,v){if(!d.enabled())throw new Error("Transport created when disabled");l.call(this);var g=this,y=r.addPath(p,"/websocket");y.slice(0,5)==="https"?y="wss"+y.slice(5):y="ws"+y.slice(4),this.url=y,this.ws=new u(this.url,[],v),this.ws.onmessage=function(_){f("message event",_.data),g.emit("message",_.data)},this.unloadRef=i.unloadAdd(function(){g.ws.close()}),this.ws.onclose=function(_){f("close event",_.code,_.reason),g.emit("close",_.code,_.reason),g._cleanup()},this.ws.onerror=function(_){g.emit("close",1006,"WebSocket connection broken"),g._cleanup()}}return s(d,l),d.prototype.send=function(p){var m="["+p+"]";this.ws.send(m)},d.prototype.close=function(){var p=this.ws;this._cleanup(),p&&p.close()},d.prototype._cleanup=function(){var p=this.ws;p&&(p.onmessage=p.onclose=p.onerror=null),i.unloadDel(this.unloadRef),this.unloadRef=this.ws=null,this.removeAllListeners()},d.enabled=function(){return!!u},d.transportName="websocket",d.roundTrips=2,oa=d,oa}var sa,oh;function t0(){if(oh)return sa;oh=1;var i=ve(),r=et().EventEmitter,s=function(){};function l(u,f){r.call(this),this.sendBuffer=[],this.sender=f,this.url=u}return i(l,r),l.prototype.send=function(u){this.sendBuffer.push(u),this.sendStop||this.sendSchedule()},l.prototype.sendScheduleWait=function(){var u=this,f;this.sendStop=function(){u.sendStop=null,clearTimeout(f)},f=setTimeout(function(){u.sendStop=null,u.sendSchedule()},25)},l.prototype.sendSchedule=function(){s("sendSchedule",this.sendBuffer.length);var u=this;if(this.sendBuffer.length>0){var f="["+this.sendBuffer.join(",")+"]";this.sendStop=this.sender(this.url,f,function(d){u.sendStop=null,d?(u.emit("close",d.code||1006,"Sending error: "+d),u.close()):u.sendScheduleWait()}),this.sendBuffer=[]}},l.prototype._cleanup=function(){this.removeAllListeners()},l.prototype.close=function(){this._cleanup(),this.sendStop&&(this.sendStop(),this.sendStop=null)},sa=l,sa}var la,sh;function n0(){if(sh)return la;sh=1;var i=ve(),r=et().EventEmitter,s=function(){};function l(u,f,d){r.call(this),this.Receiver=u,this.receiveUrl=f,this.AjaxObject=d,this._scheduleReceiver()}return i(l,r),l.prototype._scheduleReceiver=function(){var u=this,f=this.poll=new this.Receiver(this.receiveUrl,this.AjaxObject);f.on("message",function(d){u.emit("message",d)}),f.once("close",function(d,p){s("close",d,p,u.pollIsClosing),u.poll=f=null,u.pollIsClosing||(p==="network"?u._scheduleReceiver():(u.emit("close",d||1006,p),u.removeAllListeners()))})},l.prototype.abort=function(){this.removeAllListeners(),this.pollIsClosing=!0,this.poll&&this.poll.abort()},la=l,la}var aa,lh;function Wp(){if(lh)return aa;lh=1;var i=ve(),r=Et(),s=t0(),l=n0();function u(f,d,p,m,v){var g=r.addPath(f,d),y=this;s.call(this,f,p),this.poll=new l(m,g,v),this.poll.on("message",function(_){y.emit("message",_)}),this.poll.once("close",function(_,D){y.poll=null,y.emit("close",_,D),y.close()})}return i(u,s),u.prototype.close=function(){s.prototype.close.call(this),this.removeAllListeners(),this.poll&&(this.poll.abort(),this.poll=null)},aa=u,aa}var ua,ah;function gr(){if(ah)return ua;ah=1;var i=ve(),r=Et(),s=Wp();function l(f){return function(d,p,m){var v={};typeof p=="string"&&(v.headers={"Content-type":"text/plain"});var g=r.addPath(d,"/xhr_send"),y=new f("POST",g,p,v);return y.once("finish",function(_){if(y=null,_!==200&&_!==204)return m(new Error("http status "+_));m()}),function(){y.close(),y=null;var _=new Error("Aborted");_.code=1e3,m(_)}}}function u(f,d,p,m){s.call(this,f,d,l(m),p,m)}return i(u,s),ua=u,ua}var ca,uh;function Jo(){if(uh)return ca;uh=1;var i=ve(),r=et().EventEmitter;function s(l,u){r.call(this);var f=this;this.bufferPosition=0,this.xo=new u("POST",l,null),this.xo.on("chunk",this._chunkHandler.bind(this)),this.xo.once("finish",function(d,p){f._chunkHandler(d,p),f.xo=null;var m=d===200?"network":"permanent";f.emit("close",null,m),f._cleanup()})}return i(s,r),s.prototype._chunkHandler=function(l,u){if(!(l!==200||!u))for(var f=-1;;this.bufferPosition+=f+1){var d=u.slice(this.bufferPosition);if(f=d.indexOf(`
`),f===-1)break;var p=d.slice(0,f);p&&this.emit("message",p)}},s.prototype._cleanup=function(){this.removeAllListeners()},s.prototype.abort=function(){this.xo&&(this.xo.close(),this.emit("close",null,"user"),this.xo=null),this._cleanup()},ca=s,ca}var fa,ch;function qp(){if(ch)return fa;ch=1;var i=et().EventEmitter,r=ve(),s=wn(),l=Et(),u=window.XMLHttpRequest,f=function(){};function d(v,g,y,_){var D=this;i.call(this),setTimeout(function(){D._start(v,g,y,_)},0)}r(d,i),d.prototype._start=function(v,g,y,_){var D=this;try{this.xhr=new u}catch{}if(!this.xhr){this.emit("finish",0,"no xhr support"),this._cleanup();return}g=l.addQuery(g,"t="+ +new Date),this.unloadRef=s.unloadAdd(function(){D._cleanup(!0)});try{this.xhr.open(v,g,!0),this.timeout&&"timeout"in this.xhr&&(this.xhr.timeout=this.timeout,this.xhr.ontimeout=function(){f("xhr timeout"),D.emit("finish",0,""),D._cleanup(!1)})}catch{this.emit("finish",0,""),this._cleanup(!1);return}if((!_||!_.noCredentials)&&d.supportsCORS&&(this.xhr.withCredentials=!0),_&&_.headers)for(var U in _.headers)this.xhr.setRequestHeader(U,_.headers[U]);this.xhr.onreadystatechange=function(){if(D.xhr){var x=D.xhr,N,M;switch(f("readyState",x.readyState),x.readyState){case 3:try{M=x.status,N=x.responseText}catch{}M===1223&&(M=204),M===200&&N&&N.length>0&&D.emit("chunk",M,N);break;case 4:M=x.status,M===1223&&(M=204),(M===12005||M===12029)&&(M=0),f("finish",M,x.responseText),D.emit("finish",M,x.responseText),D._cleanup(!1);break}}};try{D.xhr.send(y)}catch{D.emit("finish",0,""),D._cleanup(!1)}},d.prototype._cleanup=function(v){if(this.xhr){if(this.removeAllListeners(),s.unloadDel(this.unloadRef),this.xhr.onreadystatechange=function(){},this.xhr.ontimeout&&(this.xhr.ontimeout=null),v)try{this.xhr.abort()}catch{}this.unloadRef=this.xhr=null}},d.prototype.close=function(){this._cleanup(!0)},d.enabled=!!u;var p=["Active"].concat("Object").join("X");!d.enabled&&p in window&&(u=function(){try{return new window[p]("Microsoft.XMLHTTP")}catch{return null}},d.enabled=!!new u);var m=!1;try{m="withCredentials"in new u}catch{}return d.supportsCORS=m,fa=d,fa}var da,fh;function Qo(){if(fh)return da;fh=1;var i=ve(),r=qp();function s(l,u,f,d){r.call(this,l,u,f,d)}return i(s,r),s.enabled=r.enabled&&r.supportsCORS,da=s,da}var ha,dh;function di(){if(dh)return ha;dh=1;var i=ve(),r=qp();function s(l,u,f){r.call(this,l,u,f,{noCredentials:!0})}return i(s,r),s.enabled=r.enabled,ha=s,ha}var pa,hh;function hi(){return hh||(hh=1,pa={isOpera:function(){return window.navigator&&/opera/i.test(window.navigator.userAgent)},isKonqueror:function(){return window.navigator&&/konqueror/i.test(window.navigator.userAgent)},hasDomain:function(){if(!window.document)return!0;try{return!!window.document.domain}catch{return!1}}}),pa}var ma,ph;function r0(){if(ph)return ma;ph=1;var i=ve(),r=gr(),s=Jo(),l=Qo(),u=di(),f=hi();function d(p){if(!u.enabled&&!l.enabled)throw new Error("Transport created when disabled");r.call(this,p,"/xhr_streaming",s,l)}return i(d,r),d.enabled=function(p){return p.nullOrigin||f.isOpera()?!1:l.enabled},d.transportName="xhr-streaming",d.roundTrips=2,d.needBody=!!window.document,ma=d,ma}var va,mh;function gu(){if(mh)return va;mh=1;var i=et().EventEmitter,r=ve(),s=wn(),l=hi(),u=Et(),f=function(){};function d(p,m,v){var g=this;i.call(this),setTimeout(function(){g._start(p,m,v)},0)}return r(d,i),d.prototype._start=function(p,m,v){var g=this,y=new window.XDomainRequest;m=u.addQuery(m,"t="+ +new Date),y.onerror=function(){g._error()},y.ontimeout=function(){g._error()},y.onprogress=function(){f("progress",y.responseText),g.emit("chunk",200,y.responseText)},y.onload=function(){g.emit("finish",200,y.responseText),g._cleanup(!1)},this.xdr=y,this.unloadRef=s.unloadAdd(function(){g._cleanup(!0)});try{this.xdr.open(p,m),this.timeout&&(this.xdr.timeout=this.timeout),this.xdr.send(v)}catch{this._error()}},d.prototype._error=function(){this.emit("finish",0,""),this._cleanup(!1)},d.prototype._cleanup=function(p){if(this.xdr){if(this.removeAllListeners(),s.unloadDel(this.unloadRef),this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null,p)try{this.xdr.abort()}catch{}this.unloadRef=this.xdr=null}},d.prototype.close=function(){this._cleanup(!0)},d.enabled=!!(window.XDomainRequest&&l.hasDomain()),va=d,va}var ga,vh;function $p(){if(vh)return ga;vh=1;var i=ve(),r=gr(),s=Jo(),l=gu();function u(f){if(!l.enabled)throw new Error("Transport created when disabled");r.call(this,f,"/xhr_streaming",s,l)}return i(u,r),u.enabled=function(f){return f.cookie_needed||f.nullOrigin?!1:l.enabled&&f.sameScheme},u.transportName="xdr-streaming",u.roundTrips=2,ga=u,ga}var ya,gh;function Vp(){return gh||(gh=1,ya=window.EventSource),ya}var wa,yh;function i0(){if(yh)return wa;yh=1;var i=ve(),r=et().EventEmitter,s=Vp(),l=function(){};function u(f){r.call(this);var d=this,p=this.es=new s(f);p.onmessage=function(m){l("message",m.data),d.emit("message",decodeURI(m.data))},p.onerror=function(m){l("error",p.readyState);var v=p.readyState!==2?"network":"permanent";d._cleanup(),d._close(v)}}return i(u,r),u.prototype.abort=function(){this._cleanup(),this._close("user")},u.prototype._cleanup=function(){var f=this.es;f&&(f.onmessage=f.onerror=null,f.close(),this.es=null)},u.prototype._close=function(f){var d=this;setTimeout(function(){d.emit("close",null,f),d.removeAllListeners()},200)},wa=u,wa}var Sa,wh;function Sh(){if(wh)return Sa;wh=1;var i=ve(),r=gr(),s=i0(),l=Qo(),u=Vp();function f(d){if(!f.enabled())throw new Error("Transport created when disabled");r.call(this,d,"/eventsource",s,l)}return i(f,r),f.enabled=function(){return!!u},f.transportName="eventsource",f.roundTrips=2,Sa=f,Sa}var Ea,Eh;function Xp(){return Eh||(Eh=1,Ea="1.6.1"),Ea}var _a={exports:{}},_h;function pi(){return _h||(_h=1,function(i){var r=wn(),s=hi();i.exports={WPrefix:"_jp",currentWindowId:null,polluteGlobalNamespace:function(){i.exports.WPrefix in window||(window[i.exports.WPrefix]={})},postMessage:function(l,u){window.parent!==window&&window.parent.postMessage(JSON.stringify({windowId:i.exports.currentWindowId,type:l,data:u||""}),"*")},createIframe:function(l,u){var f=window.document.createElement("iframe"),d,p,m=function(){clearTimeout(d);try{f.onload=null}catch{}f.onerror=null},v=function(){f&&(m(),setTimeout(function(){f&&f.parentNode.removeChild(f),f=null},0),r.unloadDel(p))},g=function(_){f&&(v(),u(_))},y=function(_,D){setTimeout(function(){try{f&&f.contentWindow&&f.contentWindow.postMessage(_,D)}catch{}},0)};return f.src=l,f.style.display="none",f.style.position="absolute",f.onerror=function(){g("onerror")},f.onload=function(){clearTimeout(d),d=setTimeout(function(){g("onload timeout")},2e3)},window.document.body.appendChild(f),d=setTimeout(function(){g("timeout")},15e3),p=r.unloadAdd(v),{post:y,cleanup:v,loaded:m}},createHtmlfile:function(l,u){var f=["Active"].concat("Object").join("X"),d=new window[f]("htmlfile"),p,m,v,g=function(){clearTimeout(p),v.onerror=null},y=function(){d&&(g(),r.unloadDel(m),v.parentNode.removeChild(v),v=d=null,CollectGarbage())},_=function(x){d&&(y(),u(x))},D=function(x,N){try{setTimeout(function(){v&&v.contentWindow&&v.contentWindow.postMessage(x,N)},0)}catch{}};d.open(),d.write('<html><script>document.domain="'+window.document.domain+'";<\/script></html>'),d.close(),d.parentWindow[i.exports.WPrefix]=window[i.exports.WPrefix];var U=d.createElement("div");return d.body.appendChild(U),v=d.createElement("iframe"),U.appendChild(v),v.src=l,v.onerror=function(){_("onerror")},p=setTimeout(function(){_("timeout")},15e3),m=r.unloadAdd(y),{post:D,cleanup:y,loaded:g}}},i.exports.iframeEnabled=!1,window.document&&(i.exports.iframeEnabled=(typeof window.postMessage=="function"||typeof window.postMessage=="object")&&!s.isKonqueror())}(_a)),_a.exports}var xa,xh;function Jp(){if(xh)return xa;xh=1;var i=ve(),r=et().EventEmitter,s=Xp(),l=Et(),u=pi(),f=wn(),d=vr(),p=function(){};function m(v,g,y){if(!m.enabled())throw new Error("Transport created when disabled");r.call(this);var _=this;this.origin=l.getOrigin(y),this.baseUrl=y,this.transUrl=g,this.transport=v,this.windowId=d.string(8);var D=l.addPath(y,"/iframe.html")+"#"+this.windowId;this.iframeObj=u.createIframe(D,function(U){_.emit("close",1006,"Unable to load an iframe ("+U+")"),_.close()}),this.onmessageCallback=this._message.bind(this),f.attachEvent("message",this.onmessageCallback)}return i(m,r),m.prototype.close=function(){if(this.removeAllListeners(),this.iframeObj){f.detachEvent("message",this.onmessageCallback);try{this.postMessage("c")}catch{}this.iframeObj.cleanup(),this.iframeObj=null,this.onmessageCallback=this.iframeObj=null}},m.prototype._message=function(v){if(p("message",v.data),!l.isOriginEqual(v.origin,this.origin)){p("not same origin",v.origin,this.origin);return}var g;try{g=JSON.parse(v.data)}catch{p("bad json",v.data);return}if(g.windowId!==this.windowId){p("mismatched window id",g.windowId,this.windowId);return}switch(g.type){case"s":this.iframeObj.loaded(),this.postMessage("s",JSON.stringify([s,this.transport,this.transUrl,this.baseUrl]));break;case"t":this.emit("message",g.data);break;case"c":var y;try{y=JSON.parse(g.data)}catch{p("bad json",g.data);return}this.emit("close",y[0],y[1]),this.close();break}},m.prototype.postMessage=function(v,g){this.iframeObj.post(JSON.stringify({windowId:this.windowId,type:v,data:g||""}),this.origin)},m.prototype.send=function(v){this.postMessage("m",v)},m.enabled=function(){return u.iframeEnabled},m.transportName="iframe",m.roundTrips=2,xa=m,xa}var ka,kh;function yu(){return kh||(kh=1,ka={isObject:function(i){var r=typeof i;return r==="function"||r==="object"&&!!i},extend:function(i){if(!this.isObject(i))return i;for(var r,s,l=1,u=arguments.length;l<u;l++){r=arguments[l];for(s in r)Object.prototype.hasOwnProperty.call(r,s)&&(i[s]=r[s])}return i}}),ka}var Ca,Ch;function Ra(){if(Ch)return Ca;Ch=1;var i=ve(),r=Jp(),s=yu();return Ca=function(l){function u(f,d){r.call(this,l.transportName,f,d)}return i(u,r),u.enabled=function(f,d){if(!window.document)return!1;var p=s.extend({},d);return p.sameOrigin=!0,l.enabled(p)&&r.enabled()},u.transportName="iframe-"+l.transportName,u.needBody=!0,u.roundTrips=r.roundTrips+l.roundTrips-1,u.facadeTransport=l,u},Ca}var Na,Rh;function o0(){if(Rh)return Na;Rh=1;var i=ve(),r=pi(),s=Et(),l=et().EventEmitter,u=vr(),f=function(){};function d(m){l.call(this);var v=this;r.polluteGlobalNamespace(),this.id="a"+u.string(6),m=s.addQuery(m,"c="+decodeURIComponent(r.WPrefix+"."+this.id)),f("using htmlfile",d.htmlfileEnabled);var g=d.htmlfileEnabled?r.createHtmlfile:r.createIframe;window[r.WPrefix][this.id]={start:function(){v.iframeObj.loaded()},message:function(y){v.emit("message",y)},stop:function(){v._cleanup(),v._close("network")}},this.iframeObj=g(m,function(){v._cleanup(),v._close("permanent")})}i(d,l),d.prototype.abort=function(){this._cleanup(),this._close("user")},d.prototype._cleanup=function(){this.iframeObj&&(this.iframeObj.cleanup(),this.iframeObj=null),delete window[r.WPrefix][this.id]},d.prototype._close=function(m){this.emit("close",null,m),this.removeAllListeners()},d.htmlfileEnabled=!1;var p=["Active"].concat("Object").join("X");if(p in window)try{d.htmlfileEnabled=!!new window[p]("htmlfile")}catch{}return d.enabled=d.htmlfileEnabled||r.iframeEnabled,Na=d,Na}var Ta,Nh;function Th(){if(Nh)return Ta;Nh=1;var i=ve(),r=o0(),s=di(),l=gr();function u(f){if(!r.enabled)throw new Error("Transport created when disabled");l.call(this,f,"/htmlfile",r,s)}return i(u,l),u.enabled=function(f){return r.enabled&&f.sameOrigin},u.transportName="htmlfile",u.roundTrips=2,Ta=u,Ta}var Pa,Ph;function Oh(){if(Ph)return Pa;Ph=1;var i=ve(),r=gr(),s=Jo(),l=Qo(),u=di();function f(d){if(!u.enabled&&!l.enabled)throw new Error("Transport created when disabled");r.call(this,d,"/xhr",s,l)}return i(f,r),f.enabled=function(d){return d.nullOrigin?!1:u.enabled&&d.sameOrigin?!0:l.enabled},f.transportName="xhr-polling",f.roundTrips=2,Pa=f,Pa}var Oa,jh;function s0(){if(jh)return Oa;jh=1;var i=ve(),r=gr(),s=$p(),l=Jo(),u=gu();function f(d){if(!u.enabled)throw new Error("Transport created when disabled");r.call(this,d,"/xhr",l,u)}return i(f,r),f.enabled=s.enabled,f.transportName="xdr-polling",f.roundTrips=2,Oa=f,Oa}var ja,Lh;function l0(){if(Lh)return ja;Lh=1;var i=pi(),r=vr(),s=hi(),l=Et(),u=ve(),f=et().EventEmitter,d=function(){};function p(m){var v=this;f.call(this),i.polluteGlobalNamespace(),this.id="a"+r.string(6);var g=l.addQuery(m,"c="+encodeURIComponent(i.WPrefix+"."+this.id));window[i.WPrefix][this.id]=this._callback.bind(this),this._createScript(g),this.timeoutId=setTimeout(function(){v._abort(new Error("JSONP script loaded abnormally (timeout)"))},p.timeout)}return u(p,f),p.prototype.abort=function(){if(window[i.WPrefix][this.id]){var m=new Error("JSONP user aborted read");m.code=1e3,this._abort(m)}},p.timeout=35e3,p.scriptErrorTimeout=1e3,p.prototype._callback=function(m){this._cleanup(),!this.aborting&&(m&&this.emit("message",m),this.emit("close",null,"network"),this.removeAllListeners())},p.prototype._abort=function(m){this._cleanup(),this.aborting=!0,this.emit("close",m.code,m.message),this.removeAllListeners()},p.prototype._cleanup=function(){if(clearTimeout(this.timeoutId),this.script2&&(this.script2.parentNode.removeChild(this.script2),this.script2=null),this.script){var m=this.script;m.parentNode.removeChild(m),m.onreadystatechange=m.onerror=m.onload=m.onclick=null,this.script=null}delete window[i.WPrefix][this.id]},p.prototype._scriptError=function(){var m=this;this.errorTimer||(this.errorTimer=setTimeout(function(){m.loadedOkay||m._abort(new Error("JSONP script loaded abnormally (onerror)"))},p.scriptErrorTimeout))},p.prototype._createScript=function(m){var v=this,g=this.script=window.document.createElement("script"),y;if(g.id="a"+r.string(8),g.src=m,g.type="text/javascript",g.charset="UTF-8",g.onerror=this._scriptError.bind(this),g.onload=function(){v._abort(new Error("JSONP script loaded abnormally (onload)"))},g.onreadystatechange=function(){if(d("onreadystatechange",g.readyState),/loaded|closed/.test(g.readyState)){if(g&&g.htmlFor&&g.onclick){v.loadedOkay=!0;try{g.onclick()}catch{}}g&&v._abort(new Error("JSONP script loaded abnormally (onreadystatechange)"))}},typeof g.async>"u"&&window.document.attachEvent)if(s.isOpera())y=this.script2=window.document.createElement("script"),y.text="try{var a = document.getElementById('"+g.id+"'); if(a)a.onerror();}catch(x){};",g.async=y.async=!1;else{try{g.htmlFor=g.id,g.event="onclick"}catch{}g.async=!0}typeof g.async<"u"&&(g.async=!0);var _=window.document.getElementsByTagName("head")[0];_.insertBefore(g,_.firstChild),y&&_.insertBefore(y,_.firstChild)},ja=p,ja}var La,Ih;function a0(){if(Ih)return La;Ih=1;var i=vr(),r=Et(),s=function(){},l,u;function f(p){try{return window.document.createElement('<iframe name="'+p+'">')}catch{var m=window.document.createElement("iframe");return m.name=p,m}}function d(){l=window.document.createElement("form"),l.style.display="none",l.style.position="absolute",l.method="POST",l.enctype="application/x-www-form-urlencoded",l.acceptCharset="UTF-8",u=window.document.createElement("textarea"),u.name="d",l.appendChild(u),window.document.body.appendChild(l)}return La=function(p,m,v){l||d();var g="a"+i.string(8);l.target=g,l.action=r.addQuery(r.addPath(p,"/jsonp_send"),"i="+g);var y=f(g);y.id=g,y.style.display="none",l.appendChild(y);try{u.value=m}catch{}l.submit();var _=function(D){y.onerror&&(y.onreadystatechange=y.onerror=y.onload=null,setTimeout(function(){y.parentNode.removeChild(y),y=null},500),u.value="",v(D))};return y.onerror=function(){_()},y.onload=function(){_()},y.onreadystatechange=function(D){s("onreadystatechange",g,y.readyState),y.readyState==="complete"&&_()},function(){_(new Error("Aborted"))}},La}var Ia,bh;function u0(){if(bh)return Ia;bh=1;var i=ve(),r=Wp(),s=l0(),l=a0();function u(f){if(!u.enabled())throw new Error("Transport created when disabled");r.call(this,f,"/jsonp",l,s)}return i(u,r),u.enabled=function(){return!!window.document},u.transportName="jsonp-polling",u.roundTrips=1,u.needBody=!0,Ia=u,Ia}var ba,Ah;function c0(){return Ah||(Ah=1,ba=[e0(),r0(),$p(),Sh(),Ra()(Sh()),Th(),Ra()(Th()),Oh(),s0(),Ra()(Oh()),u0()]),ba}var Uh={},Dh;function f0(){if(Dh)return Uh;Dh=1;var i=Array.prototype,r=Object.prototype,s=Function.prototype,l=String.prototype,u=i.slice,f=r.toString,d=function(z){return r.toString.call(z)==="[object Function]"},p=function(b){return f.call(b)==="[object Array]"},m=function(b){return f.call(b)==="[object String]"},v=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),!0}catch{return!1}}(),g;v?g=function(z,b,V,ne){!ne&&b in z||Object.defineProperty(z,b,{configurable:!0,enumerable:!1,writable:!0,value:V})}:g=function(z,b,V,ne){!ne&&b in z||(z[b]=V)};var y=function(z,b,V){for(var ne in b)r.hasOwnProperty.call(b,ne)&&g(z,ne,b[ne],V)},_=function(z){if(z==null)throw new TypeError("can't convert "+z+" to object");return Object(z)};function D(z){var b=+z;return b!==b?b=0:b!==0&&b!==1/0&&b!==-1/0&&(b=(b>0||-1)*Math.floor(Math.abs(b))),b}function U(z){return z>>>0}function x(){}y(s,{bind:function(b){var V=this;if(!d(V))throw new TypeError("Function.prototype.bind called on incompatible "+V);for(var ne=u.call(arguments,1),ae=function(){if(this instanceof Se){var Le=V.apply(this,ne.concat(u.call(arguments)));return Object(Le)===Le?Le:this}else return V.apply(b,ne.concat(u.call(arguments)))},ge=Math.max(0,V.length-ne.length),X=[],je=0;je<ge;je++)X.push("$"+je);var Se=Function("binder","return function ("+X.join(",")+"){ return binder.apply(this, arguments); }")(ae);return V.prototype&&(x.prototype=V.prototype,Se.prototype=new x,x.prototype=null),Se}}),y(Array,{isArray:p});var N=Object("a"),M=N[0]!=="a"||!(0 in N),T=function(b){var V=!0,ne=!0;return b&&(b.call("foo",function(ae,ge,X){typeof X!="object"&&(V=!1)}),b.call([1],function(){ne=typeof this=="string"},"x")),!!b&&V&&ne};y(i,{forEach:function(b){var V=_(this),ne=M&&m(this)?this.split(""):V,ae=arguments[1],ge=-1,X=ne.length>>>0;if(!d(b))throw new TypeError;for(;++ge<X;)ge in ne&&b.call(ae,ne[ge],ge,V)}},!T(i.forEach));var L=Array.prototype.indexOf&&[0,1].indexOf(1,2)!==-1;y(i,{indexOf:function(b){var V=M&&m(this)?this.split(""):_(this),ne=V.length>>>0;if(!ne)return-1;var ae=0;for(arguments.length>1&&(ae=D(arguments[1])),ae=ae>=0?ae:Math.max(0,ne+ae);ae<ne;ae++)if(ae in V&&V[ae]===b)return ae;return-1}},L);var R=l.split;"ab".split(/(?:ab)*/).length!==2||".".split(/(.?)(.?)/).length!==4||"tesst".split(/(s)*/)[1]==="t"||"test".split(/(?:)/,-1).length!==4||"".split(/.?/).length||".".split(/()()/).length>1?function(){var z=/()??/.exec("")[1]===void 0;l.split=function(b,V){var ne=this;if(b===void 0&&V===0)return[];if(f.call(b)!=="[object RegExp]")return R.call(this,b,V);var ae=[],ge=(b.ignoreCase?"i":"")+(b.multiline?"m":"")+(b.extended?"x":"")+(b.sticky?"y":""),X=0,je,Se,Le,ke;for(b=new RegExp(b.source,ge+"g"),ne+="",z||(je=new RegExp("^"+b.source+"$(?!\\s)",ge)),V=V===void 0?-1>>>0:U(V);(Se=b.exec(ne))&&(Le=Se.index+Se[0].length,!(Le>X&&(ae.push(ne.slice(X,Se.index)),!z&&Se.length>1&&Se[0].replace(je,function(){for(var Q=1;Q<arguments.length-2;Q++)arguments[Q]===void 0&&(Se[Q]=void 0)}),Se.length>1&&Se.index<ne.length&&i.push.apply(ae,Se.slice(1)),ke=Se[0].length,X=Le,ae.length>=V)));)b.lastIndex===Se.index&&b.lastIndex++;return X===ne.length?(ke||!b.test(""))&&ae.push(""):ae.push(ne.slice(X)),ae.length>V?ae.slice(0,V):ae}}():"0".split(void 0,0).length&&(l.split=function(b,V){return b===void 0&&V===0?[]:R.call(this,b,V)});var I=l.substr,J="".substr&&"0b".substr(-1)!=="b";return y(l,{substr:function(b,V){return I.call(this,b<0&&(b=this.length+b)<0?0:b,V)}},J),Uh}var Aa,Fh;function d0(){if(Fh)return Aa;Fh=1;var i=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g,r,s=function(l){var u,f={},d=[];for(u=0;u<65536;u++)d.push(String.fromCharCode(u));return l.lastIndex=0,d.join("").replace(l,function(p){return f[p]="\\u"+("0000"+p.charCodeAt(0).toString(16)).slice(-4),""}),l.lastIndex=0,f};return Aa={quote:function(l){var u=JSON.stringify(l);return i.lastIndex=0,i.test(u)?(r||(r=s(i)),u.replace(i,function(f){return r[f]})):u}},Aa}var Ua,Mh;function h0(){if(Mh)return Ua;Mh=1;var i=function(){};return Ua=function(r){return{filterToEnabled:function(s,l){var u={main:[],facade:[]};return s?typeof s=="string"&&(s=[s]):s=[],r.forEach(function(f){if(f&&!(f.transportName==="websocket"&&l.websocket===!1)){if(s.length&&s.indexOf(f.transportName)===-1){i("not in whitelist",f.transportName);return}f.enabled(l)?(i("enabled",f.transportName),u.main.push(f),f.facadeTransport&&u.facade.push(f.facadeTransport)):i("disabled",f.transportName)}}),u}}},Ua}var Da,Bh;function p0(){if(Bh)return Da;Bh=1;var i={};return["log","debug","warn"].forEach(function(r){var s;try{s=window.console&&window.console[r]&&window.console[r].apply}catch{}i[r]=s?function(){return window.console[r].apply(window.console,arguments)}:r==="log"?function(){}:i.log}),Da=i,Da}var Fa,zh;function wu(){if(zh)return Fa;zh=1;function i(r){this.type=r}return i.prototype.initEvent=function(r,s,l){return this.type=r,this.bubbles=s,this.cancelable=l,this.timeStamp=+new Date,this},i.prototype.stopPropagation=function(){},i.prototype.preventDefault=function(){},i.CAPTURING_PHASE=1,i.AT_TARGET=2,i.BUBBLING_PHASE=3,Fa=i,Fa}var Ma,Hh;function Qp(){return Hh||(Hh=1,Ma=window.location||{origin:"http://localhost:80",protocol:"http:",host:"localhost",port:80,href:"http://localhost/",hash:""}),Ma}var Ba,Wh;function m0(){if(Wh)return Ba;Wh=1;var i=ve(),r=wu();function s(){r.call(this),this.initEvent("close",!1,!1),this.wasClean=!1,this.code=0,this.reason=""}return i(s,r),Ba=s,Ba}var za,qh;function v0(){if(qh)return za;qh=1;var i=ve(),r=wu();function s(l){r.call(this),this.initEvent("message",!1,!1),this.data=l}return i(s,r),za=s,za}var Ha,$h;function g0(){if($h)return Ha;$h=1;var i=et().EventEmitter,r=ve();function s(){var l=this;i.call(this),this.to=setTimeout(function(){l.emit("finish",200,"{}")},s.timeout)}return r(s,i),s.prototype.close=function(){clearTimeout(this.to)},s.timeout=2e3,Ha=s,Ha}var Wa,Vh;function Kp(){if(Vh)return Wa;Vh=1;var i=et().EventEmitter,r=ve(),s=yu();function l(u,f){i.call(this);var d=this,p=+new Date;this.xo=new f("GET",u),this.xo.once("finish",function(m,v){var g,y;if(m===200){if(y=+new Date-p,v)try{g=JSON.parse(v)}catch{}s.isObject(g)||(g={})}d.emit("finish",g,y),d.removeAllListeners()})}return r(l,i),l.prototype.close=function(){this.removeAllListeners(),this.xo.close()},Wa=l,Wa}var qa,Xh;function Gp(){if(Xh)return qa;Xh=1;var i=ve(),r=et().EventEmitter,s=di(),l=Kp();function u(f){var d=this;r.call(this),this.ir=new l(f,s),this.ir.once("finish",function(p,m){d.ir=null,d.emit("message",JSON.stringify([p,m]))})}return i(u,r),u.transportName="iframe-info-receiver",u.prototype.close=function(){this.ir&&(this.ir.close(),this.ir=null),this.removeAllListeners()},qa=u,qa}var $a,Jh;function y0(){if(Jh)return $a;Jh=1;var i=et().EventEmitter,r=ve(),s=wn(),l=Jp(),u=Gp();function f(d,p){var m=this;i.call(this);var v=function(){var g=m.ifr=new l(u.transportName,p,d);g.once("message",function(y){if(y){var _;try{_=JSON.parse(y)}catch{m.emit("finish"),m.close();return}var D=_[0],U=_[1];m.emit("finish",D,U)}m.close()}),g.once("close",function(){m.emit("finish"),m.close()})};window.document.body?v():s.attachEvent("load",v)}return r(f,i),f.enabled=function(){return l.enabled()},f.prototype.close=function(){this.ifr&&this.ifr.close(),this.removeAllListeners(),this.ifr=null},$a=f,$a}var Va,Qh;function w0(){if(Qh)return Va;Qh=1;var i=et().EventEmitter,r=ve(),s=Et(),l=gu(),u=Qo(),f=di(),d=g0(),p=y0(),m=Kp();function v(g,y){var _=this;i.call(this),setTimeout(function(){_.doXhr(g,y)},0)}return r(v,i),v._getReceiver=function(g,y,_){return _.sameOrigin?new m(y,f):u.enabled?new m(y,u):l.enabled&&_.sameScheme?new m(y,l):p.enabled()?new p(g,y):new m(y,d)},v.prototype.doXhr=function(g,y){var _=this,D=s.addPath(g,"/info");this.xo=v._getReceiver(g,D,y),this.timeoutRef=setTimeout(function(){_._cleanup(!1),_.emit("finish")},v.timeout),this.xo.once("finish",function(U,x){_._cleanup(!0),_.emit("finish",U,x)})},v.prototype._cleanup=function(g){clearTimeout(this.timeoutRef),this.timeoutRef=null,!g&&this.xo&&this.xo.close(),this.xo=null},v.prototype.close=function(){this.removeAllListeners(),this._cleanup(!1)},v.timeout=8e3,Va=v,Va}var Xa,Kh;function S0(){if(Kh)return Xa;Kh=1;var i=pi();function r(s){this._transport=s,s.on("message",this._transportMessage.bind(this)),s.on("close",this._transportClose.bind(this))}return r.prototype._transportClose=function(s,l){i.postMessage("c",JSON.stringify([s,l]))},r.prototype._transportMessage=function(s){i.postMessage("t",s)},r.prototype._send=function(s){this._transport.send(s)},r.prototype._close=function(){this._transport.close(),this._transport.removeAllListeners()},Xa=r,Xa}var Ja,Gh;function E0(){if(Gh)return Ja;Gh=1;var i=Et(),r=wn(),s=S0(),l=Gp(),u=pi(),f=Qp(),d=function(){};return Ja=function(p,m){var v={};m.forEach(function(y){y.facadeTransport&&(v[y.facadeTransport.transportName]=y.facadeTransport)}),v[l.transportName]=l;var g;p.bootstrap_iframe=function(){var y;u.currentWindowId=f.hash.slice(1);var _=function(D){if(D.source===parent&&(typeof g>"u"&&(g=D.origin),D.origin===g)){var U;try{U=JSON.parse(D.data)}catch{d("bad json",D.data);return}if(U.windowId===u.currentWindowId)switch(U.type){case"s":var x;try{x=JSON.parse(U.data)}catch{d("bad json",U.data);break}var N=x[0],M=x[1],T=x[2],L=x[3];if(N!==p.version)throw new Error('Incompatible SockJS! Main site uses: "'+N+'", the iframe: "'+p.version+'".');if(!i.isOriginEqual(T,f.href)||!i.isOriginEqual(L,f.href))throw new Error("Can't connect to different domain from within an iframe. ("+f.href+", "+T+", "+L+")");y=new s(new v[M](T,L));break;case"m":y._send(U.data);break;case"c":y&&y._close(),y=null;break}}};r.attachEvent("message",_),u.postMessage("s")}},Ja}var Qa,Yh;function _0(){if(Yh)return Qa;Yh=1,f0();var i=zp(),r=ve(),s=vr(),l=d0(),u=Et(),f=wn(),d=h0(),p=yu(),m=hi(),v=p0(),g=wu(),y=Hp(),_=Qp(),D=m0(),U=v0(),x=w0(),N=function(){},M;function T(R,I,J){if(!(this instanceof T))return new T(R,I,J);if(arguments.length<1)throw new TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");y.call(this),this.readyState=T.CONNECTING,this.extensions="",this.protocol="",J=J||{},J.protocols_whitelist&&v.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."),this._transportsWhitelist=J.transports,this._transportOptions=J.transportOptions||{},this._timeout=J.timeout||0;var z=J.sessionId||8;if(typeof z=="function")this._generateSessionId=z;else if(typeof z=="number")this._generateSessionId=function(){return s.string(z)};else throw new TypeError("If sessionId is used in the options, it needs to be a number or a function.");this._server=J.server||s.numberString(1e3);var b=new i(R);if(!b.host||!b.protocol)throw new SyntaxError("The URL '"+R+"' is invalid");if(b.hash)throw new SyntaxError("The URL must not contain a fragment");if(b.protocol!=="http:"&&b.protocol!=="https:")throw new SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '"+b.protocol+"' is not allowed.");var V=b.protocol==="https:";if(_.protocol==="https:"&&!V&&!u.isLoopbackAddr(b.hostname))throw new Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");I?Array.isArray(I)||(I=[I]):I=[];var ne=I.sort();ne.forEach(function(ge,X){if(!ge)throw new SyntaxError("The protocols entry '"+ge+"' is invalid.");if(X<ne.length-1&&ge===ne[X+1])throw new SyntaxError("The protocols entry '"+ge+"' is duplicated.")});var ae=u.getOrigin(_.href);this._origin=ae?ae.toLowerCase():null,b.set("pathname",b.pathname.replace(/\/+$/,"")),this.url=b.href,N("using url",this.url),this._urlInfo={nullOrigin:!m.hasDomain(),sameOrigin:u.isOriginEqual(this.url,_.href),sameScheme:u.isSchemeEqual(this.url,_.href)},this._ir=new x(this.url,this._urlInfo),this._ir.once("finish",this._receiveInfo.bind(this))}r(T,y);function L(R){return R===1e3||R>=3e3&&R<=4999}return T.prototype.close=function(R,I){if(R&&!L(R))throw new Error("InvalidAccessError: Invalid code");if(I&&I.length>123)throw new SyntaxError("reason argument has an invalid length");if(!(this.readyState===T.CLOSING||this.readyState===T.CLOSED)){var J=!0;this._close(R||1e3,I||"Normal closure",J)}},T.prototype.send=function(R){if(typeof R!="string"&&(R=""+R),this.readyState===T.CONNECTING)throw new Error("InvalidStateError: The connection has not been established yet");this.readyState===T.OPEN&&this._transport.send(l.quote(R))},T.version=Xp(),T.CONNECTING=0,T.OPEN=1,T.CLOSING=2,T.CLOSED=3,T.prototype._receiveInfo=function(R,I){if(this._ir=null,!R){this._close(1002,"Cannot connect to server");return}this._rto=this.countRTO(I),this._transUrl=R.base_url?R.base_url:this.url,R=p.extend(R,this._urlInfo);var J=M.filterToEnabled(this._transportsWhitelist,R);this._transports=J.main,N(this._transports.length+" enabled transports"),this._connect()},T.prototype._connect=function(){for(var R=this._transports.shift();R;R=this._transports.shift()){if(N("attempt",R.transportName),R.needBody&&(!window.document.body||typeof window.document.readyState<"u"&&window.document.readyState!=="complete"&&window.document.readyState!=="interactive")){this._transports.unshift(R),f.attachEvent("load",this._connect.bind(this));return}var I=Math.max(this._timeout,this._rto*R.roundTrips||5e3);this._transportTimeoutId=setTimeout(this._transportTimeout.bind(this),I);var J=u.addPath(this._transUrl,"/"+this._server+"/"+this._generateSessionId()),z=this._transportOptions[R.transportName],b=new R(J,this._transUrl,z);b.on("message",this._transportMessage.bind(this)),b.once("close",this._transportClose.bind(this)),b.transportName=R.transportName,this._transport=b;return}this._close(2e3,"All transports failed",!1)},T.prototype._transportTimeout=function(){this.readyState===T.CONNECTING&&(this._transport&&this._transport.close(),this._transportClose(2007,"Transport timed out"))},T.prototype._transportMessage=function(R){var I=this,J=R.slice(0,1),z=R.slice(1),b;switch(J){case"o":this._open();return;case"h":this.dispatchEvent(new g("heartbeat")),N("heartbeat",this.transport);return}if(z)try{b=JSON.parse(z)}catch{}if(!(typeof b>"u"))switch(J){case"a":Array.isArray(b)&&b.forEach(function(V){N("message",I.transport),I.dispatchEvent(new U(V))});break;case"m":N("message",this.transport),this.dispatchEvent(new U(b));break;case"c":Array.isArray(b)&&b.length===2&&this._close(b[0],b[1],!0);break}},T.prototype._transportClose=function(R,I){if(N("_transportClose",this.transport),this._transport&&(this._transport.removeAllListeners(),this._transport=null,this.transport=null),!L(R)&&R!==2e3&&this.readyState===T.CONNECTING){this._connect();return}this._close(R,I)},T.prototype._open=function(){N("_open",this._transport&&this._transport.transportName,this.readyState),this.readyState===T.CONNECTING?(this._transportTimeoutId&&(clearTimeout(this._transportTimeoutId),this._transportTimeoutId=null),this.readyState=T.OPEN,this.transport=this._transport.transportName,this.dispatchEvent(new g("open")),N("connected",this.transport)):this._close(1006,"Server lost session")},T.prototype._close=function(R,I,J){N("_close",this.transport,R,I,J,this.readyState);var z=!1;if(this._ir&&(z=!0,this._ir.close(),this._ir=null),this._transport&&(this._transport.close(),this._transport=null,this.transport=null),this.readyState===T.CLOSED)throw new Error("InvalidStateError: SockJS has already been closed");this.readyState=T.CLOSING,setTimeout((function(){this.readyState=T.CLOSED,z&&this.dispatchEvent(new g("error"));var b=new D("close");b.wasClean=J||!1,b.code=R||1e3,b.reason=I,this.dispatchEvent(b),this.onmessage=this.onclose=this.onerror=null}).bind(this),0)},T.prototype.countRTO=function(R){return R>100?4*R:300+R},Qa=function(R){return M=d(R),E0()(T,R),T},Qa}var Ka,Zh;function x0(){if(Zh)return Ka;Zh=1;var i=c0();return Ka=_0()(i),"_sockjs_onload"in window&&setTimeout(window._sockjs_onload,1),Ka}var k0=x0();const C0=rp(k0);function R0(i,r){i.terminate=function(){const s=()=>{};this.onerror=s,this.onmessage=s,this.onopen=s;const l=new Date,u=Math.random().toString().substring(2,8),f=this.onclose;this.onclose=d=>{const p=new Date().getTime()-l.getTime();r(`Discarded socket (#${u})  closed after ${p}ms, with code/reason: ${d.code}/${d.reason}`)},this.close(),f==null||f.call(i,{code:4001,reason:`Quick discarding socket (#${u}) without waiting for the shutdown sequence.`,wasClean:!1})}}const oi={LF:`
`,NULL:"\0"};class hn{get body(){return!this._body&&this.isBinaryBody&&(this._body=new TextDecoder().decode(this._binaryBody)),this._body||""}get binaryBody(){return!this._binaryBody&&!this.isBinaryBody&&(this._binaryBody=new TextEncoder().encode(this._body)),this._binaryBody}constructor(r){const{command:s,headers:l,body:u,binaryBody:f,escapeHeaderValues:d,skipContentLengthHeader:p}=r;this.command=s,this.headers=Object.assign({},l||{}),f?(this._binaryBody=f,this.isBinaryBody=!0):(this._body=u||"",this.isBinaryBody=!1),this.escapeHeaderValues=d||!1,this.skipContentLengthHeader=p||!1}static fromRawFrame(r,s){const l={},u=f=>f.replace(/^\s+|\s+$/g,"");for(const f of r.headers.reverse()){f.indexOf(":");const d=u(f[0]);let p=u(f[1]);s&&r.command!=="CONNECT"&&r.command!=="CONNECTED"&&(p=hn.hdrValueUnEscape(p)),l[d]=p}return new hn({command:r.command,headers:l,binaryBody:r.binaryBody,escapeHeaderValues:s})}toString(){return this.serializeCmdAndHeaders()}serialize(){const r=this.serializeCmdAndHeaders();return this.isBinaryBody?hn.toUnit8Array(r,this._binaryBody).buffer:r+this._body+oi.NULL}serializeCmdAndHeaders(){const r=[this.command];this.skipContentLengthHeader&&delete this.headers["content-length"];for(const s of Object.keys(this.headers||{})){const l=this.headers[s];this.escapeHeaderValues&&this.command!=="CONNECT"&&this.command!=="CONNECTED"?r.push(`${s}:${hn.hdrValueEscape(`${l}`)}`):r.push(`${s}:${l}`)}return(this.isBinaryBody||!this.isBodyEmpty()&&!this.skipContentLengthHeader)&&r.push(`content-length:${this.bodyLength()}`),r.join(oi.LF)+oi.LF+oi.LF}isBodyEmpty(){return this.bodyLength()===0}bodyLength(){const r=this.binaryBody;return r?r.length:0}static sizeOfUTF8(r){return r?new TextEncoder().encode(r).length:0}static toUnit8Array(r,s){const l=new TextEncoder().encode(r),u=new Uint8Array([0]),f=new Uint8Array(l.length+s.length+u.length);return f.set(l),f.set(s,l.length),f.set(u,l.length+s.length),f}static marshall(r){return new hn(r).serialize()}static hdrValueEscape(r){return r.replace(/\\/g,"\\\\").replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/:/g,"\\c")}static hdrValueUnEscape(r){return r.replace(/\\r/g,"\r").replace(/\\n/g,`
`).replace(/\\c/g,":").replace(/\\\\/g,"\\")}}const ep=0,jo=10,Lo=13,N0=58;class T0{constructor(r,s){this.onFrame=r,this.onIncomingPing=s,this._encoder=new TextEncoder,this._decoder=new TextDecoder,this._token=[],this._initState()}parseChunk(r,s=!1){let l;if(typeof r=="string"?l=this._encoder.encode(r):l=new Uint8Array(r),s&&l[l.length-1]!==0){const u=new Uint8Array(l.length+1);u.set(l,0),u[l.length]=0,l=u}for(let u=0;u<l.length;u++){const f=l[u];this._onByte(f)}}_collectFrame(r){if(r!==ep&&r!==Lo){if(r===jo){this.onIncomingPing();return}this._onByte=this._collectCommand,this._reinjectByte(r)}}_collectCommand(r){if(r!==Lo){if(r===jo){this._results.command=this._consumeTokenAsUTF8(),this._onByte=this._collectHeaders;return}this._consumeByte(r)}}_collectHeaders(r){if(r!==Lo){if(r===jo){this._setupCollectBody();return}this._onByte=this._collectHeaderKey,this._reinjectByte(r)}}_reinjectByte(r){this._onByte(r)}_collectHeaderKey(r){if(r===N0){this._headerKey=this._consumeTokenAsUTF8(),this._onByte=this._collectHeaderValue;return}this._consumeByte(r)}_collectHeaderValue(r){if(r!==Lo){if(r===jo){this._results.headers.push([this._headerKey,this._consumeTokenAsUTF8()]),this._headerKey=void 0,this._onByte=this._collectHeaders;return}this._consumeByte(r)}}_setupCollectBody(){const r=this._results.headers.filter(s=>s[0]==="content-length")[0];r?(this._bodyBytesRemaining=parseInt(r[1],10),this._onByte=this._collectBodyFixedSize):this._onByte=this._collectBodyNullTerminated}_collectBodyNullTerminated(r){if(r===ep){this._retrievedBody();return}this._consumeByte(r)}_collectBodyFixedSize(r){if(this._bodyBytesRemaining--===0){this._retrievedBody();return}this._consumeByte(r)}_retrievedBody(){this._results.binaryBody=this._consumeTokenAsRaw();try{this.onFrame(this._results)}catch(r){console.log("Ignoring an exception thrown by a frame handler. Original exception: ",r)}this._initState()}_consumeByte(r){this._token.push(r)}_consumeTokenAsUTF8(){return this._decoder.decode(this._consumeTokenAsRaw())}_consumeTokenAsRaw(){const r=new Uint8Array(this._token);return this._token=[],r}_initState(){this._results={command:void 0,headers:[],binaryBody:void 0},this._token=[],this._headerKey=void 0,this._onByte=this._collectFrame}}var pn;(function(i){i[i.CONNECTING=0]="CONNECTING",i[i.OPEN=1]="OPEN",i[i.CLOSING=2]="CLOSING",i[i.CLOSED=3]="CLOSED"})(pn||(pn={}));var Pt;(function(i){i[i.ACTIVE=0]="ACTIVE",i[i.DEACTIVATING=1]="DEACTIVATING",i[i.INACTIVE=2]="INACTIVE"})(Pt||(Pt={}));var Mo;(function(i){i[i.LINEAR=0]="LINEAR",i[i.EXPONENTIAL=1]="EXPONENTIAL"})(Mo||(Mo={}));var ui;(function(i){i.Interval="interval",i.Worker="worker"})(ui||(ui={}));class P0{constructor(r,s=ui.Interval,l){this._interval=r,this._strategy=s,this._debug=l,this._workerScript=`
    var startTime = Date.now();
    setInterval(function() {
        self.postMessage(Date.now() - startTime);
    }, ${this._interval});
  `}start(r){this.stop(),this.shouldUseWorker()?this.runWorker(r):this.runInterval(r)}stop(){this.disposeWorker(),this.disposeInterval()}shouldUseWorker(){return typeof Worker<"u"&&this._strategy===ui.Worker}runWorker(r){this._debug("Using runWorker for outgoing pings"),this._worker||(this._worker=new Worker(URL.createObjectURL(new Blob([this._workerScript],{type:"text/javascript"}))),this._worker.onmessage=s=>r(s.data))}runInterval(r){if(this._debug("Using runInterval for outgoing pings"),!this._timer){const s=Date.now();this._timer=setInterval(()=>{r(Date.now()-s)},this._interval)}}disposeWorker(){this._worker&&(this._worker.terminate(),delete this._worker,this._debug("Outgoing ping disposeWorker"))}disposeInterval(){this._timer&&(clearInterval(this._timer),delete this._timer,this._debug("Outgoing ping disposeInterval"))}}class Ze{constructor(r){this.versions=r}supportedVersions(){return this.versions.join(",")}protocolVersions(){return this.versions.map(r=>`v${r.replace(".","")}.stomp`)}}Ze.V1_0="1.0";Ze.V1_1="1.1";Ze.V1_2="1.2";Ze.default=new Ze([Ze.V1_2,Ze.V1_1,Ze.V1_0]);class O0{get connectedVersion(){return this._connectedVersion}get connected(){return this._connected}constructor(r,s,l){this._client=r,this._webSocket=s,this._connected=!1,this._serverFrameHandlers={CONNECTED:u=>{this.debug(`connected to server ${u.headers.server}`),this._connected=!0,this._connectedVersion=u.headers.version,this._connectedVersion===Ze.V1_2&&(this._escapeHeaderValues=!0),this._setupHeartbeat(u.headers),this.onConnect(u)},MESSAGE:u=>{const f=u.headers.subscription,d=this._subscriptions[f]||this.onUnhandledMessage,p=u,m=this,v=this._connectedVersion===Ze.V1_2?p.headers.ack:p.headers["message-id"];p.ack=(g={})=>m.ack(v,f,g),p.nack=(g={})=>m.nack(v,f,g),d(p)},RECEIPT:u=>{const f=this._receiptWatchers[u.headers["receipt-id"]];f?(f(u),delete this._receiptWatchers[u.headers["receipt-id"]]):this.onUnhandledReceipt(u)},ERROR:u=>{this.onStompError(u)}},this._counter=0,this._subscriptions={},this._receiptWatchers={},this._partialData="",this._escapeHeaderValues=!1,this._lastServerActivityTS=Date.now(),this.debug=l.debug,this.stompVersions=l.stompVersions,this.connectHeaders=l.connectHeaders,this.disconnectHeaders=l.disconnectHeaders,this.heartbeatIncoming=l.heartbeatIncoming,this.heartbeatOutgoing=l.heartbeatOutgoing,this.splitLargeFrames=l.splitLargeFrames,this.maxWebSocketChunkSize=l.maxWebSocketChunkSize,this.forceBinaryWSFrames=l.forceBinaryWSFrames,this.logRawCommunication=l.logRawCommunication,this.appendMissingNULLonIncoming=l.appendMissingNULLonIncoming,this.discardWebsocketOnCommFailure=l.discardWebsocketOnCommFailure,this.onConnect=l.onConnect,this.onDisconnect=l.onDisconnect,this.onStompError=l.onStompError,this.onWebSocketClose=l.onWebSocketClose,this.onWebSocketError=l.onWebSocketError,this.onUnhandledMessage=l.onUnhandledMessage,this.onUnhandledReceipt=l.onUnhandledReceipt,this.onUnhandledFrame=l.onUnhandledFrame}start(){const r=new T0(s=>{const l=hn.fromRawFrame(s,this._escapeHeaderValues);this.logRawCommunication||this.debug(`<<< ${l}`),(this._serverFrameHandlers[l.command]||this.onUnhandledFrame)(l)},()=>{this.debug("<<< PONG")});this._webSocket.onmessage=s=>{if(this.debug("Received data"),this._lastServerActivityTS=Date.now(),this.logRawCommunication){const l=s.data instanceof ArrayBuffer?new TextDecoder().decode(s.data):s.data;this.debug(`<<< ${l}`)}r.parseChunk(s.data,this.appendMissingNULLonIncoming)},this._webSocket.onclose=s=>{this.debug(`Connection closed to ${this._webSocket.url}`),this._cleanUp(),this.onWebSocketClose(s)},this._webSocket.onerror=s=>{this.onWebSocketError(s)},this._webSocket.onopen=()=>{const s=Object.assign({},this.connectHeaders);this.debug("Web Socket Opened..."),s["accept-version"]=this.stompVersions.supportedVersions(),s["heart-beat"]=[this.heartbeatOutgoing,this.heartbeatIncoming].join(","),this._transmit({command:"CONNECT",headers:s})}}_setupHeartbeat(r){if(r.version!==Ze.V1_1&&r.version!==Ze.V1_2||!r["heart-beat"])return;const[s,l]=r["heart-beat"].split(",").map(u=>parseInt(u,10));if(this.heartbeatOutgoing!==0&&l!==0){const u=Math.max(this.heartbeatOutgoing,l);this.debug(`send PING every ${u}ms`),this._pinger=new P0(u,this._client.heartbeatStrategy,this.debug),this._pinger.start(()=>{this._webSocket.readyState===pn.OPEN&&(this._webSocket.send(oi.LF),this.debug(">>> PING"))})}if(this.heartbeatIncoming!==0&&s!==0){const u=Math.max(this.heartbeatIncoming,s);this.debug(`check PONG every ${u}ms`),this._ponger=setInterval(()=>{const f=Date.now()-this._lastServerActivityTS;f>u*2&&(this.debug(`did not receive server activity for the last ${f}ms`),this._closeOrDiscardWebsocket())},u)}}_closeOrDiscardWebsocket(){this.discardWebsocketOnCommFailure?(this.debug("Discarding websocket, the underlying socket may linger for a while"),this.discardWebsocket()):(this.debug("Issuing close on the websocket"),this._closeWebsocket())}forceDisconnect(){this._webSocket&&(this._webSocket.readyState===pn.CONNECTING||this._webSocket.readyState===pn.OPEN)&&this._closeOrDiscardWebsocket()}_closeWebsocket(){this._webSocket.onmessage=()=>{},this._webSocket.close()}discardWebsocket(){typeof this._webSocket.terminate!="function"&&R0(this._webSocket,r=>this.debug(r)),this._webSocket.terminate()}_transmit(r){const{command:s,headers:l,body:u,binaryBody:f,skipContentLengthHeader:d}=r,p=new hn({command:s,headers:l,body:u,binaryBody:f,escapeHeaderValues:this._escapeHeaderValues,skipContentLengthHeader:d});let m=p.serialize();if(this.logRawCommunication?this.debug(`>>> ${m}`):this.debug(`>>> ${p}`),this.forceBinaryWSFrames&&typeof m=="string"&&(m=new TextEncoder().encode(m)),typeof m!="string"||!this.splitLargeFrames)this._webSocket.send(m);else{let v=m;for(;v.length>0;){const g=v.substring(0,this.maxWebSocketChunkSize);v=v.substring(this.maxWebSocketChunkSize),this._webSocket.send(g),this.debug(`chunk sent = ${g.length}, remaining = ${v.length}`)}}}dispose(){if(this.connected)try{const r=Object.assign({},this.disconnectHeaders);r.receipt||(r.receipt=`close-${this._counter++}`),this.watchForReceipt(r.receipt,s=>{this._closeWebsocket(),this._cleanUp(),this.onDisconnect(s)}),this._transmit({command:"DISCONNECT",headers:r})}catch(r){this.debug(`Ignoring error during disconnect ${r}`)}else(this._webSocket.readyState===pn.CONNECTING||this._webSocket.readyState===pn.OPEN)&&this._closeWebsocket()}_cleanUp(){this._connected=!1,this._pinger&&(this._pinger.stop(),this._pinger=void 0),this._ponger&&(clearInterval(this._ponger),this._ponger=void 0)}publish(r){const{destination:s,headers:l,body:u,binaryBody:f,skipContentLengthHeader:d}=r,p=Object.assign({destination:s},l);this._transmit({command:"SEND",headers:p,body:u,binaryBody:f,skipContentLengthHeader:d})}watchForReceipt(r,s){this._receiptWatchers[r]=s}subscribe(r,s,l={}){l=Object.assign({},l),l.id||(l.id=`sub-${this._counter++}`),l.destination=r,this._subscriptions[l.id]=s,this._transmit({command:"SUBSCRIBE",headers:l});const u=this;return{id:l.id,unsubscribe(f){return u.unsubscribe(l.id,f)}}}unsubscribe(r,s={}){s=Object.assign({},s),delete this._subscriptions[r],s.id=r,this._transmit({command:"UNSUBSCRIBE",headers:s})}begin(r){const s=r||`tx-${this._counter++}`;this._transmit({command:"BEGIN",headers:{transaction:s}});const l=this;return{id:s,commit(){l.commit(s)},abort(){l.abort(s)}}}commit(r){this._transmit({command:"COMMIT",headers:{transaction:r}})}abort(r){this._transmit({command:"ABORT",headers:{transaction:r}})}ack(r,s,l={}){l=Object.assign({},l),this._connectedVersion===Ze.V1_2?l.id=r:l["message-id"]=r,l.subscription=s,this._transmit({command:"ACK",headers:l})}nack(r,s,l={}){return l=Object.assign({},l),this._connectedVersion===Ze.V1_2?l.id=r:l["message-id"]=r,l.subscription=s,this._transmit({command:"NACK",headers:l})}}class j0{get webSocket(){var r;return(r=this._stompHandler)==null?void 0:r._webSocket}get disconnectHeaders(){return this._disconnectHeaders}set disconnectHeaders(r){this._disconnectHeaders=r,this._stompHandler&&(this._stompHandler.disconnectHeaders=this._disconnectHeaders)}get connected(){return!!this._stompHandler&&this._stompHandler.connected}get connectedVersion(){return this._stompHandler?this._stompHandler.connectedVersion:void 0}get active(){return this.state===Pt.ACTIVE}_changeState(r){this.state=r,this.onChangeState(r)}constructor(r={}){this.stompVersions=Ze.default,this.connectionTimeout=0,this.reconnectDelay=5e3,this._nextReconnectDelay=0,this.maxReconnectDelay=15*60*1e3,this.reconnectTimeMode=Mo.LINEAR,this.heartbeatIncoming=1e4,this.heartbeatOutgoing=1e4,this.heartbeatStrategy=ui.Interval,this.splitLargeFrames=!1,this.maxWebSocketChunkSize=8*1024,this.forceBinaryWSFrames=!1,this.appendMissingNULLonIncoming=!1,this.discardWebsocketOnCommFailure=!1,this.state=Pt.INACTIVE;const s=()=>{};this.debug=s,this.beforeConnect=s,this.onConnect=s,this.onDisconnect=s,this.onUnhandledMessage=s,this.onUnhandledReceipt=s,this.onUnhandledFrame=s,this.onStompError=s,this.onWebSocketClose=s,this.onWebSocketError=s,this.logRawCommunication=!1,this.onChangeState=s,this.connectHeaders={},this._disconnectHeaders={},this.configure(r)}configure(r){Object.assign(this,r),this.maxReconnectDelay>0&&this.maxReconnectDelay<this.reconnectDelay&&(this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`),this.maxReconnectDelay=this.reconnectDelay)}activate(){const r=()=>{if(this.active){this.debug("Already ACTIVE, ignoring request to activate");return}this._changeState(Pt.ACTIVE),this._nextReconnectDelay=this.reconnectDelay,this._connect()};this.state===Pt.DEACTIVATING?(this.debug("Waiting for deactivation to finish before activating"),this.deactivate().then(()=>{r()})):r()}async _connect(){if(await this.beforeConnect(this),this._stompHandler){this.debug("There is already a stompHandler, skipping the call to connect");return}if(!this.active){this.debug("Client has been marked inactive, will not attempt to connect");return}this.connectionTimeout>0&&(this._connectionWatcher&&clearTimeout(this._connectionWatcher),this._connectionWatcher=setTimeout(()=>{this.connected||(this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`),this.forceDisconnect())},this.connectionTimeout)),this.debug("Opening Web Socket...");const r=this._createWebSocket();this._stompHandler=new O0(this,r,{debug:this.debug,stompVersions:this.stompVersions,connectHeaders:this.connectHeaders,disconnectHeaders:this._disconnectHeaders,heartbeatIncoming:this.heartbeatIncoming,heartbeatOutgoing:this.heartbeatOutgoing,heartbeatStrategy:this.heartbeatStrategy,splitLargeFrames:this.splitLargeFrames,maxWebSocketChunkSize:this.maxWebSocketChunkSize,forceBinaryWSFrames:this.forceBinaryWSFrames,logRawCommunication:this.logRawCommunication,appendMissingNULLonIncoming:this.appendMissingNULLonIncoming,discardWebsocketOnCommFailure:this.discardWebsocketOnCommFailure,onConnect:s=>{if(this._connectionWatcher&&(clearTimeout(this._connectionWatcher),this._connectionWatcher=void 0),!this.active){this.debug("STOMP got connected while deactivate was issued, will disconnect now"),this._disposeStompHandler();return}this.onConnect(s)},onDisconnect:s=>{this.onDisconnect(s)},onStompError:s=>{this.onStompError(s)},onWebSocketClose:s=>{this._stompHandler=void 0,this.state===Pt.DEACTIVATING&&this._changeState(Pt.INACTIVE),this.onWebSocketClose(s),this.active&&this._schedule_reconnect()},onWebSocketError:s=>{this.onWebSocketError(s)},onUnhandledMessage:s=>{this.onUnhandledMessage(s)},onUnhandledReceipt:s=>{this.onUnhandledReceipt(s)},onUnhandledFrame:s=>{this.onUnhandledFrame(s)}}),this._stompHandler.start()}_createWebSocket(){let r;if(this.webSocketFactory)r=this.webSocketFactory();else if(this.brokerURL)r=new WebSocket(this.brokerURL,this.stompVersions.protocolVersions());else throw new Error("Either brokerURL or webSocketFactory must be provided");return r.binaryType="arraybuffer",r}_schedule_reconnect(){this._nextReconnectDelay>0&&(this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`),this._reconnector=setTimeout(()=>{this.reconnectTimeMode===Mo.EXPONENTIAL&&(this._nextReconnectDelay=this._nextReconnectDelay*2,this.maxReconnectDelay!==0&&(this._nextReconnectDelay=Math.min(this._nextReconnectDelay,this.maxReconnectDelay))),this._connect()},this._nextReconnectDelay))}async deactivate(r={}){var f;const s=r.force||!1,l=this.active;let u;if(this.state===Pt.INACTIVE)return this.debug("Already INACTIVE, nothing more to do"),Promise.resolve();if(this._changeState(Pt.DEACTIVATING),this._nextReconnectDelay=0,this._reconnector&&(clearTimeout(this._reconnector),this._reconnector=void 0),this._stompHandler&&this.webSocket.readyState!==pn.CLOSED){const d=this._stompHandler.onWebSocketClose;u=new Promise((p,m)=>{this._stompHandler.onWebSocketClose=v=>{d(v),p()}})}else return this._changeState(Pt.INACTIVE),Promise.resolve();return s?(f=this._stompHandler)==null||f.discardWebsocket():l&&this._disposeStompHandler(),u}forceDisconnect(){this._stompHandler&&this._stompHandler.forceDisconnect()}_disposeStompHandler(){this._stompHandler&&this._stompHandler.dispose()}publish(r){this._checkConnection(),this._stompHandler.publish(r)}_checkConnection(){if(!this.connected)throw new TypeError("There is no underlying STOMP connection")}watchForReceipt(r,s){this._checkConnection(),this._stompHandler.watchForReceipt(r,s)}subscribe(r,s,l={}){return this._checkConnection(),this._stompHandler.subscribe(r,s,l)}unsubscribe(r,s={}){this._checkConnection(),this._stompHandler.unsubscribe(r,s)}begin(r){return this._checkConnection(),this._stompHandler.begin(r)}commit(r){this._checkConnection(),this._stompHandler.commit(r)}abort(r){this._checkConnection(),this._stompHandler.abort(r)}ack(r,s,l={}){this._checkConnection(),this._stompHandler.ack(r,s,l)}nack(r,s,l={}){this._checkConnection(),this._stompHandler.nack(r,s,l)}}let Dt=null,Vt={},ar=!1;const Yp=()=>new Promise((i,r)=>{if(Ke()){console.log("Using mock WebSocket service"),setTimeout(()=>{ar=!0,console.log("Mock WebSocket connected"),i()},500);return}try{const s=new C0("/ws");Dt=new j0({webSocketFactory:()=>s,debug:l=>{console.log("STOMP Debug:",l)},reconnectDelay:5e3,heartbeatIncoming:4e3,heartbeatOutgoing:4e3,onConnect:()=>{console.log("Connected to WebSocket"),i()},onStompError:l=>{console.error("WebSocket connection error:",l),r(l)}}),Dt.activate()}catch(s){console.error("Error setting up WebSocket:",s),Ke()?setTimeout(()=>{ar=!0,console.log("Fallback to mock WebSocket after connection error"),i()},500):r(s)}}),Zp=()=>{if(Ke()&&ar){ar=!1,Vt={},console.log("Mock WebSocket disconnected");return}if(Dt)try{Dt.deactivate(),console.log("Disconnected from WebSocket")}catch(i){console.error("Error disconnecting from WebSocket:",i)}},tp=(i,r)=>{if(Ke()){if(!ar)return console.error("Mock WebSocket not connected"),null;Vt[i]||(Vt[i]=[]);const s=Date.now().toString();return Vt[i].push({id:s,callback:r}),{id:s,unsubscribe:()=>{Vt[i]&&(Vt[i]=Vt[i].filter(l=>l.id!==s))}}}return!Dt||!Dt.connected?(console.error("STOMP client not connected"),null):Dt.subscribe(i,s=>{try{const l=JSON.parse(s.body);r(l)}catch(l){console.error("Error parsing message:",l)}})},L0=(i,r)=>{if(Ke()){if(!ar){console.error("Mock WebSocket not connected");return}console.log(`Mock sending message to ${i}:`,r),setTimeout(()=>{if(Vt[i]){const s=JSON.stringify(r);Vt[i].forEach(l=>{try{l.callback(JSON.parse(s))}catch(u){console.error("Error in mock subscription callback:",u)}})}},100);return}if(!Dt||!Dt.connected){console.error("STOMP client not connected");return}Dt.publish({destination:i,headers:{},body:JSON.stringify(r)})},I0=({messages:i,currentUser:r})=>{const s=l=>new Date(l).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return i.length===0?P.jsx("div",{className:"no-messages",children:"No messages yet. Start the conversation!"}):P.jsx("div",{className:"message-list",children:i.map((l,u)=>{const f=l.sender===r;return P.jsxs("div",{className:`message ${f?"message-own":"message-other"}`,children:[!f&&P.jsx("div",{className:"message-sender",children:l.sender}),P.jsxs("div",{className:"message-bubble",children:[P.jsx("div",{className:"message-content",children:l.content}),P.jsx("div",{className:"message-time",children:s(l.timestamp)})]})]},l.id||u)})})},b0=({onSendMessage:i,disabled:r=!1})=>{const[s,l]=F.useState(""),u=d=>{d.preventDefault(),s.trim()&&!r&&(i(s),l(""))},f=d=>{d.key==="Enter"&&!d.shiftKey&&(d.preventDefault(),u(d))};return P.jsxs("form",{className:"message-input-container",onSubmit:u,children:[P.jsx("textarea",{className:"message-input",value:s,onChange:d=>l(d.target.value),onKeyDown:f,placeholder:"Type a message...",disabled:r}),P.jsx("button",{type:"submit",className:"send-button",disabled:!s.trim()||r,children:"Send"})]})},A0=({users:i})=>P.jsxs("div",{className:"user-list",children:[P.jsx("h3",{className:"user-list-title",children:"Online Users"}),i.length===0?P.jsx("div",{className:"no-users",children:"No users online"}):P.jsx("ul",{className:"users",children:i.map((r,s)=>P.jsxs("li",{className:"user-item",children:[P.jsx("div",{className:"user-avatar",children:r.charAt(0).toUpperCase()}),P.jsx("div",{className:"user-name",children:r}),P.jsx("div",{className:"user-status online"})]},s))})]}),U0=()=>{const[i,r]=F.useState([]),[s,l]=F.useState(!0),[u,f]=F.useState(null),{user:d}=F.useContext(mr),p=F.useRef(null),[m,v]=F.useState([]);F.useEffect(()=>{Ke()?v(su.map(y=>y.username)):v(["user1","user2","user3"])},[]),F.useEffect(()=>{(async()=>{try{l(!0);const x=await Jw();r(x),f(null)}catch(x){console.error("Error fetching messages:",x),f("Failed to load messages. Please try again later.")}finally{l(!1)}})();let _=null,D=null;return(async()=>{try{await Yp(),console.log("WebSocket connected successfully"),_=tp("/topic/messages",x=>{console.log("Received broadcast message:",x),r(N=>N.some(M=>M.id===x.id||M.sender===x.sender&&M.timestamp===x.timestamp&&M.content===x.content)?N:[...N,x])}),d!=null&&d.username&&(D=tp(`/user/${d.username}/queue/messages`,x=>{console.log("Received direct message:",x),r(N=>N.some(M=>M.id===x.id||M.sender===x.sender&&M.timestamp===x.timestamp&&M.content===x.content)?N:[...N,x])}))}catch(x){console.error("WebSocket connection error:",x)}})(),()=>{try{console.log("Cleaning up WebSocket connections"),_&&_.unsubscribe(),D&&D.unsubscribe(),Zp()}catch(x){console.error("Error during cleanup:",x)}}},[d==null?void 0:d.username]),F.useEffect(()=>{var y;(y=p.current)==null||y.scrollIntoView({behavior:"smooth"})},[i]);const g=async y=>{if(!y.trim()||!d)return;const _={sender:d.username,content:y,timestamp:new Date().toISOString()};try{r(D=>[...D,_]),L0("/app/chat",_);try{await Qw(_)}catch(D){console.error("Error saving message to database:",D)}}catch(D){console.error("Error sending message:",D),f("Failed to send message. Please try again.")}};return P.jsxs("div",{className:"chat-container",children:[P.jsx("div",{className:"chat-sidebar",children:P.jsx(A0,{users:m})}),P.jsxs("div",{className:"chat-main",children:[u&&P.jsx("div",{className:"error-banner",children:u}),P.jsxs("div",{className:"messages-container",children:[s?P.jsx("div",{className:"loading-messages",children:"Loading messages..."}):P.jsx(I0,{messages:i,currentUser:(d==null?void 0:d.username)||""}),P.jsx("div",{ref:p})]}),P.jsx(b0,{onSendMessage:g,disabled:s})]})]})},D0=()=>P.jsx("div",{className:"home-container",children:P.jsxs("div",{className:"home-content",children:[P.jsx("h1",{children:"Welcome to MyChatApp"}),P.jsx("p",{children:"A simple and secure way to chat with your friends and colleagues."}),P.jsxs("div",{className:"home-buttons",children:[P.jsx(St,{to:"/login",className:"home-button login-button",children:"Login"}),P.jsx(St,{to:"/register",className:"home-button register-button",children:"Register"})]}),P.jsxs("div",{className:"home-features",children:[P.jsxs("div",{className:"feature",children:[P.jsx("h3",{children:"Real-time Chat"}),P.jsx("p",{children:"Instant messaging with real-time updates"})]}),P.jsxs("div",{className:"feature",children:[P.jsx("h3",{children:"Secure"}),P.jsx("p",{children:"Your conversations are protected"})]}),P.jsxs("div",{className:"feature",children:[P.jsx("h3",{children:"User Friendly"}),P.jsx("p",{children:"Simple and intuitive interface"})]})]})]})}),F0=({darkMode:i,toggleDarkMode:r})=>{const{isAuthenticated:s,user:l,logout:u}=F.useContext(mr),f=dr(),[d,p]=F.useState(!1);F.useEffect(()=>{const v=localStorage.getItem("useMockServices");p(v==="true")},[]);const m=()=>{u(),f("/login")};return P.jsx("header",{className:"header",children:P.jsxs("div",{className:"header-container",children:[P.jsx("div",{className:"logo",children:P.jsx(St,{to:"/",children:"MyChatApp"})}),P.jsxs("div",{className:"header-right",children:[P.jsxs("div",{className:"header-buttons",children:[!1,P.jsx("button",{className:"theme-toggle",onClick:r,"aria-label":i?"Switch to light mode":"Switch to dark mode",children:i?"☀️":"🌙"})]}),s?P.jsxs("div",{className:"user-menu",children:[P.jsxs("span",{className:"username",children:["Hello, ",l==null?void 0:l.name]}),P.jsx("button",{className:"logout-button",onClick:m,children:"Logout"})]}):P.jsxs("div",{className:"auth-links",children:[P.jsx(St,{to:"/login",className:"auth-link",children:"Login"}),P.jsx(St,{to:"/register",className:"auth-link register",children:"Register"})]})]})]})})},M0=({children:i})=>{const{isAuthenticated:r,loading:s}=F.useContext(mr);return s?P.jsx("div",{className:"loading",children:"Loading..."}):r?P.jsx(P.Fragment,{children:i}):P.jsx(Qg,{to:"/login",replace:!0})};function B0(){const[i,r]=F.useState(!1);F.useEffect(()=>{const l=localStorage.getItem("darkMode");l&&r(JSON.parse(l))},[]),F.useEffect(()=>{document.body.className=i?"dark-theme":"light-theme",localStorage.setItem("darkMode",JSON.stringify(i))},[i]);const s=()=>{r(!i)};return F.useEffect(()=>(Ke()?console.log("Using mock services"):(console.log("Using real services"),Yp().catch(l=>{console.error("Failed to connect to chat:",l)})),()=>{Ke()||Zp()}),[]),P.jsx(Ww,{children:P.jsx(iy,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:P.jsxs("div",{className:"app",children:[P.jsx(F0,{darkMode:i,toggleDarkMode:s}),P.jsx("main",{className:"main-content",children:P.jsxs(Gg,{children:[P.jsx(In,{path:"/login",element:P.jsx(qw,{})}),P.jsx(In,{path:"/register",element:P.jsx($w,{})}),P.jsx(In,{path:"/forgot-password",element:P.jsx(Vw,{})}),P.jsx(In,{path:"/reset-password",element:P.jsx(Xw,{})}),P.jsx(In,{path:"/chat",element:P.jsx(M0,{children:P.jsx(U0,{})})}),P.jsx(In,{path:"/",element:P.jsx(D0,{})})]})})]})})})}const np=document.getElementById("root");np?og.createRoot(np).render(P.jsx(F.StrictMode,{children:P.jsx(B0,{})})):console.error("Root element not found!");
