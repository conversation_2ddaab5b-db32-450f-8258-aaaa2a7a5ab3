package com.chat.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "contacts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Contact {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String ownerUsername; // Who owns this contact

    @Column(nullable = false)
    private String contactUsername; // The contact's username

    private String displayName; // Custom display name for the contact

    private String phoneNumber; // Contact's phone number

    private boolean isBlocked = false;

    private boolean isFavorite = false;

    private LocalDateTime addedAt;

    private LocalDateTime lastInteraction;

    @PrePersist
    protected void onCreate() {
        addedAt = LocalDateTime.now();
    }
}
