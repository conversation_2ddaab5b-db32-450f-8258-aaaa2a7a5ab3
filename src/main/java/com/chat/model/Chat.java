package com.chat.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "chats")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Chat {

    public enum ChatType {
        PRIVATE,
        GROUP
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String chatId; // Unique chat identifier

    @Enumerated(EnumType.STRING)
    private ChatType type = ChatType.PRIVATE;

    private String name; // Chat name (for groups)

    private String description; // Chat description (for groups)

    private String profilePicture; // Chat profile picture URL

    @ElementCollection
    @CollectionTable(name = "chat_participants", joinColumns = @JoinColumn(name = "chat_id"))
    @Column(name = "username")
    private List<String> participants;

    private String createdBy; // Username of chat creator

    private LocalDateTime createdAt;

    private LocalDateTime lastActivity;

    private String lastMessage; // Preview of last message

    private String lastMessageSender;

    private LocalDateTime lastMessageTime;

    private boolean isActive = true;

    // Group chat specific fields
    private String adminUsers; // JSON array of admin usernames

    private boolean allowMembersToAddOthers = true;

    private boolean allowMembersToEditInfo = false;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        lastActivity = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        lastActivity = LocalDateTime.now();
    }
}
