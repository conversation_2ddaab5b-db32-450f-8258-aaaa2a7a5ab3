package com.chat.repository;

import com.chat.model.Chat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChatRepository extends JpaRepository<Chat, Long> {

    Optional<Chat> findByChatId(String chatId);

    @Query("SELECT c FROM Chat c WHERE :username MEMBER OF c.participants ORDER BY c.lastActivity DESC")
    List<Chat> findChatsByParticipant(@Param("username") String username);

    @Query("SELECT c FROM Chat c WHERE :username MEMBER OF c.participants AND c.type = 'PRIVATE' ORDER BY c.lastActivity DESC")
    List<Chat> findPrivateChatsByParticipant(@Param("username") String username);

    @Query("SELECT c FROM Chat c WHERE :username MEMBER OF c.participants AND c.type = 'GROUP' ORDER BY c.lastActivity DESC")
    List<Chat> findGroupChatsByParticipant(@Param("username") String username);

    @Query("SELECT c FROM Chat c WHERE c.type = 'PRIVATE' AND :user1 MEMBER OF c.participants AND :user2 MEMBER OF c.participants")
    Optional<Chat> findPrivateChatBetweenUsers(@Param("user1") String user1, @Param("user2") String user2);

    List<Chat> findByNameContainingIgnoreCaseAndTypeOrderByLastActivityDesc(String name, Chat.ChatType type);
}
