package com.chat.repository;

import com.chat.model.Contact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ContactRepository extends JpaRepository<Contact, Long> {

    List<Contact> findByOwnerUsernameOrderByDisplayNameAsc(String ownerUsername);

    Optional<Contact> findByOwnerUsernameAndContactUsername(String ownerUsername, String contactUsername);

    List<Contact> findByOwnerUsernameAndIsFavoriteTrue(String ownerUsername);

    List<Contact> findByOwnerUsernameAndIsBlockedFalse(String ownerUsername);

    @Query("SELECT c FROM Contact c WHERE c.ownerUsername = :ownerUsername AND " +
           "(LOWER(c.displayName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.contactUsername) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    List<Contact> searchContacts(@Param("ownerUsername") String ownerUsername, @Param("searchTerm") String searchTerm);

    boolean existsByOwnerUsernameAndContactUsername(String ownerUsername, String contactUsername);
}
