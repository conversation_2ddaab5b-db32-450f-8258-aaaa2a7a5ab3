{"version": 3, "file": "router.umd.min.js", "sources": ["../history.ts", "../utils.ts", "../router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  // We keep the raw Response for redirects so we can return it verbatim\n  response: Response;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\ntype DataFunctionReturnValue = Promise<DataFunctionValue> | DataFunctionValue;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (\n    args: LoaderFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (\n    args: ActionFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  actionStatus?: number;\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\nexport interface DataStrategyMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {\n  shouldLoad: boolean;\n  resolve: (\n    handlerOverride?: (\n      handler: (ctx?: unknown) => DataFunctionReturnValue\n    ) => DataFunctionReturnValue\n  ) => Promise<DataStrategyResult>;\n}\n\nexport interface DataStrategyFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {\n  matches: DataStrategyMatch[];\n  fetcherKey: string | null;\n}\n\n/**\n * Result from a loader or action called via dataStrategy\n */\nexport interface DataStrategyResult {\n  type: \"data\" | \"error\";\n  result: unknown; // data, Error, Response, DeferredData, DataWithResponseInit\n}\n\nexport interface DataStrategyFunction {\n  (args: DataStrategyFunctionArgs): Promise<Record<string, DataStrategyResult>>;\n}\n\nexport type AgnosticPatchRoutesOnNavigationFunctionArgs<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = {\n  signal: AbortSignal;\n  path: string;\n  matches: M[];\n  fetcherKey: string | undefined;\n  patch: (routeId: string | null, children: O[]) => void;\n};\n\nexport type AgnosticPatchRoutesOnNavigationFunction<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = (\n  opts: AgnosticPatchRoutesOnNavigationFunctionArgs<O, M>\n) => void | Promise<void>;\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction | boolean;\n  action?: ActionFunction | boolean;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: string[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/v6/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\n\nexport function matchRoutesImpl<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename: string,\n  allowPartial: boolean\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string,\n  allowPartial = false\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    let route = meta.route;\n\n    if (\n      !match &&\n      end &&\n      allowPartial &&\n      !routesMeta[routesMeta.length - 1].route.index\n    ) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false,\n        },\n        remainingPathname\n      );\n    }\n\n    if (!match) {\n      return null;\n    }\n\n    Object.assign(matchedParams, match.params);\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/v6/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/v6/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nexport function decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/v6/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n *\n * @deprecated The `json` method is deprecated in favor of returning raw objects.\n * This method will be removed in v7.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport class DataWithResponseInit<D> {\n  type: string = \"DataWithResponseInit\";\n  data: D;\n  init: ResponseInit | null;\n\n  constructor(data: D, init?: ResponseInit) {\n    this.data = data;\n    this.init = init || null;\n  }\n}\n\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nexport function data<D>(data: D, init?: number | ResponseInit) {\n  return new DataWithResponseInit(\n    data,\n    typeof init === \"number\" ? { status: init } : init\n  );\n}\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\n/**\n * @deprecated The `defer` method is deprecated in favor of returning raw\n * objects. This method will be removed in v7.\n */\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const replace: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DataStrategyMatch,\n  AgnosticRouteObject,\n  DataResult,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  DataStrategyResult,\n  ImmutableRouteKey,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n  AgnosticPatchRoutesOnNavigationFunction,\n  DataWithResponseInit,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  matchRoutesImpl,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE DO NOT USE\n   *\n   * Patch additional children routes into an existing parent route\n   * @param routeId The parent route id or a callback function accepting `patch`\n   *                to perform batch patching\n   * @param children The additional children routes\n   */\n  patchRoutes(routeId: string | null, children: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n  v7_skipActionErrorRevalidation: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: AgnosticPatchRoutesOnNavigationFunction;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: {\n      routeId?: string;\n      requestContext?: unknown;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      viewTransitionOpts?: ViewTransitionOpts;\n      flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ntype PendingActionResult = [string, SuccessResult | ErrorResult];\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * Tuple for the returned or thrown value from the current action.  The routeId\n   * is the action route for success and the bubbled boundary route for errors.\n   */\n  pendingActionResult?: PendingActionResult;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialMatchesIsFOW = false;\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(\n      initialMatches,\n      dataRoutes,\n      init.history.location.pathname\n    );\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n\n  let initialized: boolean;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(\n        null,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatchesIsFOW = true;\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some((m) => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some((m) => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(\n        (m) => errors![m.route.id] !== undefined\n      );\n      initialized = initialMatches\n        .slice(0, idx + 1)\n        .every((m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(\n        (m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors)\n      );\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: Set<string> = new Set();\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Map of pending patchRoutesOnNavigation() promises (keyed by path/matches) so\n  // that we only kick them off once for a given combo\n  let pendingPatchRoutes = new Map<\n    string,\n    ReturnType<AgnosticPatchRoutesOnNavigationFunction>\n  >();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate: (() => void) | undefined = undefined;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (unblockBlockerHistoryUpdate) {\n          unblockBlockerHistoryUpdate();\n          unblockBlockerHistoryUpdate = undefined;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          let nextHistoryUpdatePromise = new Promise<void>((resolve) => {\n            unblockBlockerHistoryUpdate = resolve;\n          });\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked, after the url\n              // restoration is also complete.  See:\n              // https://github.com/remix-run/react-router/issues/11613\n              nextHistoryUpdatePromise.then(() => init.history.go(delta));\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Remove any lingering deleted fetchers that have already been removed\n    // from state.fetchers\n    deletedFetchers.forEach((key) => {\n      if (!state.fetchers.has(key) && !fetchControllers.has(key)) {\n        deletedFetchersKeys.push(key);\n      }\n    });\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        viewTransitionOpts: opts.viewTransitionOpts,\n        flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    } else {\n      // We already called deleteFetcher() on these, can remove them from this\n      // Set now that we've handed the keys off to the data layer\n      deletedFetchersKeys.forEach((key) => deletedFetchers.delete(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      {\n        overrideNavigation: state.navigation,\n        // Proxy through any rending view transition\n        enableViewTransition: pendingViewTransitionEnabled === true,\n      }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches =\n      opts?.initialHydration &&\n      state.matches &&\n      state.matches.length > 0 &&\n      !initialMatchesIsFOW\n        ? // `matchRoutes()` has already been called if we're in here via `router.initialize()`\n          state.matches\n        : matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      matches &&\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionResult: PendingActionResult | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: ResultType.error, error: opts.pendingError },\n      ];\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        fogOfWar.active,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionResult.shortCircuited) {\n        return;\n      }\n\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (\n          isErrorResult(result) &&\n          isRouteErrorResponse(result.error) &&\n          result.error.status === 404\n        ) {\n          pendingNavigationController = null;\n\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error,\n            },\n          });\n          return;\n        }\n      }\n\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n\n      // Create a GET request for the loaders\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors,\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [\n            boundaryId,\n            {\n              type: ResultType.error,\n              error: discoverResult.error,\n            },\n          ],\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        state,\n        request,\n        [actionMatch],\n        matches,\n        null\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\")!,\n          new URL(request.url),\n          basename\n        );\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result],\n      };\n    }\n\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result],\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionResult?: PendingActionResult\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState =\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration);\n\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...(actionData !== undefined ? { actionData } : {}),\n          },\n          {\n            flushSync,\n          }\n        );\n      }\n\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error,\n          },\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionResult\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors:\n            pendingActionResult && isErrorResult(pendingActionResult[1])\n              ? { [pendingActionResult[0]]: pendingActionResult[1].error }\n              : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    if (shouldUpdateNavigationState) {\n      let updates: Partial<RouterState> = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = { ...state.errors, ...errors };\n    }\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getUpdatedActionData(\n    pendingActionResult: PendingActionResult | undefined\n  ): Record<string, RouteData> | null | undefined {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data as any,\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n\n  function getUpdatedRevalidatingFetchers(\n    revalidatingFetchers: RevalidatingFetcher[]\n  ) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        undefined,\n        fetcher ? fetcher.data : undefined\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    abortFetcher(key);\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        fogOfWar.active,\n        flushSync,\n        preventScrollReset,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      fogOfWar.active,\n      flushSync,\n      preventScrollReset,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    function detectAndHandle405Error(m: AgnosticDataRouteMatch) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId,\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        new URL(fetchRequest.url).pathname,\n        fetchRequest.signal,\n        key\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\n      \"action\",\n      state,\n      fetchRequest,\n      [match],\n      requestMatches,\n      key\n    );\n    let actionResult = actionResults[match.route.id];\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      [match.route.id, actionResult]\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        abortFetcher(staleKey);\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        new URL(fetchRequest.url).pathname,\n        fetchRequest.signal,\n        key\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\n      \"loader\",\n      state,\n      fetchRequest,\n      [match],\n      matches,\n      key\n    );\n    let result = results[match.route.id];\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset,\n        });\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    request: Request,\n    redirect: RedirectResult,\n    isNavigation: boolean,\n    {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true,\n    });\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true || redirect.response.headers.has(\"X-Remix-Replace\")\n        ? HistoryAction.Replace\n        : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.response.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location,\n        },\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    }\n  }\n\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    state: RouterState,\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    fetcherKey: string | null\n  ): Promise<Record<string, DataResult>> {\n    let results: Record<string, DataStrategyResult>;\n    let dataResults: Record<string, DataResult> = {};\n    try {\n      results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        type,\n        state,\n        request,\n        matchesToLoad,\n        matches,\n        fetcherKey,\n        manifest,\n        mapRouteProperties\n      );\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach((m) => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e,\n        };\n      });\n      return dataResults;\n    }\n\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result as Response;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            routeId,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          ),\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(\n          result\n        );\n      }\n    }\n\n    return dataResults;\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    state: RouterState,\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    let currentMatches = state.matches;\n\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\n      \"loader\",\n      state,\n      request,\n      matchesToLoad,\n      matches,\n      null\n    );\n\n    let fetcherResultsPromise = Promise.all(\n      fetchersToLoad.map(async (f) => {\n        if (f.matches && f.match && f.controller) {\n          let results = await callDataStrategy(\n            \"loader\",\n            state,\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            [f.match],\n            f.matches,\n            f.key\n          );\n          let result = results[f.match.route.id];\n          // Fetcher results are keyed by fetcher key from here on out, not routeId\n          return { [f.key]: result };\n        } else {\n          return Promise.resolve({\n            [f.key]: {\n              type: ResultType.error,\n              error: getInternalRouterError(404, {\n                pathname: f.path,\n              }),\n            } as ErrorResult,\n          });\n        }\n      })\n    );\n\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce(\n      (acc, r) => Object.assign(acc, r),\n      {}\n    );\n\n    await Promise.all([\n      resolveNavigationDeferredResults(\n        matches,\n        loaderResults,\n        request.signal,\n        currentMatches,\n        state.loaderData\n      ),\n      resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad),\n    ]);\n\n    return {\n      loaderResults,\n      fetcherResults,\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n    // If this fetcher was previously marked for deletion, unmark it since we\n    // have a new instance\n    if (deletedFetchers.has(key)) {\n      deletedFetchers.delete(key);\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n\n    // If we opted into the flag we can clear this now since we're calling\n    // deleteFetcher() at the end of updateState() and we've already handed the\n    // deleted fetcher keys off to the data layer.\n    // If not, we're eagerly calling deleteFetcher() and we need to keep this\n    // Set populated until the next updateState call, and we'll clear\n    // `deletedFetchers` then\n    if (future.v7_fetcherPersist) {\n      deletedFetchers.delete(key);\n    }\n\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    let count = (activeFetchers.get(key) || 0) - 1;\n    if (count <= 0) {\n      activeFetchers.delete(key);\n      deletedFetchers.add(key);\n      if (!future.v7_fetcherPersist) {\n        deleteFetcher(key);\n      }\n    } else {\n      activeFetchers.set(key, count);\n    }\n\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function handleNavigational404(pathname: string) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function checkFogOfWar(\n    matches: AgnosticDataRouteMatch[] | null,\n    routesToUse: AgnosticDataRouteObject[],\n    pathname: string\n  ): { active: boolean; matches: AgnosticDataRouteMatch[] | null } {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n\n    return { active: false, matches: null };\n  }\n\n  type DiscoverRoutesSuccessResult = {\n    type: \"success\";\n    matches: AgnosticDataRouteMatch[] | null;\n  };\n  type DiscoverRoutesErrorResult = {\n    type: \"error\";\n    error: any;\n    partialMatches: AgnosticDataRouteMatch[];\n  };\n  type DiscoverRoutesAbortedResult = { type: \"aborted\" };\n  type DiscoverRoutesResult =\n    | DiscoverRoutesSuccessResult\n    | DiscoverRoutesErrorResult\n    | DiscoverRoutesAbortedResult;\n\n  async function discoverRoutes(\n    matches: AgnosticDataRouteMatch[],\n    pathname: string,\n    signal: AbortSignal,\n    fetcherKey?: string\n  ): Promise<DiscoverRoutesResult> {\n    if (!patchRoutesOnNavigationImpl) {\n      return { type: \"success\", matches };\n    }\n\n    let partialMatches: AgnosticDataRouteMatch[] | null = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          signal,\n          path: pathname,\n          matches: partialMatches,\n          fetcherKey,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(\n              routeId,\n              children,\n              routesToUse,\n              localManifest,\n              mapRouteProperties\n            );\n          },\n        });\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return { type: \"success\", matches: newMatches };\n      }\n\n      let newPartialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n\n      // Avoid loops if the second pass results in the same partial matches\n      if (\n        !newPartialMatches ||\n        (partialMatches.length === newPartialMatches.length &&\n          partialMatches.every(\n            (m, i) => m.route.id === newPartialMatches![i].route.id\n          ))\n      ) {\n        return { type: \"success\", matches: null };\n      }\n\n      partialMatches = newPartialMatches;\n    }\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  function patchRoutes(\n    routeId: string | null,\n    children: AgnosticRouteObject[]\n  ): void {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties\n    );\n\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(\n    request: Request,\n    {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      routeId?: string;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      false,\n      match\n    );\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches,\n        isRouteRequest,\n        requestContext,\n        dataStrategy\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")!,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling\n        ? actionMatch\n        : findNearestBoundary(matches, actionMatch.route.id);\n\n      let context = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        [boundaryMatch.route.id, result]\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : result.statusCode != null\n          ? result.statusCode\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      dataStrategy,\n      skipLoaderErrorBubbling,\n      null\n    );\n\n    return {\n      ...context,\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionHeaders: result.headers\n        ? { [actionMatch.route.id]: result.headers }\n        : {},\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null,\n    pendingActionResult?: PendingActionResult\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0])\n      : matches;\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors:\n          pendingActionResult && isErrorResult(pendingActionResult[1])\n            ? {\n                [pendingActionResult[0]]: pendingActionResult[1].error,\n              }\n            : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await callDataStrategy(\n      \"loader\",\n      request,\n      matchesToLoad,\n      matches,\n      isRouteRequest,\n      requestContext,\n      dataStrategy\n    );\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      results,\n      pendingActionResult,\n      activeDeferreds,\n      skipLoaderErrorBubbling\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    isRouteRequest: boolean,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null\n  ): Promise<Record<string, DataResult>> {\n    let results = await callDataStrategyImpl(\n      dataStrategy || defaultDataStrategy,\n      type,\n      null,\n      request,\n      matchesToLoad,\n      matches,\n      null,\n      manifest,\n      mapRouteProperties,\n      requestContext\n    );\n\n    let dataResults: Record<string, DataResult> = {};\n    await Promise.all(\n      matches.map(async (match) => {\n        if (!(match.route.id in results)) {\n          return;\n        }\n        let result = results[match.route.id];\n        if (isRedirectDataStrategyResultResult(result)) {\n          let response = result.result as Response;\n          // Throw redirects and let the server handle them with an HTTP redirect\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            match.route.id,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          // For SSR single-route requests, we want to hand Responses back\n          // directly without unwrapping\n          throw result;\n        }\n\n        dataResults[match.route.id] =\n          await convertDataStrategyResultToDataResult(result);\n      })\n    );\n    return dataResults;\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search\n        ? path.search.replace(/^\\?/, \"?index&\")\n        : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId: string,\n  includeBoundary = false\n) {\n  let index = matches.findIndex((m) => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  initialHydration: boolean,\n  skipActionErrorRevalidation: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: Set<string>,\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionResult?: PendingActionResult\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingActionResult\n    ? isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : pendingActionResult[1].data\n    : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      Object.keys(state.errors)[0],\n      true\n    );\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      pendingActionResult[0]\n    );\n  }\n\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult\n    ? pendingActionResult[1].statusCode\n    : undefined;\n  let shouldSkipRevalidation =\n    skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation\n        ? false\n        : // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n          isRevalidationRequired ||\n          currentUrl.pathname + currentUrl.search ===\n            nextUrl.pathname + nextUrl.search ||\n          // Search params affect all loaders\n          currentUrl.search !== nextUrl.search ||\n          isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      initialHydration ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation\n          ? false\n          : isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction shouldLoadRouteOnHydration(\n  route: AgnosticDataRouteObject,\n  loaderData: RouteData | null | undefined,\n  errors: RouteData | null | undefined\n) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\nfunction patchRoutesImpl(\n  routeId: string | null,\n  children: AgnosticRouteObject[],\n  routesToUse: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction\n) {\n  let childrenToPatch: AgnosticDataRouteObject[];\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(\n    (newRoute) =>\n      !childrenToPatch.some((existingRoute) =>\n        isSameRoute(newRoute, existingRoute)\n      )\n  );\n\n  let newRoutes = convertRoutesToDataRoutes(\n    uniqueChildren,\n    mapRouteProperties,\n    [routeId || \"_\", \"patch\", String(childrenToPatch?.length || \"0\")],\n    manifest\n  );\n\n  childrenToPatch.push(...newRoutes);\n}\n\nfunction isSameRoute(\n  newRoute: AgnosticRouteObject,\n  existingRoute: AgnosticRouteObject\n): boolean {\n  // Most optimal check is by id\n  if (\n    \"id\" in newRoute &&\n    \"id\" in existingRoute &&\n    newRoute.id === existingRoute.id\n  ) {\n    return true;\n  }\n\n  // Second is by pathing differences\n  if (\n    !(\n      newRoute.index === existingRoute.index &&\n      newRoute.path === existingRoute.path &&\n      newRoute.caseSensitive === existingRoute.caseSensitive\n    )\n  ) {\n    return false;\n  }\n\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if (\n    (!newRoute.children || newRoute.children.length === 0) &&\n    (!existingRoute.children || existingRoute.children.length === 0)\n  ) {\n    return true;\n  }\n\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children!.every((aChild, i) =>\n    existingRoute.children?.some((bChild) => isSameRoute(aChild, bChild))\n  );\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy({\n  matches,\n}: DataStrategyFunctionArgs): ReturnType<DataStrategyFunction> {\n  let matchesToLoad = matches.filter((m) => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map((m) => m.resolve()));\n  return results.reduce(\n    (acc, result, i) =>\n      Object.assign(acc, { [matchesToLoad[i].route.id]: result }),\n    {}\n  );\n}\n\nasync function callDataStrategyImpl(\n  dataStrategyImpl: DataStrategyFunction,\n  type: \"loader\" | \"action\",\n  state: RouterState | null,\n  request: Request,\n  matchesToLoad: AgnosticDataRouteMatch[],\n  matches: AgnosticDataRouteMatch[],\n  fetcherKey: string | null,\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  requestContext?: unknown\n): Promise<Record<string, DataStrategyResult>> {\n  let loadRouteDefinitionsPromises = matches.map((m) =>\n    m.route.lazy\n      ? loadLazyRouteModule(m.route, mapRouteProperties, manifest)\n      : undefined\n  );\n\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some((m) => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve: DataStrategyMatch[\"resolve\"] = async (handlerOverride) => {\n      if (\n        handlerOverride &&\n        request.method === \"GET\" &&\n        (match.route.lazy || match.route.loader)\n      ) {\n        shouldLoad = true;\n      }\n      return shouldLoad\n        ? callLoaderOrAction(\n            type,\n            request,\n            match,\n            loadRoutePromise,\n            handlerOverride,\n            requestContext\n          )\n        : Promise.resolve({ type: ResultType.data, result: undefined });\n    };\n\n    return {\n      ...match,\n      shouldLoad,\n      resolve,\n    };\n  });\n\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext,\n  });\n\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n\n  return results;\n}\n\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  loadRoutePromise: Promise<void> | undefined,\n  handlerOverride: Parameters<DataStrategyMatch[\"resolve\"]>[0],\n  staticContext?: unknown\n): Promise<DataStrategyResult> {\n  let result: DataStrategyResult;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (\n    handler: AgnosticRouteObject[\"loader\"] | AgnosticRouteObject[\"action\"]\n  ): Promise<DataStrategyResult> => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise<DataStrategyResult>((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n\n    let actualHandler = (ctx?: unknown) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean ` +\n              `\"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: staticContext,\n        },\n        ...(ctx !== undefined ? [ctx] : [])\n      );\n    };\n\n    let handlerPromise: Promise<DataStrategyResult> = (async () => {\n      try {\n        let val = await (handlerOverride\n          ? handlerOverride((ctx: unknown) => actualHandler(ctx))\n          : actualHandler());\n        return { type: \"data\", result: val };\n      } catch (e) {\n        return { type: \"error\", result: e };\n      }\n    })();\n\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadRoutePromise,\n        ]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value!;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, result: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result.result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return { type: ResultType.error, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  return result;\n}\n\nasync function convertDataStrategyResultToDataResult(\n  dataStrategyResult: DataStrategyResult\n): Promise<DataResult> {\n  let { result, type } = dataStrategyResult;\n\n  if (isResponse(result)) {\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      if (result.data instanceof Error) {\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: result.init?.status,\n          headers: result.init?.headers\n            ? new Headers(result.init.headers)\n            : undefined,\n        };\n      }\n\n      // Convert thrown data() to ErrorResponse instances\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(\n          result.init?.status || 500,\n          undefined,\n          result.data\n        ),\n        statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n        headers: result.init?.headers\n          ? new Headers(result.init.headers)\n          : undefined,\n      };\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n    };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  if (isDataWithResponseInit(result)) {\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: result.init?.status,\n      headers: result.init?.headers\n        ? new Headers(result.init.headers)\n        : undefined,\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(\n  response: Response,\n  request: Request,\n  routeId: string,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  v7_relativeSplatPath: boolean\n) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      true,\n      location,\n      v7_relativeSplatPath\n    );\n    response.headers.set(\"Location\", location);\n  }\n\n  return response;\n}\n\nfunction normalizeRedirectLocation(\n  location: string,\n  currentUrl: URL,\n  basename: string\n): string {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\")\n      ? new URL(currentUrl.protocol + normalizedLocation)\n      : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  activeDeferreds: Map<string, DeferredData>,\n  skipLoaderErrorBubbling: boolean\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n  let pendingError =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : undefined;\n\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach((match) => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (\n          result.statusCode != null &&\n          result.statusCode !== 200 &&\n          !foundError\n        ) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: Record<string, DataResult>,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    results,\n    pendingActionResult,\n    activeDeferreds,\n    false // This method is only called client side so we always want to bubble\n  );\n\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach((rf) => {\n    let { key, match, controller } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\nfunction getActionDataForCommit(\n  pendingActionResult: PendingActionResult | undefined\n) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1])\n    ? {\n        // Clear out prior actionData on errors\n        actionData: {},\n      }\n    : {\n        actionData: {\n          [pendingActionResult[0]]: pendingActionResult[1].data,\n        },\n      };\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n    message,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\";\n    message?: string;\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: Record<string, DataResult>\n): { key: string; result: RedirectResult } | undefined {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return { key, result };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isPromise<T = unknown>(val: unknown): val is Promise<T> {\n  return typeof val === \"object\" && val != null && \"then\" in val;\n}\n\nfunction isDataStrategyResult(result: unknown): result is DataStrategyResult {\n  return (\n    result != null &&\n    typeof result === \"object\" &&\n    \"type\" in result &&\n    \"result\" in result &&\n    (result.type === ResultType.data || result.type === ResultType.error)\n  );\n}\n\nfunction isRedirectDataStrategyResultResult(result: DataStrategyResult) {\n  return (\n    isResponse(result.result) && redirectStatusCodes.has(result.result.status)\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDataWithResponseInit(\n  value: any\n): value is DataWithResponseInit<unknown> {\n  return (\n    typeof value === \"object\" &&\n    value != null &&\n    \"type\" in value &&\n    \"data\" in value &&\n    \"init\" in value &&\n    value.type === \"DataWithResponseInit\"\n  );\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveNavigationDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  signal: AbortSignal,\n  currentMatches: AgnosticDataRouteMatch[],\n  currentLoaderData: RouteData\n) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then((result) => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\n\nasync function resolveFetcherDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  revalidatingFetchers: RevalidatingFetcher[]\n) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, routeId, controller } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(\n        controller,\n        \"Expected an AbortController for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, controller.signal, true).then(\n        (result) => {\n          if (result) {\n            results[key] = result;\n          }\n        }\n      );\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n//#endregion\n"], "names": ["Action", "PopStateEventType", "invariant", "value", "message", "Error", "warning", "cond", "console", "warn", "e", "getHistoryState", "location", "index", "usr", "state", "key", "idx", "createLocation", "current", "to", "_extends", "pathname", "search", "hash", "parsePath", "Math", "random", "toString", "substr", "createPath", "_ref", "char<PERSON>t", "path", "parsed<PERSON><PERSON>", "hashIndex", "indexOf", "searchIndex", "getUrlBasedHistory", "getLocation", "createHref", "validateLocation", "options", "window", "document", "defaultView", "v5Compat", "globalHistory", "history", "action", "Pop", "listener", "getIndex", "handlePop", "nextIndex", "delta", "createURL", "base", "origin", "href", "replace", "URL", "replaceState", "listen", "fn", "addEventListener", "removeEventListener", "encodeLocation", "url", "push", "<PERSON><PERSON>", "historyState", "pushState", "error", "DOMException", "name", "assign", "Replace", "go", "n", "ResultType", "immutableRouteKeys", "Set", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "map", "route", "treePath", "String", "id", "join", "children", "isIndexRoute", "indexRoute", "pathOrLayoutRoute", "undefined", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "sort", "a", "b", "score", "length", "slice", "every", "i", "compareIndexes", "routesMeta", "meta", "childrenIndex", "rankRouteBranches", "matches", "decoded", "decodePath", "matchRouteBranch", "convertRouteMatchToUiMatch", "match", "loaderData", "params", "data", "handle", "parents<PERSON>eta", "flattenRoute", "relativePath", "caseSensitive", "startsWith", "joinPaths", "concat", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "paramRe", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "matchPath", "Object", "pathnameBase", "normalizePathname", "pattern", "matcher", "compiledParams", "regexpSource", "_", "paramName", "RegExp", "compilePath", "captureGroups", "memo", "splatValue", "v", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "pop", "resolvePathname", "normalizeSearch", "normalizeHash", "getInvalidPathError", "char", "field", "dest", "JSON", "stringify", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "from", "isEmptyPath", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "paths", "DataWithResponseInit", "constructor", "init", "this", "type", "Aborted<PERSON>eferredError", "DeferredData", "responseInit", "reject", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "entries", "acc", "_ref2", "trackPromise", "done", "add", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "k", "async", "resolve", "size", "unwrappedData", "_ref3", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "_tracked", "isTrackedPromise", "_error", "_data", "defer", "redirect", "status", "headers", "Headers", "set", "Response", "ErrorResponseImpl", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "json", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "hasErrorBou<PERSON>ry", "Boolean", "TRANSITIONS_STORAGE_KEY", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "throwStaticHandlerAbortedError", "request", "isRouteRequest", "future", "v7_throwAbortReason", "reason", "method", "normalizeTo", "prependBasename", "fromRouteId", "relative", "contextualMatches", "activeRouteMatch", "nakedIndex", "hasNakedIndexQuery", "URLSearchParams", "indexValues", "getAll", "append", "qs", "normalizeNavigateOptions", "normalizeFormMethod", "isFetcher", "opts", "body", "isSubmissionNavigation", "isValidMethod", "getInternalRouterError", "searchParams", "getInvalidBodyError", "rawFormMethod", "toUpperCase", "stripHashFromPath", "isMutationMethod", "FormData", "submission", "parse", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "getLoaderMatchesUntilBoundary", "boundaryId", "includeBoundary", "findIndex", "m", "getMatchesToLoad", "initialHydration", "skipActionErrorRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "deletedFetchers", "fetchLoadMatches", "fetchRedirectIds", "routesToUse", "pendingActionResult", "actionResult", "isErrorResult", "currentUrl", "nextUrl", "boundaryMatches", "errors", "keys", "actionStatus", "statusCode", "shouldSkipRevalidation", "navigationMatches", "lazy", "loader", "shouldLoadRouteOnHydration", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "revalidatingFetchers", "f", "routeId", "has", "fetcherMatches", "fetcher", "fetchers", "fetcherMatch", "getTargetMatch", "shouldRevalidate", "hasData", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "currentPath", "loaderMatch", "arg", "routeChoice", "patchRoutesImpl", "_childrenToPatch", "childrenToPatch", "newRoutes", "newRoute", "existingRoute", "isSameRoute", "<PERSON><PERSON><PERSON><PERSON>", "_existingRoute$childr", "b<PERSON><PERSON><PERSON>", "defaultDataStrategy", "_ref4", "matchesToLoad", "shouldLoad", "all", "callDataStrategyImpl", "dataStrategyImpl", "fetcher<PERSON>ey", "requestContext", "loadRouteDefinitionsPromises", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "isPropertyStaticallyDefined", "loadLazyRouteModule", "dsMatches", "loadRoutePromise", "handlerOverride", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "context", "handlerPromise", "handlerError", "callLoaderOrAction", "results", "convertDataStrategyResultToDataResult", "dataStrategyResult", "isResponse", "contentType", "_result$init3", "_result$init4", "_result$init", "_result$init2", "_result$init5", "_result$init6", "_result$init7", "_result$init8", "isDataWithResponseInit", "isDeferredData", "deferred", "deferredData", "normalizeRelativeRoutingRedirectResponse", "response", "trimmedMatches", "normalizeRedirectLocation", "normalizedLocation", "protocol", "isSameBasename", "createClientSideRequest", "Request", "processRouteLoaderData", "activeDeferreds", "skipL<PERSON>derError<PERSON><PERSON>bling", "found<PERSON><PERSON>r", "loaderHeaders", "pendingError", "isRedirectResult", "boundaryMatch", "findNearestBoundary", "isDeferredResult", "processLoaderData", "fetcherResults", "rf", "done<PERSON>etcher", "getDoneFetcher", "mergeLoaderData", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "getActionDataForCommit", "actionData", "reverse", "find", "getShortCircuitMatches", "_temp5", "errorMessage", "findRedirect", "isRedirectDataStrategyResultResult", "resolveData", "resolveNavigationDeferredResults", "currentMatches", "isRevalidatingLoader", "resolveDeferredData", "resolveFetcherDeferredResults", "unwrap", "getSubmissionFromNavigation", "navigation", "getLoadingNavigation", "getSubmittingNavigation", "getLoadingFetcher", "querySelector", "getAttribute", "initialEntries", "initialIndex", "entry", "createMemoryLocation", "clampIndex", "min", "max", "getCurrentLocation", "nextLocation", "splice", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "detectErrorBoundary", "inFlightDataRoutes", "initialized", "router", "dataRoutes", "dataStrategy", "patchRoutesOnNavigationImpl", "patchRoutesOnNavigation", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_partialHydration", "v7_prependBasename", "v7_skipActionErrorRevalidation", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialMatchesIsFOW", "initialErrors", "checkFogOfWar", "active", "fogOfWar", "pendingNavigationController", "unblockBlockerHistoryUpdate", "historyAction", "restoreScrollPosition", "preventScrollReset", "revalidation", "Map", "blockers", "pendingAction", "HistoryAction", "pendingPreventScrollReset", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "activeFetchers", "blockerFunctions", "updateState", "newState", "completedFetchers", "deletedFetchersKeys", "viewTransitionOpts", "flushSync", "deleteFetcher", "completeNavigation", "_temp", "_location$state", "_location$state2", "isActionReload", "_isRedirect", "priorPaths", "currentLocation", "toPaths", "getSavedScrollPosition", "startNavigation", "startUninterruptedRevalidation", "getScrollKey", "saveScrollPosition", "enableViewTransition", "loadingNavigation", "overrideNavigation", "isHashChangeOnly", "notFoundMatches", "handleNavigational404", "isFogOfWar", "interruptActiveLoads", "discoverResult", "discoverRoutes", "shortCircuited", "partialMatch<PERSON>", "actionMatch", "callDataStrategy", "startRedirectNavigation", "handleAction", "updatedMatches", "fetcherSubmission", "activeSubmission", "shouldUpdateNavigationState", "getUpdatedActionData", "cancelActiveDeferreds", "updatedFetchers", "markFetchRedirectsDone", "updates", "revalidatingFetcher", "getUpdatedRevalidatingFetchers", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "loaderResults", "callLoadersAndMaybeResolveData", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "handleLoaders", "isNavigation", "_temp2", "redirectLocation", "isDocumentReload", "redirectHistoryAction", "dataResults", "fetchersToLoad", "loaderResultsPromise", "fetcherResultsPromise", "updateFetcherState", "setFetcherError", "getFetcher", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "deleteBlocker", "updateBlocker", "newBlocker", "blocker", "shouldBlockNavigation", "blockerKey", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "y", "isNonHMR", "localManifest", "patch", "newMatches", "newPartialMatches", "initialize", "nextHistoryUpdatePromise", "_window", "transitions", "sessionPositions", "sessionStorage", "getItem", "restoreAppliedTransitions", "_saveAppliedTransitions", "setItem", "persistAppliedTransitions", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "navigate", "normalizedPath", "userReplace", "viewTransition", "fetch", "requestMatches", "detectAndHandle405Error", "existingFetcher", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "handleFetcherAction", "handleFetcherLoader", "revalidate", "count", "dispose", "clear", "get<PERSON><PERSON>er", "patchRoutes", "_internalFetchControllers", "_internalActiveDeferreds", "_internalSetRoutes", "queryImpl", "routeMatch", "Location", "actionHeaders", "loaderRequest", "loadRouteData", "submit", "isDataStrategyResult", "isRedirectResponse", "executedLoaders", "fromEntries", "query", "_temp3", "methodNotAllowedMatches", "queryRoute", "_temp4", "values", "_result$activeDeferre", "originalPath", "prefix", "p", "array", "keyMatch", "optional", "param", "_deepestRenderedBoundaryId", "redirectDocument"], "mappings": ";;;;;;;;;;udAOYA,IAAAA,WAAAA,GAAM,OAANA,EAAM,IAAA,MAANA,EAAM,KAAA,OAANA,EAAM,QAAA,UAANA,CAAM,EAAA,IA2LlB,MAAMC,EAAoB,WAySnB,SAASC,EAAUC,EAAYC,GACpC,IAAc,IAAVD,SAAmBA,EACrB,MAAM,IAAIE,MAAMD,EAEpB,CAEO,SAASE,EAAQC,EAAWH,GACjC,IAAKG,EAAM,CAEc,oBAAZC,SAAyBA,QAAQC,KAAKL,GAEjD,IAME,MAAM,IAAIC,MAAMD,EAEL,CAAX,MAAOM,GAAI,CACf,CACF,CASA,SAASC,EAAgBC,EAAoBC,GAC3C,MAAO,CACLC,IAAKF,EAASG,MACdC,IAAKJ,EAASI,IACdC,IAAKJ,EAET,CAKO,SAASK,EACdC,EACAC,EACAL,EACAC,GAcA,YAfU,IAAVD,IAAAA,EAAa,MAGmBM,EAAA,CAC9BC,SAA6B,iBAAZH,EAAuBA,EAAUA,EAAQG,SAC1DC,OAAQ,GACRC,KAAM,IACY,iBAAPJ,EAAkBK,EAAUL,GAAMA,EAAE,CAC/CL,QAKAC,IAAMI,GAAOA,EAAgBJ,KAAQA,GAjChCU,KAAKC,SAASC,SAAS,IAAIC,OAAO,EAAG,IAoC9C,CAKO,SAASC,EAAUC,GAIR,IAJST,SACzBA,EAAW,IAAGC,OACdA,EAAS,GAAEC,KACXA,EAAO,IACOO,EAKd,OAJIR,GAAqB,MAAXA,IACZD,GAAiC,MAArBC,EAAOS,OAAO,GAAaT,EAAS,IAAMA,GACpDC,GAAiB,MAATA,IACVF,GAA+B,MAAnBE,EAAKQ,OAAO,GAAaR,EAAO,IAAMA,GAC7CF,CACT,CAKO,SAASG,EAAUQ,GACxB,IAAIC,EAA4B,CAAA,EAEhC,GAAID,EAAM,CACR,IAAIE,EAAYF,EAAKG,QAAQ,KACzBD,GAAa,IACfD,EAAWV,KAAOS,EAAKJ,OAAOM,GAC9BF,EAAOA,EAAKJ,OAAO,EAAGM,IAGxB,IAAIE,EAAcJ,EAAKG,QAAQ,KAC3BC,GAAe,IACjBH,EAAWX,OAASU,EAAKJ,OAAOQ,GAChCJ,EAAOA,EAAKJ,OAAO,EAAGQ,IAGpBJ,IACFC,EAAWZ,SAAWW,EAE1B,CAEA,OAAOC,CACT,CASA,SAASI,EACPC,EACAC,EACAC,EACAC,QAA0B,IAA1BA,IAAAA,EAA6B,CAAA,GAE7B,IAAIC,OAAEA,EAASC,SAASC,YAAYC,SAAEA,GAAW,GAAUJ,EACvDK,EAAgBJ,EAAOK,QACvBC,EAASjD,EAAOkD,IAChBC,EAA4B,KAE5BtC,EAAQuC,IASZ,SAASA,IAEP,OADYL,EAAchC,OAAS,CAAEE,IAAK,OAC7BA,GACf,CAEA,SAASoC,IACPJ,EAASjD,EAAOkD,IAChB,IAAII,EAAYF,IACZG,EAAqB,MAAbD,EAAoB,KAAOA,EAAYzC,EACnDA,EAAQyC,EACJH,GACFA,EAAS,CAAEF,SAAQrC,SAAUoC,EAAQpC,SAAU2C,SAEnD,CA+CA,SAASC,EAAUpC,GAIjB,IAAIqC,EACyB,SAA3Bd,EAAO/B,SAAS8C,OACZf,EAAO/B,SAAS8C,OAChBf,EAAO/B,SAAS+C,KAElBA,EAAqB,iBAAPvC,EAAkBA,EAAKU,EAAWV,GASpD,OALAuC,EAAOA,EAAKC,QAAQ,KAAM,OAC1B1D,EACEuD,EACsEE,sEAAAA,GAEjE,IAAIE,IAAIF,EAAMF,EACvB,CApFa,MAAT5C,IACFA,EAAQ,EACRkC,EAAce,aAAYzC,EAAM0B,CAAAA,EAAAA,EAAchC,MAAK,CAAEE,IAAKJ,IAAS,KAoFrE,IAAImC,EAAmB,CACjBC,aACF,OAAOA,CACR,EACGrC,eACF,OAAO2B,EAAYI,EAAQI,EAC5B,EACDgB,OAAOC,GACL,GAAIb,EACF,MAAM,IAAI9C,MAAM,8CAKlB,OAHAsC,EAAOsB,iBAAiBhE,EAAmBoD,GAC3CF,EAAWa,EAEJ,KACLrB,EAAOuB,oBAAoBjE,EAAmBoD,GAC9CF,EAAW,IAAI,CAElB,EACDX,WAAWpB,GACFoB,EAAWG,EAAQvB,GAE5BoC,YACAW,eAAe/C,GAEb,IAAIgD,EAAMZ,EAAUpC,GACpB,MAAO,CACLE,SAAU8C,EAAI9C,SACdC,OAAQ6C,EAAI7C,OACZC,KAAM4C,EAAI5C,KAEb,EACD6C,KAlGF,SAAcjD,EAAQL,GACpBkC,EAASjD,EAAOsE,KAChB,IAAI1D,EAAWM,EAAe8B,EAAQpC,SAAUQ,EAAIL,GAChD0B,GAAkBA,EAAiB7B,EAAUQ,GAEjDP,EAAQuC,IAAa,EACrB,IAAImB,EAAe5D,EAAgBC,EAAUC,GACzCuD,EAAMpB,EAAQR,WAAW5B,GAG7B,IACEmC,EAAcyB,UAAUD,EAAc,GAAIH,EAY5C,CAXE,MAAOK,GAKP,GAAIA,aAAiBC,cAA+B,mBAAfD,EAAME,KACzC,MAAMF,EAIR9B,EAAO/B,SAASgE,OAAOR,EACzB,CAEItB,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAUoC,EAAQpC,SAAU2C,MAAO,GAE1D,EAuEEK,QArEF,SAAiBxC,EAAQL,GACvBkC,EAASjD,EAAO6E,QAChB,IAAIjE,EAAWM,EAAe8B,EAAQpC,SAAUQ,EAAIL,GAChD0B,GAAkBA,EAAiB7B,EAAUQ,GAEjDP,EAAQuC,IACR,IAAImB,EAAe5D,EAAgBC,EAAUC,GACzCuD,EAAMpB,EAAQR,WAAW5B,GAC7BmC,EAAce,aAAaS,EAAc,GAAIH,GAEzCtB,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAUoC,EAAQpC,SAAU2C,MAAO,GAE1D,EAyDEuB,GAAGC,GACMhC,EAAc+B,GAAGC,IAI5B,OAAO/B,CACT,CC7tBYgC,IAAAA,WAAAA,GAAU,OAAVA,EAAU,KAAA,OAAVA,EAAU,SAAA,WAAVA,EAAU,SAAA,WAAVA,EAAU,MAAA,QAAVA,CAAU,EAAA,CAAA,GAgSf,MAAMC,EAAqB,IAAIC,IAAuB,CAC3D,OACA,gBACA,OACA,KACA,QACA,aA6JK,SAASC,EACdC,EACAC,EACAC,EACAC,GAEA,YAHoB,IAApBD,IAAAA,EAAuB,SACA,IAAvBC,IAAAA,EAA0B,CAAA,GAEnBH,EAAOI,KAAI,CAACC,EAAO5E,KACxB,IAAI6E,EAAW,IAAIJ,EAAYK,OAAO9E,IAClC+E,EAAyB,iBAAbH,EAAMG,GAAkBH,EAAMG,GAAKF,EAASG,KAAK,KAWjE,GAVA3F,GACkB,IAAhBuF,EAAM5E,QAAmB4E,EAAMK,SAAQ,6CAGzC5F,GACGqF,EAASK,GACV,qCAAqCA,EAArC,qEAvBN,SACEH,GAEA,OAAuB,IAAhBA,EAAM5E,KACf,CAuBQkF,CAAaN,GAAQ,CACvB,IAAIO,EAAwC3E,EAAA,CAAA,EACvCoE,EACAJ,EAAmBI,GAAM,CAC5BG,OAGF,OADAL,EAASK,GAAMI,EACRA,CACT,CAAO,CACL,IAAIC,EAAkD5E,EAAA,CAAA,EACjDoE,EACAJ,EAAmBI,GAAM,CAC5BG,KACAE,cAAUI,IAaZ,OAXAX,EAASK,GAAMK,EAEXR,EAAMK,WACRG,EAAkBH,SAAWX,EAC3BM,EAAMK,SACNT,EACAK,EACAH,IAIGU,CACT,IAEJ,CAOO,SAASE,EAGdf,EACAgB,EACAC,GAEA,YAFQ,IAARA,IAAAA,EAAW,KAEJC,EAAgBlB,EAAQgB,EAAaC,GAAU,EACxD,CAEO,SAASC,EAGdlB,EACAgB,EACAC,EACAE,GAEA,IAGIjF,EAAWkF,GAFU,iBAAhBJ,EAA2B3E,EAAU2E,GAAeA,GAEvB9E,UAAY,IAAK+E,GAEvD,GAAgB,MAAZ/E,EACF,OAAO,KAGT,IAAImF,EAAWC,EAActB,IAmM/B,SAA2BqB,GACzBA,EAASE,MAAK,CAACC,EAAGC,IAChBD,EAAEE,QAAUD,EAAEC,MACVD,EAAEC,MAAQF,EAAEE,MAyCpB,SAAwBF,EAAaC,GAInC,OAFED,EAAEG,SAAWF,EAAEE,QAAUH,EAAEI,MAAM,GAAI,GAAGC,OAAM,CAAClC,EAAGmC,IAAMnC,IAAM8B,EAAEK,KAO9DN,EAAEA,EAAEG,OAAS,GAAKF,EAAEA,EAAEE,OAAS,GAG/B,CACN,CArDQI,CACEP,EAAEQ,WAAW5B,KAAK6B,GAASA,EAAKC,gBAChCT,EAAEO,WAAW5B,KAAK6B,GAASA,EAAKC,kBAG1C,CA3MEC,CAAkBd,GAElB,IAAIe,EAAU,KACd,IAAK,IAAIN,EAAI,EAAc,MAAXM,GAAmBN,EAAIT,EAASM,SAAUG,EAAG,CAO3D,IAAIO,EAAUC,EAAWpG,GACzBkG,EAAUG,EACRlB,EAASS,GACTO,EACAlB,EAEJ,CAEA,OAAOiB,CACT,CAUO,SAASI,EACdC,EACAC,GAEA,IAAIrC,MAAEA,EAAKnE,SAAEA,EAAQyG,OAAEA,GAAWF,EAClC,MAAO,CACLjC,GAAIH,EAAMG,GACVtE,WACAyG,SACAC,KAAMF,EAAWrC,EAAMG,IACvBqC,OAAQxC,EAAMwC,OAElB,CAmBA,SAASvB,EAGPtB,EACAqB,EACAyB,EACA5C,QAFwC,IAAxCmB,IAAAA,EAA2C,SACF,IAAzCyB,IAAAA,EAA4C,SAClC,IAAV5C,IAAAA,EAAa,IAEb,IAAI6C,EAAeA,CACjB1C,EACA5E,EACAuH,KAEA,IAAIf,EAAmC,CACrCe,kBACmBlC,IAAjBkC,EAA6B3C,EAAMxD,MAAQ,GAAKmG,EAClDC,eAAuC,IAAxB5C,EAAM4C,cACrBf,cAAezG,EACf4E,SAGE4B,EAAKe,aAAaE,WAAW,OAC/BpI,EACEmH,EAAKe,aAAaE,WAAWhD,GAC7B,wBAAwB+B,EAAKe,aAA7B,wBACM9C,EADN,4GAKF+B,EAAKe,aAAef,EAAKe,aAAapB,MAAM1B,EAAWyB,SAGzD,IAAI9E,EAAOsG,EAAU,CAACjD,EAAY+B,EAAKe,eACnChB,EAAac,EAAYM,OAAOnB,GAKhC5B,EAAMK,UAAYL,EAAMK,SAASiB,OAAS,IAC5C7G,GAGkB,IAAhBuF,EAAM5E,MACN,4FACuCoB,QAEzCyE,EAAcjB,EAAMK,SAAUW,EAAUW,EAAYnF,KAKpC,MAAdwD,EAAMxD,MAAiBwD,EAAM5E,QAIjC4F,EAASpC,KAAK,CACZpC,OACA6E,MAAO2B,EAAaxG,EAAMwD,EAAM5E,OAChCuG,cACA,EAaJ,OAXAhC,EAAOsD,SAAQ,CAACjD,EAAO5E,KAAU,IAAA8H,EAE/B,GAAmB,KAAflD,EAAMxD,aAAe0G,EAAClD,EAAMxD,OAAN0G,EAAYC,SAAS,KAG7C,IAAK,IAAIC,KAAYC,EAAwBrD,EAAMxD,MACjDkG,EAAa1C,EAAO5E,EAAOgI,QAH7BV,EAAa1C,EAAO5E,EAKtB,IAGK4F,CACT,CAgBA,SAASqC,EAAwB7G,GAC/B,IAAI8G,EAAW9G,EAAK+G,MAAM,KAC1B,GAAwB,IAApBD,EAAShC,OAAc,MAAO,GAElC,IAAKkC,KAAUC,GAAQH,EAGnBI,EAAaF,EAAMG,SAAS,KAE5BC,EAAWJ,EAAMrF,QAAQ,MAAO,IAEpC,GAAoB,IAAhBsF,EAAKnC,OAGP,OAAOoC,EAAa,CAACE,EAAU,IAAM,CAACA,GAGxC,IAAIC,EAAeR,EAAwBI,EAAKrD,KAAK,MAEjD0D,EAAmB,GAqBvB,OAZAA,EAAOlF,QACFiF,EAAa9D,KAAKgE,GACP,KAAZA,EAAiBH,EAAW,CAACA,EAAUG,GAAS3D,KAAK,QAKrDsD,GACFI,EAAOlF,QAAQiF,GAIVC,EAAO/D,KAAKqD,GACjB5G,EAAKqG,WAAW,MAAqB,KAAbO,EAAkB,IAAMA,GAEpD,CAaA,MAAMY,EAAU,YAMVC,EAAWC,GAAoB,MAANA,EAE/B,SAASlB,EAAaxG,EAAcpB,GAClC,IAAIkI,EAAW9G,EAAK+G,MAAM,KACtBY,EAAeb,EAAShC,OAS5B,OARIgC,EAASc,KAAKH,KAChBE,IAPiB,GAUf/I,IACF+I,GAdoB,GAiBfb,EACJe,QAAQH,IAAOD,EAAQC,KACvBI,QACC,CAACjD,EAAOkD,IACNlD,GACC2C,EAAQQ,KAAKD,GAvBM,EAyBJ,KAAZA,EAvBc,EACC,KAyBrBJ,EAEN,CAiBA,SAASjC,EAIPuC,EACA5I,EACAiF,QAAY,IAAZA,IAAAA,GAAe,GAEf,IAAIa,WAAEA,GAAe8C,EAEjBC,EAAgB,CAAA,EAChBC,EAAkB,IAClB5C,EAA2D,GAC/D,IAAK,IAAIN,EAAI,EAAGA,EAAIE,EAAWL,SAAUG,EAAG,CAC1C,IAAIG,EAAOD,EAAWF,GAClBmD,EAAMnD,IAAME,EAAWL,OAAS,EAChCuD,EACkB,MAApBF,EACI9I,EACAA,EAAS0F,MAAMoD,EAAgBrD,SAAW,IAC5Cc,EAAQ0C,EACV,CAAEtI,KAAMoF,EAAKe,aAAcC,cAAehB,EAAKgB,cAAegC,OAC9DC,GAGE7E,EAAQ4B,EAAK5B,MAkBjB,IAfGoC,GACDwC,GACA9D,IACCa,EAAWA,EAAWL,OAAS,GAAGtB,MAAM5E,QAEzCgH,EAAQ0C,EACN,CACEtI,KAAMoF,EAAKe,aACXC,cAAehB,EAAKgB,cACpBgC,KAAK,GAEPC,KAICzC,EACH,OAAO,KAGT2C,OAAO5F,OAAOuF,EAAetC,EAAME,QAEnCP,EAAQnD,KAAK,CAEX0D,OAAQoC,EACR7I,SAAUiH,EAAU,CAAC6B,EAAiBvC,EAAMvG,WAC5CmJ,aAAcC,EACZnC,EAAU,CAAC6B,EAAiBvC,EAAM4C,gBAEpChF,UAGyB,MAAvBoC,EAAM4C,eACRL,EAAkB7B,EAAU,CAAC6B,EAAiBvC,EAAM4C,eAExD,CAEA,OAAOjD,CACT,CAiHO,SAAS+C,EAIdI,EACArJ,GAEuB,iBAAZqJ,IACTA,EAAU,CAAE1I,KAAM0I,EAAStC,eAAe,EAAOgC,KAAK,IAGxD,IAAKO,EAASC,GA4ChB,SACE5I,EACAoG,EACAgC,QADa,IAAbhC,IAAAA,GAAgB,QACb,IAAHgC,IAAAA,GAAM,GAEN/J,EACW,MAAT2B,IAAiBA,EAAKmH,SAAS,MAAQnH,EAAKmH,SAAS,MACrD,eAAenH,EAAf,oCACMA,EAAK2B,QAAQ,MAAO,MAD1B,qIAGsC3B,EAAK2B,QAAQ,MAAO,YAG5D,IAAImE,EAA8B,GAC9B+C,EACF,IACA7I,EACG2B,QAAQ,UAAW,IACnBA,QAAQ,OAAQ,KAChBA,QAAQ,qBAAsB,QAC9BA,QACC,qBACA,CAACmH,EAAWC,EAAmB7B,KAC7BpB,EAAO1D,KAAK,CAAE2G,YAAW7B,WAA0B,MAAdA,IAC9BA,EAAa,eAAiB,gBAIzClH,EAAKmH,SAAS,MAChBrB,EAAO1D,KAAK,CAAE2G,UAAW,MACzBF,GACW,MAAT7I,GAAyB,OAATA,EACZ,QACA,qBACGoI,EAETS,GAAgB,QACE,KAAT7I,GAAwB,MAATA,IAQxB6I,GAAgB,iBAOlB,MAAO,CAFO,IAAIG,OAAOH,EAAczC,OAAgBnC,EAAY,KAElD6B,EACnB,CAjGkCmD,CAC9BP,EAAQ1I,KACR0I,EAAQtC,cACRsC,EAAQN,KAGNxC,EAAQvG,EAASuG,MAAM+C,GAC3B,IAAK/C,EAAO,OAAO,KAEnB,IAAIuC,EAAkBvC,EAAM,GACxB4C,EAAeL,EAAgBxG,QAAQ,UAAW,MAClDuH,EAAgBtD,EAAMb,MAAM,GAuBhC,MAAO,CACLe,OAvBmB8C,EAAed,QAClC,CAACqB,EAAIrJ,EAA6BlB,KAAU,IAArCmK,UAAEA,EAAS7B,WAAEA,GAAYpH,EAG9B,GAAkB,MAAdiJ,EAAmB,CACrB,IAAIK,EAAaF,EAActK,IAAU,GACzC4J,EAAeL,EACZpD,MAAM,EAAGoD,EAAgBrD,OAASsE,EAAWtE,QAC7CnD,QAAQ,UAAW,KACxB,CAEA,MAAMzD,EAAQgL,EAActK,GAM5B,OAJEuK,EAAKJ,GADH7B,IAAehJ,OACC+F,GAEC/F,GAAS,IAAIyD,QAAQ,OAAQ,KAE3CwH,CAAI,GAEb,CACF,GAIE9J,SAAU8I,EACVK,eACAE,UAEJ,CA2DO,SAASjD,EAAWvH,GACzB,IACE,OAAOA,EACJ6I,MAAM,KACNxD,KAAK8F,GAAMC,mBAAmBD,GAAG1H,QAAQ,MAAO,SAChDiC,KAAK,IAUV,CATE,MAAOpB,GAQP,OAPAnE,GACE,EACA,iBAAiBH,EAAjB,oHAEesE,EAAK,MAGftE,CACT,CACF,CAKO,SAASqG,EACdlF,EACA+E,GAEA,GAAiB,MAAbA,EAAkB,OAAO/E,EAE7B,IAAKA,EAASkK,cAAclD,WAAWjC,EAASmF,eAC9C,OAAO,KAKT,IAAIC,EAAapF,EAAS+C,SAAS,KAC/B/C,EAASU,OAAS,EAClBV,EAASU,OACT2E,EAAWpK,EAASU,OAAOyJ,GAC/B,OAAIC,GAAyB,MAAbA,EAEP,KAGFpK,EAAS0F,MAAMyE,IAAe,GACvC,CAOO,SAASE,EAAYvK,EAAQwK,QAAY,IAAZA,IAAAA,EAAe,KACjD,IACEtK,SAAUuK,EAAUtK,OACpBA,EAAS,GAAEC,KACXA,EAAO,IACS,iBAAPJ,EAAkBK,EAAUL,GAAMA,EAEzCE,EAAWuK,EACXA,EAAWvD,WAAW,KACpBuD,EAWR,SAAyBzD,EAAsBwD,GAC7C,IAAI7C,EAAW6C,EAAahI,QAAQ,OAAQ,IAAIoF,MAAM,KAYtD,OAXuBZ,EAAaY,MAAM,KAEzBN,SAASsB,IACR,OAAZA,EAEEjB,EAAShC,OAAS,GAAGgC,EAAS+C,MACb,MAAZ9B,GACTjB,EAAS1E,KAAK2F,EAChB,IAGKjB,EAAShC,OAAS,EAAIgC,EAASlD,KAAK,KAAO,GACpD,CAxBQkG,CAAgBF,EAAYD,GAC9BA,EAEJ,MAAO,CACLtK,WACAC,OAAQyK,EAAgBzK,GACxBC,KAAMyK,EAAczK,GAExB,CAkBA,SAAS0K,EACPC,EACAC,EACAC,EACApK,GAEA,MACE,qBAAqBkK,EAArB,2CACQC,cAAkBE,KAAKC,UAC7BtK,GAFF,yCAIQoK,EAJR,2HAOJ,CAyBO,SAASG,EAEdhF,GACA,OAAOA,EAAQsC,QACb,CAACjC,EAAOhH,IACI,IAAVA,GAAgBgH,EAAMpC,MAAMxD,MAAQ4F,EAAMpC,MAAMxD,KAAK8E,OAAS,GAEpE,CAIO,SAAS0F,EAEdjF,EAAckF,GACd,IAAIC,EAAcH,EAA2BhF,GAK7C,OAAIkF,EACKC,EAAYnH,KAAI,CAACqC,EAAO5G,IAC7BA,IAAQ0L,EAAY5F,OAAS,EAAIc,EAAMvG,SAAWuG,EAAM4C,eAIrDkC,EAAYnH,KAAKqC,GAAUA,EAAM4C,cAC1C,CAKO,SAASmC,EACdC,EACAC,EACAC,EACAC,GAEA,IAAI5L,OAFU,IAAd4L,IAAAA,GAAiB,GAGI,iBAAVH,EACTzL,EAAKK,EAAUoL,IAEfzL,EAAEC,EAAQwL,GAAAA,GAEV3M,GACGkB,EAAGE,WAAaF,EAAGE,SAASsH,SAAS,KACtCsD,EAAoB,IAAK,WAAY,SAAU9K,IAEjDlB,GACGkB,EAAGE,WAAaF,EAAGE,SAASsH,SAAS,KACtCsD,EAAoB,IAAK,WAAY,OAAQ9K,IAE/ClB,GACGkB,EAAGG,SAAWH,EAAGG,OAAOqH,SAAS,KAClCsD,EAAoB,IAAK,SAAU,OAAQ9K,KAI/C,IAGI6L,EAHAC,EAAwB,KAAVL,GAAgC,KAAhBzL,EAAGE,SACjCuK,EAAaqB,EAAc,IAAM9L,EAAGE,SAaxC,GAAkB,MAAduK,EACFoB,EAAOF,MACF,CACL,IAAII,EAAqBL,EAAe/F,OAAS,EAMjD,IAAKiG,GAAkBnB,EAAWvD,WAAW,MAAO,CAClD,IAAI8E,EAAavB,EAAW7C,MAAM,KAElC,KAAyB,OAAlBoE,EAAW,IAChBA,EAAWC,QACXF,GAAsB,EAGxB/L,EAAGE,SAAW8L,EAAWvH,KAAK,IAChC,CAEAoH,EAAOE,GAAsB,EAAIL,EAAeK,GAAsB,GACxE,CAEA,IAAIlL,EAAO0J,EAAYvK,EAAI6L,GAGvBK,EACFzB,GAA6B,MAAfA,GAAsBA,EAAWzC,SAAS,KAEtDmE,GACDL,GAA8B,MAAfrB,IAAuBkB,EAAiB3D,SAAS,KAQnE,OANGnH,EAAKX,SAAS8H,SAAS,OACvBkE,IAA4BC,IAE7BtL,EAAKX,UAAY,KAGZW,CACT,OAiBasG,EAAaiF,GACxBA,EAAM3H,KAAK,KAAKjC,QAAQ,SAAU,KAKvB8G,EAAqBpJ,GAChCA,EAASsC,QAAQ,OAAQ,IAAIA,QAAQ,OAAQ,KAKlCoI,EAAmBzK,GAC7BA,GAAqB,MAAXA,EAEPA,EAAO+G,WAAW,KAClB/G,EACA,IAAMA,EAHN,GAQO0K,EAAiBzK,GAC3BA,GAAiB,MAATA,EAAoBA,EAAK8G,WAAW,KAAO9G,EAAO,IAAMA,EAAzC,GA4BnB,MAAMiM,EAKXC,YAAY1F,EAAS2F,GAAqBC,KAJ1CC,KAAe,uBAKbD,KAAK5F,KAAOA,EACZ4F,KAAKD,KAAOA,GAAQ,IACtB,EAoBK,MAAMG,UAA6BzN,OAEnC,MAAM0N,EAWXL,YAAY1F,EAA+BgG,GAQzC,IAAIC,EARkEL,KAVhEM,eAA8B,IAAIhJ,IAAa0I,KAI/CO,YACN,IAAIjJ,IAAK0I,KAGXQ,aAAyB,GAGvBlO,EACE8H,GAAwB,iBAATA,IAAsBqG,MAAMC,QAAQtG,GACnD,sCAMF4F,KAAKW,aAAe,IAAIC,SAAQ,CAACzD,EAAG0D,IAAOR,EAASQ,IACpDb,KAAKc,WAAa,IAAIC,gBACtB,IAAIC,EAAUA,IACZX,EAAO,IAAIH,EAAqB,0BAClCF,KAAKiB,oBAAsB,IACzBjB,KAAKc,WAAWI,OAAO5K,oBAAoB,QAAS0K,GACtDhB,KAAKc,WAAWI,OAAO7K,iBAAiB,QAAS2K,GAEjDhB,KAAK5F,KAAOwC,OAAOuE,QAAQ/G,GAAM+B,QAC/B,CAACiF,EAAGC,KAAA,IAAGjO,EAAKb,GAAM8O,EAAA,OAChBzE,OAAO5F,OAAOoK,EAAK,CACjBhO,CAACA,GAAM4M,KAAKsB,aAAalO,EAAKb,IAC9B,GACJ,CACF,GAEIyN,KAAKuB,MAEPvB,KAAKiB,sBAGPjB,KAAKD,KAAOK,CACd,CAEQkB,aACNlO,EACAb,GAEA,KAAMA,aAAiBqO,SACrB,OAAOrO,EAGTyN,KAAKQ,aAAa/J,KAAKrD,GACvB4M,KAAKM,eAAekB,IAAIpO,GAIxB,IAAIqO,EAA0Bb,QAAQc,KAAK,CAACnP,EAAOyN,KAAKW,eAAegB,MACpEvH,GAAS4F,KAAK4B,SAASH,EAASrO,OAAKkF,EAAW8B,KAChDvD,GAAUmJ,KAAK4B,SAASH,EAASrO,EAAKyD,KAQzC,OAHA4K,EAAQI,OAAM,SAEdjF,OAAOkF,eAAeL,EAAS,WAAY,CAAEM,IAAKA,KAAM,IACjDN,CACT,CAEQG,SACNH,EACArO,EACAyD,EACAuD,GAEA,GACE4F,KAAKc,WAAWI,OAAOc,SACvBnL,aAAiBqJ,EAIjB,OAFAF,KAAKiB,sBACLrE,OAAOkF,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMlL,IAC/C+J,QAAQP,OAAOxJ,GAYxB,GATAmJ,KAAKM,eAAe2B,OAAO7O,GAEvB4M,KAAKuB,MAEPvB,KAAKiB,2BAKO3I,IAAVzB,QAAgCyB,IAAT8B,EAAoB,CAC7C,IAAI8H,EAAiB,IAAIzP,MACvB,0BAA0BW,EAA1B,yFAKF,OAFAwJ,OAAOkF,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMG,IACtDlC,KAAKmC,MAAK,EAAO/O,GACVwN,QAAQP,OAAO6B,EACxB,CAEA,YAAa5J,IAAT8B,GACFwC,OAAOkF,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMlL,IACtDmJ,KAAKmC,MAAK,EAAO/O,GACVwN,QAAQP,OAAOxJ,KAGxB+F,OAAOkF,eAAeL,EAAS,QAAS,CAAEM,IAAKA,IAAM3H,IACrD4F,KAAKmC,MAAK,EAAO/O,GACVgH,EACT,CAEQ+H,KAAKH,EAAkBI,GAC7BpC,KAAKO,YAAYzF,SAASuH,GAAeA,EAAWL,EAASI,IAC/D,CAEAE,UAAUlM,GAER,OADA4J,KAAKO,YAAYiB,IAAIpL,GACd,IAAM4J,KAAKO,YAAY0B,OAAO7L,EACvC,CAEAmM,SACEvC,KAAKc,WAAW0B,QAChBxC,KAAKM,eAAexF,SAAQ,CAAC4C,EAAG+E,IAAMzC,KAAKM,eAAe2B,OAAOQ,KACjEzC,KAAKmC,MAAK,EACZ,CAEAO,kBAAkBxB,GAChB,IAAIc,GAAU,EACd,IAAKhC,KAAKuB,KAAM,CACd,IAAIP,EAAUA,IAAMhB,KAAKuC,SACzBrB,EAAO7K,iBAAiB,QAAS2K,GACjCgB,QAAgB,IAAIpB,SAAS+B,IAC3B3C,KAAKsC,WAAWN,IACdd,EAAO5K,oBAAoB,QAAS0K,IAChCgB,GAAWhC,KAAKuB,OAClBoB,EAAQX,EACV,GACA,GAEN,CACA,OAAOA,CACT,CAEIT,WACF,OAAoC,IAA7BvB,KAAKM,eAAesC,IAC7B,CAEIC,oBAMF,OALAvQ,EACgB,OAAd0N,KAAK5F,MAAiB4F,KAAKuB,KAC3B,6DAGK3E,OAAOuE,QAAQnB,KAAK5F,MAAM+B,QAC/B,CAACiF,EAAG0B,KAAA,IAAG1P,EAAKb,GAAMuQ,EAAA,OAChBlG,OAAO5F,OAAOoK,EAAK,CACjBhO,CAACA,GAAM2P,EAAqBxQ,IAC5B,GACJ,CACF,EACF,CAEIyQ,kBACF,OAAOvC,MAAMpB,KAAKW,KAAKM,eACzB,EASF,SAASyC,EAAqBxQ,GAC5B,IAPF,SAA0BA,GACxB,OACEA,aAAiBqO,UAAkD,IAAtCrO,EAAyB0Q,QAE1D,CAGOC,CAAiB3Q,GACpB,OAAOA,EAGT,GAAIA,EAAM4Q,OACR,MAAM5Q,EAAM4Q,OAEd,OAAO5Q,EAAM6Q,KACf,CAWaC,MAeAC,EAA6B,SAAC9M,EAAKuJ,QAAI,IAAJA,IAAAA,EAAO,KACrD,IAAIK,EAAeL,EACS,iBAAjBK,EACTA,EAAe,CAAEmD,OAAQnD,QACe,IAAxBA,EAAamD,SAC7BnD,EAAamD,OAAS,KAGxB,IAAIC,EAAU,IAAIC,QAAQrD,EAAaoD,SAGvC,OAFAA,EAAQE,IAAI,WAAYlN,GAEjB,IAAImN,SAAS,KAAIlQ,KACnB2M,EAAY,CACfoD,YAEJ,EAuCO,MAAMI,EAOX9D,YACEyD,EACAM,EACAzJ,EACA0J,QAAQ,IAARA,IAAAA,GAAW,GAEX9D,KAAKuD,OAASA,EACdvD,KAAK6D,WAAaA,GAAc,GAChC7D,KAAK8D,SAAWA,EACZ1J,aAAgB3H,OAClBuN,KAAK5F,KAAOA,EAAKpG,WACjBgM,KAAKnJ,MAAQuD,GAEb4F,KAAK5F,KAAOA,CAEhB,EAOK,SAAS2J,EAAqBlN,GACnC,OACW,MAATA,GACwB,iBAAjBA,EAAM0M,QACe,iBAArB1M,EAAMgN,YACa,kBAAnBhN,EAAMiN,UACb,SAAUjN,CAEd,CCpgCA,MAAMmN,EAAgD,CACpD,OACA,MACA,QACA,UAEIC,EAAuB,IAAI3M,IAC/B0M,GAGIE,EAAuC,CAC3C,SACGF,GAECG,EAAsB,IAAI7M,IAAgB4M,GAE1CE,EAAsB,IAAI9M,IAAI,CAAC,IAAK,IAAK,IAAK,IAAK,MACnD+M,EAAoC,IAAI/M,IAAI,CAAC,IAAK,MAE3CgN,EAA4C,CACvDnR,MAAO,OACPH,cAAUsF,EACViM,gBAAYjM,EACZkM,gBAAYlM,EACZmM,iBAAanM,EACboM,cAAUpM,EACVqM,UAAMrM,EACNsM,UAAMtM,GAGKuM,EAAsC,CACjD1R,MAAO,OACPiH,UAAM9B,EACNiM,gBAAYjM,EACZkM,gBAAYlM,EACZmM,iBAAanM,EACboM,cAAUpM,EACVqM,UAAMrM,EACNsM,UAAMtM,GAGKwM,EAAiC,CAC5C3R,MAAO,YACP4R,aAASzM,EACT0M,WAAO1M,EACPtF,cAAUsF,GAGN2M,EAAqB,gCAErBC,EAAyDrN,IAAW,CACxEsN,iBAAkBC,QAAQvN,EAAMsN,oBAG5BE,EAA0B,iCAsoFnBC,GAAyBC,OAAO,YAspB7C,SAASC,GACPC,EACAC,EACAC,GAEA,GAAIA,EAAOC,0BAAiDtN,IAA1BmN,EAAQvE,OAAO2E,OAC/C,MAAMJ,EAAQvE,OAAO2E,OAIvB,MAAM,IAAIpT,OADGiT,EAAiB,aAAe,SACAD,oBAAAA,EAAQK,OAAUL,IAAAA,EAAQjP,IACzE,CAYA,SAASuP,GACP/S,EACA4G,EACAnB,EACAuN,EACAxS,EACAsL,EACAmH,EACAC,GAEA,IAAIC,EACAC,EACJ,GAAIH,EAAa,CAGfE,EAAoB,GACpB,IAAK,IAAIlM,KAASL,EAEhB,GADAuM,EAAkB1P,KAAKwD,GACnBA,EAAMpC,MAAMG,KAAOiO,EAAa,CAClCG,EAAmBnM,EACnB,KACF,CAEJ,MACEkM,EAAoBvM,EACpBwM,EAAmBxM,EAAQA,EAAQT,OAAS,GAI9C,IAAI9E,EAAO2K,EACTxL,GAAU,IACVqL,EAAoBsH,EAAmBrH,GACvClG,EAAc5F,EAASU,SAAU+E,IAAazF,EAASU,SAC1C,SAAbwS,GAYF,GANU,MAAN1S,IACFa,EAAKV,OAASX,EAASW,OACvBU,EAAKT,KAAOZ,EAASY,OAIZ,MAANJ,GAAqB,KAAPA,GAAoB,MAAPA,IAAe4S,EAAkB,CAC/D,IAAIC,EAAaC,GAAmBjS,EAAKV,QACzC,GAAIyS,EAAiBvO,MAAM5E,QAAUoT,EAEnChS,EAAKV,OAASU,EAAKV,OACfU,EAAKV,OAAOqC,QAAQ,MAAO,WAC3B,cACC,IAAKoQ,EAAiBvO,MAAM5E,OAASoT,EAAY,CAEtD,IAAIlM,EAAS,IAAIoM,gBAAgBlS,EAAKV,QAClC6S,EAAcrM,EAAOsM,OAAO,SAChCtM,EAAO8H,OAAO,SACduE,EAAYtK,QAAQwB,GAAMA,IAAG5C,SAAS4C,GAAMvD,EAAOuM,OAAO,QAAShJ,KACnE,IAAIiJ,EAAKxM,EAAOnG,WAChBK,EAAKV,OAASgT,EAASA,IAAAA,EAAO,EAChC,CACF,CAWA,OALIX,GAAgC,MAAbvN,IACrBpE,EAAKX,SACe,MAAlBW,EAAKX,SAAmB+E,EAAWkC,EAAU,CAAClC,EAAUpE,EAAKX,YAG1DQ,EAAWG,EACpB,CAIA,SAASuS,GACPC,EACAC,EACAzS,EACA0S,GAOA,IAAKA,IAlGP,SACEA,GAEA,OACU,MAARA,IACE,aAAcA,GAAyB,MAAjBA,EAAKrC,UAC1B,SAAUqC,QAAsBzO,IAAdyO,EAAKC,KAE9B,CA0FgBC,CAAuBF,GACnC,MAAO,CAAE1S,QAGX,GAAI0S,EAAKxC,aAAe2C,GAAcH,EAAKxC,YACzC,MAAO,CACLlQ,OACAwC,MAAOsQ,GAAuB,IAAK,CAAErB,OAAQiB,EAAKxC,cAItD,IA0EI6C,EACA1C,EA3EA2C,EAAsBA,KAAO,CAC/BhT,OACAwC,MAAOsQ,GAAuB,IAAK,CAAElH,KAAM,mBAIzCqH,EAAgBP,EAAKxC,YAAc,MACnCA,EAAasC,EACZS,EAAcC,cACdD,EAAc1J,cACf4G,EAAagD,GAAkBnT,GAEnC,QAAkBiE,IAAdyO,EAAKC,KAAoB,CAC3B,GAAyB,eAArBD,EAAKtC,YAA8B,CAErC,IAAKgD,GAAiBlD,GACpB,OAAO8C,IAGT,IAAIzC,EACmB,iBAAdmC,EAAKC,KACRD,EAAKC,KACLD,EAAKC,gBAAgBU,UACrBX,EAAKC,gBAAgBT,gBAErB9F,MAAMpB,KAAK0H,EAAKC,KAAK7F,WAAWhF,QAC9B,CAACiF,EAAG0B,KAAA,IAAG/L,EAAMxE,GAAMuQ,EAAA,MAAA,GAAQ1B,EAAMrK,EAAI,IAAIxE,EAAK,IAAA,GAC9C,IAEFwF,OAAOgP,EAAKC,MAElB,MAAO,CACL3S,OACAsT,WAAY,CACVpD,aACAC,aACAC,YAAasC,EAAKtC,YAClBC,cAAUpM,EACVqM,UAAMrM,EACNsM,QAGN,CAAO,GAAyB,qBAArBmC,EAAKtC,YAAoC,CAElD,IAAKgD,GAAiBlD,GACpB,OAAO8C,IAGT,IACE,IAAI1C,EACmB,iBAAdoC,EAAKC,KAAoBtI,KAAKkJ,MAAMb,EAAKC,MAAQD,EAAKC,KAE/D,MAAO,CACL3S,OACAsT,WAAY,CACVpD,aACAC,aACAC,YAAasC,EAAKtC,YAClBC,cAAUpM,EACVqM,OACAC,UAAMtM,GAKZ,CAFE,MAAOxF,GACP,OAAOuU,GACT,CACF,CACF,CAUA,GARA/U,EACsB,mBAAboV,SACP,iDAMEX,EAAKrC,SACP0C,EAAeS,GAA8Bd,EAAKrC,UAClDA,EAAWqC,EAAKrC,cACX,GAAIqC,EAAKC,gBAAgBU,SAC9BN,EAAeS,GAA8Bd,EAAKC,MAClDtC,EAAWqC,EAAKC,UACX,GAAID,EAAKC,gBAAgBT,gBAC9Ba,EAAeL,EAAKC,KACpBtC,EAAWoD,GAA8BV,QACpC,GAAiB,MAAbL,EAAKC,KACdI,EAAe,IAAIb,gBACnB7B,EAAW,IAAIgD,cAEf,IACEN,EAAe,IAAIb,gBAAgBQ,EAAKC,MACxCtC,EAAWoD,GAA8BV,EAG3C,CAFE,MAAOtU,GACP,OAAOuU,GACT,CAGF,IAAIM,EAAyB,CAC3BpD,aACAC,aACAC,YACGsC,GAAQA,EAAKtC,aAAgB,oCAChCC,WACAC,UAAMrM,EACNsM,UAAMtM,GAGR,GAAImP,GAAiBE,EAAWpD,YAC9B,MAAO,CAAElQ,OAAMsT,cAIjB,IAAIrT,EAAaT,EAAUQ,GAS3B,OALIyS,GAAaxS,EAAWX,QAAU2S,GAAmBhS,EAAWX,SAClEyT,EAAaV,OAAO,QAAS,IAE/BpS,EAAWX,OAAM,IAAOyT,EAEjB,CAAE/S,KAAMH,EAAWI,GAAaqT,aACzC,CAIA,SAASI,GACPnO,EACAoO,EACAC,QAAe,IAAfA,IAAAA,GAAkB,GAElB,IAAIhV,EAAQ2G,EAAQsO,WAAWC,GAAMA,EAAEtQ,MAAMG,KAAOgQ,IACpD,OAAI/U,GAAS,EACJ2G,EAAQR,MAAM,EAAG6O,EAAkBhV,EAAQ,EAAIA,GAEjD2G,CACT,CAEA,SAASwO,GACPhT,EACAjC,EACAyG,EACA+N,EACA3U,EACAqV,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACApQ,EACAqQ,GAEA,IAAIC,EAAeD,EACfE,GAAcF,EAAoB,IAChCA,EAAoB,GAAGjS,MACvBiS,EAAoB,GAAG1O,UACzB9B,EACA2Q,EAAa7T,EAAQQ,UAAUzC,EAAMH,UACrCkW,EAAU9T,EAAQQ,UAAU5C,GAG5BmW,EAAkBvP,EAClByO,GAAoBlV,EAAMiW,OAM5BD,EAAkBpB,GAChBnO,EACAgD,OAAOyM,KAAKlW,EAAMiW,QAAQ,IAC1B,GAEON,GAAuBE,GAAcF,EAAoB,MAGlEK,EAAkBpB,GAChBnO,EACAkP,EAAoB,KAOxB,IAAIQ,EAAeR,EACfA,EAAoB,GAAGS,gBACvBjR,EACAkR,EACFlB,GAA+BgB,GAAgBA,GAAgB,IAE7DG,EAAoBN,EAAgBjN,QAAO,CAACjC,EAAOhH,KACrD,IAAI4E,MAAEA,GAAUoC,EAChB,GAAIpC,EAAM6R,KAER,OAAO,EAGT,GAAoB,MAAhB7R,EAAM8R,OACR,OAAO,EAGT,GAAItB,EACF,OAAOuB,GAA2B/R,EAAO1E,EAAM+G,WAAY/G,EAAMiW,QAInE,GA2JJ,SACES,EACAC,EACA7P,GAEA,IAAI8P,GAEDD,GAED7P,EAAMpC,MAAMG,KAAO8R,EAAajS,MAAMG,GAIpCgS,OAAsD1R,IAAtCuR,EAAkB5P,EAAMpC,MAAMG,IAGlD,OAAO+R,GAASC,CAClB,CA3KMC,CAAY9W,EAAM+G,WAAY/G,EAAMyG,QAAQ3G,GAAQgH,IACpDuO,EAAwBvM,MAAMjE,GAAOA,IAAOiC,EAAMpC,MAAMG,KAExD,OAAO,EAOT,IAAIkS,EAAoB/W,EAAMyG,QAAQ3G,GAClCkX,EAAiBlQ,EAErB,OAAOmQ,GAAuBnQ,EAAKxG,EAAA,CACjCwV,aACAoB,cAAeH,EAAkB/P,OACjC+O,UACAoB,WAAYH,EAAehQ,QACxBwN,EAAU,CACboB,eACAO,eACAiB,yBAAyBf,IAGrBjB,GACAU,EAAWvV,SAAWuV,EAAWtV,SAC/BuV,EAAQxV,SAAWwV,EAAQvV,QAE7BsV,EAAWtV,SAAWuV,EAAQvV,QAC9B6W,GAAmBN,EAAmBC,MAC1C,IAIAM,EAA8C,GAqFlD,OApFA9B,EAAiB7N,SAAQ,CAAC4P,EAAGtX,KAM3B,GACEiV,IACCzO,EAAQqC,MAAMkM,GAAMA,EAAEtQ,MAAMG,KAAO0S,EAAEC,WACtCjC,EAAgBkC,IAAIxX,GAEpB,OAGF,IAAIyX,EAAiBtS,EAAYsQ,EAAa6B,EAAErW,KAAMoE,GAMtD,IAAKoS,EASH,YARAJ,EAAqBhU,KAAK,CACxBrD,MACAuX,QAASD,EAAEC,QACXtW,KAAMqW,EAAErW,KACRuF,QAAS,KACTK,MAAO,KACP6G,WAAY,OAQhB,IAAIgK,EAAU3X,EAAM4X,SAAShJ,IAAI3O,GAC7B4X,EAAeC,GAAeJ,EAAgBH,EAAErW,MAEhD6W,GAAmB,EACnBtC,EAAiBgC,IAAIxX,GAEvB8X,GAAmB,EACVzC,EAAsBmC,IAAIxX,IAEnCqV,EAAsBxG,OAAO7O,GAC7B8X,GAAmB,GASnBA,EAPAJ,GACkB,SAAlBA,EAAQ3X,YACSmF,IAAjBwS,EAAQ1Q,KAKWmO,EAIA6B,GAAuBY,EAAYvX,EAAA,CACpDwV,aACAoB,cAAelX,EAAMyG,QAAQzG,EAAMyG,QAAQT,OAAS,GAAGgB,OACvD+O,UACAoB,WAAY1Q,EAAQA,EAAQT,OAAS,GAAGgB,QACrCwN,EAAU,CACboB,eACAO,eACAiB,yBAAyBf,GAErBjB,KAIJ2C,GACFT,EAAqBhU,KAAK,CACxBrD,MACAuX,QAASD,EAAEC,QACXtW,KAAMqW,EAAErW,KACRuF,QAASiR,EACT5Q,MAAO+Q,EACPlK,WAAY,IAAIC,iBAEpB,IAGK,CAAC0I,EAAmBgB,EAC7B,CAEA,SAASb,GACP/R,EACAqC,EACAkP,GAGA,GAAIvR,EAAM6R,KACR,OAAO,EAIT,IAAK7R,EAAM8R,OACT,OAAO,EAGT,IAAIwB,EAAwB,MAAdjR,QAA+C5B,IAAzB4B,EAAWrC,EAAMG,IACjDoT,EAAqB,MAAVhC,QAAuC9Q,IAArB8Q,EAAOvR,EAAMG,IAG9C,SAAKmT,GAAWC,KAKY,mBAAjBvT,EAAM8R,SAAkD,IAAzB9R,EAAM8R,OAAO0B,UAK/CF,IAAYC,EACtB,CAqBA,SAASZ,GACPV,EACA7P,GAEA,IAAIqR,EAAcxB,EAAajS,MAAMxD,KACrC,OAEEyV,EAAapW,WAAauG,EAAMvG,UAGhB,MAAf4X,GACCA,EAAY9P,SAAS,MACrBsO,EAAa3P,OAAO,OAASF,EAAME,OAAO,IAEhD,CAEA,SAASiQ,GACPmB,EACAC,GAEA,GAAID,EAAY1T,MAAMqT,iBAAkB,CACtC,IAAIO,EAAcF,EAAY1T,MAAMqT,iBAAiBM,GACrD,GAA2B,kBAAhBC,EACT,OAAOA,CAEX,CAEA,OAAOD,EAAIjB,uBACb,CAEA,SAASmB,GACPf,EACAzS,EACA2Q,EACAlR,EACAF,GACA,IAAAkU,EACA,IAAIC,EACJ,GAAIjB,EAAS,CACX,IAAI9S,EAAQF,EAASgT,GACrBrY,EACEuF,EACoD8S,oDAAAA,GAEjD9S,EAAMK,WACTL,EAAMK,SAAW,IAEnB0T,EAAkB/T,EAAMK,QAC1B,MACE0T,EAAkB/C,EAMpB,IAOIgD,EAAYtU,EAPKW,EAASgE,QAC3B4P,IACEF,EAAgB3P,MAAM8P,GACrBC,GAAYF,EAAUC,OAM1BtU,EACA,CAACkT,GAAW,IAAK,QAAS5S,eAAO4T,EAAAC,UAAAD,EAAiBxS,SAAU,MAC5DxB,GAGFiU,EAAgBnV,QAAQoV,EAC1B,CAEA,SAASG,GACPF,EACAC,GAGA,MACE,OAAQD,GACR,OAAQC,GACRD,EAAS9T,KAAO+T,EAAc/T,IAQ5B8T,EAAS7Y,QAAU8Y,EAAc9Y,OACjC6Y,EAASzX,OAAS0X,EAAc1X,MAChCyX,EAASrR,gBAAkBsR,EAActR,kBASzCqR,EAAS5T,UAAyC,IAA7B4T,EAAS5T,SAASiB,QACvC4S,EAAc7T,UAA8C,IAAlC6T,EAAc7T,SAASiB,SAO9C2S,EAAS5T,SAAUmB,OAAM,CAAC4S,EAAQ3S,KAAC,IAAA4S,EAAA,OAClB,OADkBA,EACxCH,EAAc7T,eAAQ,EAAtBgU,EAAwBjQ,MAAMkQ,GAAWH,GAAYC,EAAQE,IAAQ,IAEzE,CAiFAzJ,eAAe0J,GAAmBC,GAE6B,IAF5BzS,QACjCA,GACyByS,EACrBC,EAAgB1S,EAAQsC,QAAQiM,GAAMA,EAAEoE,aAE5C,aADoB3L,QAAQ4L,IAAIF,EAAc1U,KAAKuQ,GAAMA,EAAExF,cAC5CxG,QACb,CAACiF,EAAKzF,EAAQrC,IACZsD,OAAO5F,OAAOoK,EAAK,CAAE,CAACkL,EAAchT,GAAGzB,MAAMG,IAAK2D,KACpD,CACF,EACF,CAEA+G,eAAe+J,GACbC,EACAzM,EACA9M,EACAsS,EACA6G,EACA1S,EACA+S,EACAhV,EACAF,EACAmV,GAEA,IAAIC,EAA+BjT,EAAQhC,KAAKuQ,GAC9CA,EAAEtQ,MAAM6R,KAnGZhH,eACE7K,EACAJ,EACAE,GAEA,IAAKE,EAAM6R,KACT,OAGF,IAAIoD,QAAkBjV,EAAM6R,OAK5B,IAAK7R,EAAM6R,KACT,OAGF,IAAIqD,EAAgBpV,EAASE,EAAMG,IACnC1F,EAAUya,EAAe,8BAUzB,IAAIC,EAAoC,CAAA,EACxC,IAAK,IAAIC,KAAqBH,EAAW,CACvC,IAGII,OACmB5U,IAHrByU,EAAcE,IAMQ,qBAAtBA,EAEFva,GACGwa,EACD,UAAUH,EAAc/U,GAAE,4BAA4BiV,EAAtD,yGAE8BA,wBAI7BC,GACA7V,EAAmBuT,IAAIqC,KAExBD,EAAaC,GACXH,EAAUG,GAEhB,CAIArQ,OAAO5F,OAAO+V,EAAeC,GAK7BpQ,OAAO5F,OAAO+V,EAAatZ,EAKtBgE,CAAAA,EAAAA,EAAmBsV,GAAc,CACpCrD,UAAMpR,IAEV,CA6BQ6U,CAAoBhF,EAAEtQ,MAAOJ,EAAoBE,QACjDW,IAGF8U,EAAYxT,EAAQhC,KAAI,CAACqC,EAAOX,KAClC,IAAI+T,EAAmBR,EAA6BvT,GAChDiT,EAAaD,EAAcrQ,MAAMkM,GAAMA,EAAEtQ,MAAMG,KAAOiC,EAAMpC,MAAMG,KAyBtE,OAAAvE,KACKwG,EAAK,CACRsS,aACA5J,QAvB0CD,UAExC4K,GACmB,QAAnB7H,EAAQK,SACP7L,EAAMpC,MAAM6R,MAAQzP,EAAMpC,MAAM8R,UAEjC4C,GAAa,GAERA,EA2Cb7J,eACEzC,EACAwF,EACAxL,EACAoT,EACAC,EACAC,GAEA,IAAI5R,EACA6R,EAEAC,EACFC,IAGA,IAAIrN,EAGAM,EAAe,IAAIC,SAA4B,CAACzD,EAAG0D,IAAOR,EAASQ,IACvE2M,EAAWA,IAAMnN,IACjBoF,EAAQvE,OAAO7K,iBAAiB,QAASmX,GAEzC,IAAIG,EAAiBC,GACI,mBAAZF,EACF9M,QAAQP,OACb,IAAI5N,MACF,oEACMwN,EAAI,eAAehG,EAAMpC,MAAMG,GAAE,MAItC0V,EACL,CACEjI,UACAtL,OAAQF,EAAME,OACd0T,QAASN,WAECjV,IAARsV,EAAoB,CAACA,GAAO,IAIhCE,EAA8C,WAChD,IAIE,MAAO,CAAE7N,KAAM,OAAQtE,aAHN2R,EACbA,GAAiBM,GAAiBD,EAAcC,KAChDD,KAIN,CAFE,MAAO7a,GACP,MAAO,CAAEmN,KAAM,QAAStE,OAAQ7I,EAClC,CACD,EATiD,GAWlD,OAAO8N,QAAQc,KAAK,CAACoM,EAAgBnN,GAAc,EAGrD,IACE,IAAI+M,EAAUzT,EAAMpC,MAAMoI,GAG1B,GAAIoN,EACF,GAAIK,EAAS,CAEX,IAAIK,GACCxb,SAAeqO,QAAQ4L,IAAI,CAI9BiB,EAAWC,GAAS7L,OAAO/O,IACzBib,EAAejb,CAAC,IAElBua,IAEF,QAAqB/U,IAAjByV,EACF,MAAMA,EAERpS,EAASpJ,CACX,KAAO,CAKL,SAHM8a,EAENK,EAAUzT,EAAMpC,MAAMoI,IAClByN,EAKG,IAAa,WAATzN,EAAmB,CAC5B,IAAIzJ,EAAM,IAAIP,IAAIwP,EAAQjP,KACtB9C,EAAW8C,EAAI9C,SAAW8C,EAAI7C,OAClC,MAAMwT,GAAuB,IAAK,CAChCrB,OAAQL,EAAQK,OAChBpS,WACAiX,QAAS1Q,EAAMpC,MAAMG,IAEzB,CAGE,MAAO,CAAEiI,KAAM7I,EAAWgD,KAAMuB,YAAQrD,EAC1C,CAbEqD,QAAe8R,EAAWC,EAc9B,KACK,KAAKA,EAAS,CACnB,IAAIlX,EAAM,IAAIP,IAAIwP,EAAQjP,KAE1B,MAAM2Q,GAAuB,IAAK,CAChCzT,SAFa8C,EAAI9C,SAAW8C,EAAI7C,QAIpC,CACEgI,QAAe8R,EAAWC,EAC5B,CAEApb,OACoBgG,IAAlBqD,EAAOA,OACP,gBAAwB,WAATsE,EAAoB,YAAc,YAAjD,eACMhG,EAAMpC,MAAMG,GAA8CiI,4CAAAA,EADhE,+CAaJ,CATE,MAAOnN,GAIP,MAAO,CAAEmN,KAAM7I,EAAWP,MAAO8E,OAAQ7I,EAC3C,CAAU,QACJ0a,GACF/H,EAAQvE,OAAO5K,oBAAoB,QAASkX,EAEhD,CAEA,OAAO7R,CACT,CA1KUqS,CACE/N,EACAwF,EACAxL,EACAoT,EACAC,EACAV,GAEFhM,QAAQ+B,QAAQ,CAAE1C,KAAM7I,EAAWgD,KAAMuB,YAAQrD,MAM9C,IAOP2V,QAAgBvB,EAAiB,CACnC9S,QAASwT,EACT3H,UACAtL,OAAQP,EAAQ,GAAGO,OACnBwS,aACAkB,QAASjB,IAMX,UACQhM,QAAQ4L,IAAIK,EAElB,CADA,MAAO/Z,GACP,CAGF,OAAOmb,CACT,CAqIAvL,eAAewL,GACbC,GAEA,IAAIxS,OAAEA,EAAMsE,KAAEA,GAASkO,EAEvB,GAAIC,GAAWzS,GAAS,CACtB,IAAIvB,EAEJ,IACE,IAAIiU,EAAc1S,EAAO6H,QAAQzB,IAAI,gBAKjC3H,EAFAiU,GAAe,wBAAwBhS,KAAKgS,GAC3B,MAAf1S,EAAOqL,KACF,WAEMrL,EAAOgJ,aAGThJ,EAAOiJ,MAIxB,CAFE,MAAO9R,GACP,MAAO,CAAEmN,KAAM7I,EAAWP,MAAOA,MAAO/D,EAC1C,CAEA,OAAImN,IAAS7I,EAAWP,MACf,CACLoJ,KAAM7I,EAAWP,MACjBA,MAAO,IAAI+M,EAAkBjI,EAAO4H,OAAQ5H,EAAOkI,WAAYzJ,GAC/DmP,WAAY5N,EAAO4H,OACnBC,QAAS7H,EAAO6H,SAIb,CACLvD,KAAM7I,EAAWgD,KACjBA,OACAmP,WAAY5N,EAAO4H,OACnBC,QAAS7H,EAAO6H,QAEpB,CAGsC,IAAA8K,EAAAC,EACAC,EAAAC,EAgCVC,EAAAC,EASQC,EAAAC,EA3CpC,OAAI5O,IAAS7I,EAAWP,MAClBiY,GAAuBnT,GACrBA,EAAOvB,gBAAgB3H,MAClB,CACLwN,KAAM7I,EAAWP,MACjBA,MAAO8E,EAAOvB,KACdmP,WAAuB,OAAbiF,EAAE7S,EAAOoE,WAAI,EAAXyO,EAAajL,OACzBC,eAASiL,EAAA9S,EAAOoE,OAAP0O,EAAajL,QAClB,IAAIC,QAAQ9H,EAAOoE,KAAKyD,cACxBlL,GAKD,CACL2H,KAAM7I,EAAWP,MACjBA,MAAO,IAAI+M,GACE,OAAX0K,EAAA3S,EAAOoE,WAAI,EAAXuO,EAAa/K,SAAU,SACvBjL,EACAqD,EAAOvB,MAETmP,WAAYxF,EAAqBpI,GAAUA,EAAO4H,YAASjL,EAC3DkL,eAAS+K,EAAA5S,EAAOoE,OAAPwO,EAAa/K,QAClB,IAAIC,QAAQ9H,EAAOoE,KAAKyD,cACxBlL,GAGD,CACL2H,KAAM7I,EAAWP,MACjBA,MAAO8E,EACP4N,WAAYxF,EAAqBpI,GAAUA,EAAO4H,YAASjL,GAI3DyW,GAAepT,GACV,CACLsE,KAAM7I,EAAW4X,SACjBC,aAActT,EACd4N,WAAuB,OAAbmF,EAAE/S,EAAOoE,WAAI,EAAX2O,EAAanL,OACzBC,SAASmL,OAAAA,EAAAhT,EAAOoE,WAAP4O,EAAAA,EAAanL,UAAW,IAAIC,QAAQ9H,EAAOoE,KAAKyD,UAIzDsL,GAAuBnT,GAClB,CACLsE,KAAM7I,EAAWgD,KACjBA,KAAMuB,EAAOvB,KACbmP,WAAuB,OAAbqF,EAAEjT,EAAOoE,WAAI,EAAX6O,EAAarL,OACzBC,eAASqL,EAAAlT,EAAOoE,OAAP8O,EAAarL,QAClB,IAAIC,QAAQ9H,EAAOoE,KAAKyD,cACxBlL,GAID,CAAE2H,KAAM7I,EAAWgD,KAAMA,KAAMuB,EACxC,CAGA,SAASuT,GACPC,EACA1J,EACAkF,EACA/Q,EACAnB,EACAqG,GAEA,IAAI9L,EAAWmc,EAAS3L,QAAQzB,IAAI,YAMpC,GALAzP,EACEU,EACA,+EAGGiS,EAAmB5I,KAAKrJ,GAAW,CACtC,IAAIoc,EAAiBxV,EAAQR,MAC3B,EACAQ,EAAQsO,WAAWC,GAAMA,EAAEtQ,MAAMG,KAAO2S,IAAW,GAErD3X,EAAW+S,GACT,IAAI9P,IAAIwP,EAAQjP,KAChB4Y,EACA3W,GACA,EACAzF,EACA8L,GAEFqQ,EAAS3L,QAAQE,IAAI,WAAY1Q,EACnC,CAEA,OAAOmc,CACT,CAEA,SAASE,GACPrc,EACAiW,EACAxQ,GAEA,GAAIwM,EAAmB5I,KAAKrJ,GAAW,CAErC,IAAIsc,EAAqBtc,EACrBwD,EAAM8Y,EAAmB5U,WAAW,MACpC,IAAIzE,IAAIgT,EAAWsG,SAAWD,GAC9B,IAAIrZ,IAAIqZ,GACRE,EAA0D,MAAzC5W,EAAcpC,EAAI9C,SAAU+E,GACjD,GAAIjC,EAAIV,SAAWmT,EAAWnT,QAAU0Z,EACtC,OAAOhZ,EAAI9C,SAAW8C,EAAI7C,OAAS6C,EAAI5C,IAE3C,CACA,OAAOZ,CACT,CAKA,SAASyc,GACPra,EACApC,EACAkO,EACAyG,GAEA,IAAInR,EAAMpB,EAAQQ,UAAU4R,GAAkBxU,IAAWgB,WACrD+L,EAAoB,CAAEmB,UAE1B,GAAIyG,GAAcF,GAAiBE,EAAWpD,YAAa,CACzD,IAAIA,WAAEA,EAAUE,YAAEA,GAAgBkD,EAIlC5H,EAAK+F,OAASvB,EAAWgD,cAEL,qBAAhB9C,GACF1E,EAAKyD,QAAU,IAAIC,QAAQ,CAAE,eAAgBgB,IAC7C1E,EAAKiH,KAAOtI,KAAKC,UAAUgJ,EAAWhD,OACb,eAAhBF,EAET1E,EAAKiH,KAAOW,EAAW/C,KAEP,sCAAhBH,GACAkD,EAAWjD,SAGX3E,EAAKiH,KAAOa,GAA8BF,EAAWjD,UAGrD3E,EAAKiH,KAAOW,EAAWjD,QAE3B,CAEA,OAAO,IAAIgL,QAAQlZ,EAAKuJ,EAC1B,CAEA,SAAS8H,GAA8BnD,GACrC,IAAI0C,EAAe,IAAIb,gBAEvB,IAAK,IAAKnT,EAAKb,KAAUmS,EAASvD,UAEhCiG,EAAaV,OAAOtT,EAAsB,iBAAVb,EAAqBA,EAAQA,EAAMwE,MAGrE,OAAOqQ,CACT,CAEA,SAASU,GACPV,GAEA,IAAI1C,EAAW,IAAIgD,SACnB,IAAK,IAAKtU,EAAKb,KAAU6U,EAAajG,UACpCuD,EAASgC,OAAOtT,EAAKb,GAEvB,OAAOmS,CACT,CAEA,SAASiL,GACP/V,EACAqU,EACAnF,EACA8G,EACAC,GAQA,IAEItG,EAFArP,EAAwC,CAAA,EACxCkP,EAAuC,KAEvC0G,GAAa,EACbC,EAAyC,CAAA,EACzCC,EACFlH,GAAuBE,GAAcF,EAAoB,IACrDA,EAAoB,GAAGjS,WACvByB,EAyFN,OAtFAsB,EAAQkB,SAASb,IACf,KAAMA,EAAMpC,MAAMG,MAAMiW,GACtB,OAEF,IAAIjW,EAAKiC,EAAMpC,MAAMG,GACjB2D,EAASsS,EAAQjW,GAKrB,GAJA1F,GACG2d,GAAiBtU,GAClB,uDAEEqN,GAAcrN,GAAS,CACzB,IAAI9E,EAAQ8E,EAAO9E,MAWnB,QAPqByB,IAAjB0X,IACFnZ,EAAQmZ,EACRA,OAAe1X,GAGjB8Q,EAASA,GAAU,GAEfyG,EACFzG,EAAOpR,GAAMnB,MACR,CAIL,IAAIqZ,EAAgBC,GAAoBvW,EAAS5B,GACX,MAAlCoR,EAAO8G,EAAcrY,MAAMG,MAC7BoR,EAAO8G,EAAcrY,MAAMG,IAAMnB,EAErC,CAGAqD,EAAWlC,QAAMM,EAIZwX,IACHA,GAAa,EACbvG,EAAaxF,EAAqBpI,EAAO9E,OACrC8E,EAAO9E,MAAM0M,OACb,KAEF5H,EAAO6H,UACTuM,EAAc/X,GAAM2D,EAAO6H,QAE/B,MACM4M,GAAiBzU,IACnBiU,EAAgBlM,IAAI1L,EAAI2D,EAAOsT,cAC/B/U,EAAWlC,GAAM2D,EAAOsT,aAAa7U,KAId,MAArBuB,EAAO4N,YACe,MAAtB5N,EAAO4N,YACNuG,IAEDvG,EAAa5N,EAAO4N,YAElB5N,EAAO6H,UACTuM,EAAc/X,GAAM2D,EAAO6H,WAG7BtJ,EAAWlC,GAAM2D,EAAOvB,KAGpBuB,EAAO4N,YAAoC,MAAtB5N,EAAO4N,aAAuBuG,IACrDvG,EAAa5N,EAAO4N,YAElB5N,EAAO6H,UACTuM,EAAc/X,GAAM2D,EAAO6H,SAGjC,SAMmBlL,IAAjB0X,GAA8BlH,IAChCM,EAAS,CAAE,CAACN,EAAoB,IAAKkH,GACrC9V,EAAW4O,EAAoB,SAAMxQ,GAGhC,CACL4B,aACAkP,SACAG,WAAYA,GAAc,IAC1BwG,gBAEJ,CAEA,SAASM,GACPld,EACAyG,EACAqU,EACAnF,EACA2B,EACA6F,EACAV,GAKA,IAAI1V,WAAEA,EAAUkP,OAAEA,GAAWuG,GAC3B/V,EACAqU,EACAnF,EACA8G,GACA,GAoCF,OAhCAnF,EAAqB3P,SAASyV,IAC5B,IAAInd,IAAEA,EAAG6G,MAAEA,EAAK6G,WAAEA,GAAeyP,EAC7B5U,EAAS2U,EAAeld,GAI5B,GAHAd,EAAUqJ,EAAQ,8CAGdmF,IAAcA,EAAWI,OAAOc,QAG7B,GAAIgH,GAAcrN,GAAS,CAChC,IAAIuU,EAAgBC,GAAoBhd,EAAMyG,cAASK,SAAAA,EAAOpC,MAAMG,IAC9DoR,GAAUA,EAAO8G,EAAcrY,MAAMG,MACzCoR,EAAM3V,EAAA,CAAA,EACD2V,EAAM,CACT,CAAC8G,EAAcrY,MAAMG,IAAK2D,EAAO9E,SAGrC1D,EAAM4X,SAAS9I,OAAO7O,EACxB,MAAO,GAAI6c,GAAiBtU,GAG1BrJ,GAAU,EAAO,gDACZ,GAAI8d,GAAiBzU,GAG1BrJ,GAAU,EAAO,uCACZ,CACL,IAAIke,EAAcC,GAAe9U,EAAOvB,MACxCjH,EAAM4X,SAASrH,IAAItQ,EAAKod,EAC1B,KAGK,CAAEtW,aAAYkP,SACvB,CAEA,SAASsH,GACPxW,EACAyW,EACA/W,EACAwP,GAEA,IAAIwH,EAAgBnd,EAAA,CAAA,EAAQkd,GAC5B,IAAK,IAAI1W,KAASL,EAAS,CACzB,IAAI5B,EAAKiC,EAAMpC,MAAMG,GAerB,GAdI2Y,EAAcE,eAAe7Y,QACLM,IAAtBqY,EAAc3Y,KAChB4Y,EAAiB5Y,GAAM2Y,EAAc3Y,SAMXM,IAAnB4B,EAAWlC,IAAqBiC,EAAMpC,MAAM8R,SAGrDiH,EAAiB5Y,GAAMkC,EAAWlC,IAGhCoR,GAAUA,EAAOyH,eAAe7Y,GAElC,KAEJ,CACA,OAAO4Y,CACT,CAEA,SAASE,GACPhI,GAEA,OAAKA,EAGEE,GAAcF,EAAoB,IACrC,CAEEiI,WAAY,CAAC,GAEf,CACEA,WAAY,CACV,CAACjI,EAAoB,IAAKA,EAAoB,GAAG1O,OAThD,EAYX,CAKA,SAAS+V,GACPvW,EACA+Q,GAKA,OAHsBA,EAClB/Q,EAAQR,MAAM,EAAGQ,EAAQsO,WAAWC,GAAMA,EAAEtQ,MAAMG,KAAO2S,IAAW,GACpE,IAAI/Q,IAEUoX,UAAUC,MAAM9I,IAAmC,IAA7BA,EAAEtQ,MAAMsN,oBAC9CvL,EAAQ,EAEZ,CAEA,SAASsX,GAAuB1Z,GAK9B,IAAIK,EACgB,IAAlBL,EAAO2B,OACH3B,EAAO,GACPA,EAAOyZ,MAAMpQ,GAAMA,EAAE5N,QAAU4N,EAAExM,MAAmB,MAAXwM,EAAExM,QAAiB,CAC1D2D,GAAE,wBAGV,MAAO,CACL4B,QAAS,CACP,CACEO,OAAQ,CAAE,EACVzG,SAAU,GACVmJ,aAAc,GACdhF,UAGJA,QAEJ,CAEA,SAASsP,GACP5D,EAAc4N,GAcd,IAbAzd,SACEA,EAAQiX,QACRA,EAAO7E,OACPA,EAAM7F,KACNA,EAAIzN,QACJA,QAOD,IAAA2e,EAAG,CAAA,EAAEA,EAEFtN,EAAa,uBACbuN,EAAe,kCAgCnB,OA9Be,MAAX7N,GACFM,EAAa,cACTiC,GAAUpS,GAAYiX,EACxByG,EACE,cAActL,EAAM,gBAAgBpS,EAApC,+CAC2CiX,EAD3C,+CAGgB,iBAAT1K,EACTmR,EAAe,sCACG,iBAATnR,IACTmR,EAAe,qCAEG,MAAX7N,GACTM,EAAa,YACbuN,EAAyBzG,UAAAA,EAAgCjX,yBAAAA,EAAW,KAChD,MAAX6P,GACTM,EAAa,YACbuN,EAAY,yBAA4B1d,EAAW,KAC/B,MAAX6P,IACTM,EAAa,qBACTiC,GAAUpS,GAAYiX,EACxByG,EACE,cAActL,EAAOyB,cAAa,gBAAgB7T,EAAlD,gDAC4CiX,EAD5C,+CAGO7E,IACTsL,6BAA0CtL,EAAOyB,cAAgB,MAI9D,IAAI3D,EACTL,GAAU,IACVM,EACA,IAAIpR,MAAM2e,IACV,EAEJ,CAGA,SAASC,GACPpD,GAEA,IAAI9M,EAAUvE,OAAOuE,QAAQ8M,GAC7B,IAAK,IAAI3U,EAAI6H,EAAQhI,OAAS,EAAGG,GAAK,EAAGA,IAAK,CAC5C,IAAKlG,EAAKuI,GAAUwF,EAAQ7H,GAC5B,GAAI2W,GAAiBtU,GACnB,MAAO,CAAEvI,MAAKuI,SAElB,CACF,CAEA,SAAS6L,GAAkBnT,GAEzB,OAAOH,EAAUT,EAAA,CAAA,EADgB,iBAATY,EAAoBR,EAAUQ,GAAQA,EAC7B,CAAET,KAAM,KAC3C,CAqCA,SAAS0d,GAAmC3V,GAC1C,OACEyS,GAAWzS,EAAOA,SAAWyI,EAAoBwG,IAAIjP,EAAOA,OAAO4H,OAEvE,CAEA,SAAS6M,GAAiBzU,GACxB,OAAOA,EAAOsE,OAAS7I,EAAW4X,QACpC,CAEA,SAAShG,GAAcrN,GACrB,OAAOA,EAAOsE,OAAS7I,EAAWP,KACpC,CAEA,SAASoZ,GAAiBtU,GACxB,OAAQA,GAAUA,EAAOsE,QAAU7I,EAAWkM,QAChD,CAEO,SAASwL,GACdvc,GAEA,MACmB,iBAAVA,GACE,MAATA,GACA,SAAUA,GACV,SAAUA,GACV,SAAUA,GACK,yBAAfA,EAAM0N,IAEV,CAEO,SAAS8O,GAAexc,GAC7B,IAAIyc,EAAyBzc,EAC7B,OACEyc,GACoB,iBAAbA,GACkB,iBAAlBA,EAAS5U,MACc,mBAAvB4U,EAAS1M,WACW,mBAApB0M,EAASzM,QACgB,mBAAzByM,EAASuC,WAEpB,CAEA,SAASnD,GAAW7b,GAClB,OACW,MAATA,GACwB,iBAAjBA,EAAMgR,QACe,iBAArBhR,EAAMsR,YACY,iBAAlBtR,EAAMiR,cACS,IAAfjR,EAAMyU,IAEjB,CAYA,SAASE,GAAcpB,GACrB,OAAO3B,EAAoByG,IAAI9E,EAAOlI,cACxC,CAEA,SAAS6J,GACP3B,GAEA,OAAO7B,EAAqB2G,IAAI9E,EAAOlI,cACzC,CAEA8E,eAAe8O,GACb5X,EACAqU,EACA/M,EACAuQ,EACA5H,GAEA,IAAI1I,EAAUvE,OAAOuE,QAAQ8M,GAC7B,IAAK,IAAIhb,EAAQ,EAAGA,EAAQkO,EAAQhI,OAAQlG,IAAS,CACnD,IAAK0X,EAAShP,GAAUwF,EAAQlO,GAC5BgH,EAAQL,EAAQqX,MAAM9I,IAAO,MAADA,OAAC,EAADA,EAAGtQ,MAAMG,MAAO2S,IAIhD,IAAK1Q,EACH,SAGF,IAAI6P,EAAe2H,EAAeR,MAC/B9I,GAAMA,EAAEtQ,MAAMG,KAAOiC,EAAOpC,MAAMG,KAEjC0Z,EACc,MAAhB5H,IACCU,GAAmBV,EAAc7P,SAC2B3B,KAA5DuR,GAAqBA,EAAkB5P,EAAMpC,MAAMG,KAElDoY,GAAiBzU,IAAW+V,SAIxBC,GAAoBhW,EAAQuF,GAAQ,GAAOS,MAAMhG,IACjDA,IACFsS,EAAQtD,GAAWhP,EACrB,GAGN,CACF,CAEA+G,eAAekP,GACbhY,EACAqU,EACAxD,GAEA,IAAK,IAAIxX,EAAQ,EAAGA,EAAQwX,EAAqBtR,OAAQlG,IAAS,CAChE,IAAIG,IAAEA,EAAGuX,QAAEA,EAAO7J,WAAEA,GAAe2J,EAAqBxX,GACpD0I,EAASsS,EAAQ7a,GACTwG,EAAQqX,MAAM9I,IAAO,MAADA,OAAC,EAADA,EAAGtQ,MAAMG,MAAO2S,MAQ5CyF,GAAiBzU,KAInBrJ,EACEwO,EACA,8EAEI6Q,GAAoBhW,EAAQmF,EAAWI,QAAQ,GAAMS,MACxDhG,IACKA,IACFsS,EAAQ7a,GAAOuI,EACjB,KAIR,CACF,CAEA+G,eAAeiP,GACbhW,EACAuF,EACA2Q,GAGA,QAHM,IAANA,IAAAA,GAAS,UAEWlW,EAAOsT,aAAasC,YAAYrQ,GACpD,CAIA,GAAI2Q,EACF,IACE,MAAO,CACL5R,KAAM7I,EAAWgD,KACjBA,KAAMuB,EAAOsT,aAAapM,cAQ9B,CANE,MAAO/P,GAEP,MAAO,CACLmN,KAAM7I,EAAWP,MACjBA,MAAO/D,EAEX,CAGF,MAAO,CACLmN,KAAM7I,EAAWgD,KACjBA,KAAMuB,EAAOsT,aAAa7U,KAnB5B,CAqBF,CAEA,SAASkM,GAAmB3S,GAC1B,OAAO,IAAI4S,gBAAgB5S,GAAQ8S,OAAO,SAASxK,MAAMyB,GAAY,KAANA,GACjE,CAEA,SAASuN,GACPrR,EACA5G,GAEA,IAAIW,EACkB,iBAAbX,EAAwBa,EAAUb,GAAUW,OAASX,EAASW,OACvE,GACEiG,EAAQA,EAAQT,OAAS,GAAGtB,MAAM5E,OAClCqT,GAAmB3S,GAAU,IAG7B,OAAOiG,EAAQA,EAAQT,OAAS,GAIlC,IAAI4F,EAAcH,EAA2BhF,GAC7C,OAAOmF,EAAYA,EAAY5F,OAAS,EAC1C,CAEA,SAAS2Y,GACPC,GAEA,IAAIxN,WAAEA,EAAUC,WAAEA,EAAUC,YAAEA,EAAWG,KAAEA,EAAIF,SAAEA,EAAQC,KAAEA,GACzDoN,EACF,GAAKxN,GAAeC,GAAeC,EAInC,OAAY,MAARG,EACK,CACLL,aACAC,aACAC,cACAC,cAAUpM,EACVqM,UAAMrM,EACNsM,QAEmB,MAAZF,EACF,CACLH,aACAC,aACAC,cACAC,WACAC,UAAMrM,EACNsM,UAAMtM,QAEUA,IAATqM,EACF,CACLJ,aACAC,aACAC,cACAC,cAAUpM,EACVqM,OACAC,UAAMtM,QAPH,CAUT,CAEA,SAAS0Z,GACPhf,EACA2U,GAEA,GAAIA,EAAY,CAWd,MAV8C,CAC5CxU,MAAO,UACPH,WACAuR,WAAYoD,EAAWpD,WACvBC,WAAYmD,EAAWnD,WACvBC,YAAakD,EAAWlD,YACxBC,SAAUiD,EAAWjD,SACrBC,KAAMgD,EAAWhD,KACjBC,KAAM+C,EAAW/C,KAGrB,CAWE,MAV8C,CAC5CzR,MAAO,UACPH,WACAuR,gBAAYjM,EACZkM,gBAAYlM,EACZmM,iBAAanM,EACboM,cAAUpM,EACVqM,UAAMrM,EACNsM,UAAMtM,EAIZ,CAEA,SAAS2Z,GACPjf,EACA2U,GAYA,MAViD,CAC/CxU,MAAO,aACPH,WACAuR,WAAYoD,EAAWpD,WACvBC,WAAYmD,EAAWnD,WACvBC,YAAakD,EAAWlD,YACxBC,SAAUiD,EAAWjD,SACrBC,KAAMgD,EAAWhD,KACjBC,KAAM+C,EAAW/C,KAGrB,CAEA,SAASsN,GACPvK,EACAvN,GAEA,GAAIuN,EAAY,CAWd,MAVwC,CACtCxU,MAAO,UACPoR,WAAYoD,EAAWpD,WACvBC,WAAYmD,EAAWnD,WACvBC,YAAakD,EAAWlD,YACxBC,SAAUiD,EAAWjD,SACrBC,KAAMgD,EAAWhD,KACjBC,KAAM+C,EAAW/C,KACjBxK,OAGJ,CAWE,MAVwC,CACtCjH,MAAO,UACPoR,gBAAYjM,EACZkM,gBAAYlM,EACZmM,iBAAanM,EACboM,cAAUpM,EACVqM,UAAMrM,EACNsM,UAAMtM,EACN8B,OAIN,CAmBA,SAASqW,GAAerW,GAWtB,MAVqC,CACnCjH,MAAO,OACPoR,gBAAYjM,EACZkM,gBAAYlM,EACZmM,iBAAanM,EACboM,cAAUpM,EACVqM,UAAMrM,EACNsM,UAAMtM,EACN8B,OAGJ,2WF59KO,SACLtF,GAoBA,YApB8B,IAA9BA,IAAAA,EAAiC,CAAA,GAoB1BJ,GAlBP,SACEK,EACAI,GAEA,IAAIzB,SAAEA,EAAQC,OAAEA,EAAMC,KAAEA,GAASmB,EAAO/B,SACxC,OAAOM,EACL,GACA,CAAEI,WAAUC,SAAQC,QAEnBuB,EAAchC,OAASgC,EAAchC,MAAMD,KAAQ,KACnDiC,EAAchC,OAASgC,EAAchC,MAAMC,KAAQ,UAExD,IAEA,SAA2B2B,EAAgBvB,GACzC,MAAqB,iBAAPA,EAAkBA,EAAKU,EAAWV,EAClD,GAKE,KACAsB,EAEJ,sBA8BO,SACLA,GAqDA,YArD2B,IAA3BA,IAAAA,EAA8B,CAAA,GAqDvBJ,GAnDP,SACEK,EACAI,GAEA,IAAIzB,SACFA,EAAW,IAAGC,OACdA,EAAS,GAAEC,KACXA,EAAO,IACLC,EAAUkB,EAAO/B,SAASY,KAAKK,OAAO,IAY1C,OAJKP,EAASgH,WAAW,MAAShH,EAASgH,WAAW,OACpDhH,EAAW,IAAMA,GAGZJ,EACL,GACA,CAAEI,WAAUC,SAAQC,QAEnBuB,EAAchC,OAASgC,EAAchC,MAAMD,KAAQ,KACnDiC,EAAchC,OAASgC,EAAchC,MAAMC,KAAQ,UAExD,IAEA,SAAwB2B,EAAgBvB,GACtC,IAAIqC,EAAOd,EAAOC,SAASmd,cAAc,QACrCpc,EAAO,GAEX,GAAIF,GAAQA,EAAKuc,aAAa,QAAS,CACrC,IAAI5b,EAAMzB,EAAO/B,SAAS+C,KACtBxB,EAAYiC,EAAIhC,QAAQ,KAC5BuB,GAAsB,IAAfxB,EAAmBiC,EAAMA,EAAI4C,MAAM,EAAG7E,EAC/C,CAEA,OAAOwB,EAAO,KAAqB,iBAAPvC,EAAkBA,EAAKU,EAAWV,GAChE,IAEA,SAA8BR,EAAoBQ,GAChDd,EACkC,MAAhCM,EAASU,SAASU,OAAO,GAAU,6DAC0BsK,KAAKC,UAChEnL,OAGN,GAMEsB,EAEJ,wBAvPO,SACLA,QAA6B,IAA7BA,IAAAA,EAAgC,CAAA,GAEhC,IACIqM,GADAkR,eAAEA,EAAiB,CAAC,KAAIC,aAAEA,EAAYpd,SAAEA,GAAW,GAAUJ,EAEjEqM,EAAUkR,EAAeza,KAAI,CAAC2a,EAAOtf,IACnCuf,EACED,EACiB,iBAAVA,EAAqB,KAAOA,EAAMpf,MAC/B,IAAVF,EAAc,eAAYqF,KAG9B,IAAIrF,EAAQwf,EACM,MAAhBH,EAAuBnR,EAAQhI,OAAS,EAAImZ,GAE1Cjd,EAASjD,EAAOkD,IAChBC,EAA4B,KAEhC,SAASkd,EAAWtb,GAClB,OAAOrD,KAAK4e,IAAI5e,KAAK6e,IAAIxb,EAAG,GAAIgK,EAAQhI,OAAS,EACnD,CACA,SAASyZ,IACP,OAAOzR,EAAQlO,EACjB,CACA,SAASuf,EACPhf,EACAL,EACAC,QADU,IAAVD,IAAAA,EAAa,MAGb,IAAIH,EAAWM,EACb6N,EAAUyR,IAAqBlf,SAAW,IAC1CF,EACAL,EACAC,GAQF,OANAV,EACkC,MAAhCM,EAASU,SAASU,OAAO,8DACkCsK,KAAKC,UAC9DnL,IAGGR,CACT,CAEA,SAAS4B,EAAWpB,GAClB,MAAqB,iBAAPA,EAAkBA,EAAKU,EAAWV,EAClD,CA0DA,MAxD6B,CACvBP,YACF,OAAOA,CACR,EACGoC,aACF,OAAOA,CACR,EACGrC,eACF,OAAO4f,GACR,EACDhe,aACAgB,UAAUpC,GACD,IAAIyC,IAAIrB,EAAWpB,GAAK,oBAEjC+C,eAAe/C,GACb,IAAIa,EAAqB,iBAAPb,EAAkBK,EAAUL,GAAMA,EACpD,MAAO,CACLE,SAAUW,EAAKX,UAAY,GAC3BC,OAAQU,EAAKV,QAAU,GACvBC,KAAMS,EAAKT,MAAQ,GAEtB,EACD6C,KAAKjD,EAAIL,GACPkC,EAASjD,EAAOsE,KAChB,IAAImc,EAAeL,EAAqBhf,EAAIL,GAC5CF,GAAS,EACTkO,EAAQ2R,OAAO7f,EAAOkO,EAAQhI,OAAQ0Z,GAClC3d,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAU6f,EAAcld,MAAO,GAErD,EACDK,QAAQxC,EAAIL,GACVkC,EAASjD,EAAO6E,QAChB,IAAI4b,EAAeL,EAAqBhf,EAAIL,GAC5CgO,EAAQlO,GAAS4f,EACb3d,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAU6f,EAAcld,MAAO,GAErD,EACDuB,GAAGvB,GACDN,EAASjD,EAAOkD,IAChB,IAAII,EAAY+c,EAAWxf,EAAQ0C,GAC/Bkd,EAAe1R,EAAQzL,GAC3BzC,EAAQyC,EACJH,GACFA,EAAS,CAAEF,SAAQrC,SAAU6f,EAAcld,SAE9C,EACDQ,OAAOC,IACLb,EAAWa,EACJ,KACLb,EAAW,IAAI,GAMvB,gCEwaO,SAAsBwK,GAC3B,MAAMgT,EAAehT,EAAKhL,OACtBgL,EAAKhL,OACa,oBAAXA,OACPA,YACAuD,EACE0a,OACoB,IAAjBD,QAC0B,IAA1BA,EAAa/d,eAC2B,IAAxC+d,EAAa/d,SAASie,cACzBC,GAAYF,EAOlB,IAAIvb,EACJ,GANAnF,EACEyN,EAAKvI,OAAO2B,OAAS,EACrB,6DAIE4G,EAAKtI,mBACPA,EAAqBsI,EAAKtI,wBACrB,GAAIsI,EAAKoT,oBAAqB,CAEnC,IAAIA,EAAsBpT,EAAKoT,oBAC/B1b,EAAsBI,IAAW,CAC/BsN,iBAAkBgO,EAAoBtb,IAE1C,MACEJ,EAAqByN,EAIvB,IAQIkO,EAiEAC,EAmDAC,EA5HA3b,EAA0B,CAAA,EAE1B4b,EAAahc,EACfwI,EAAKvI,OACLC,OACAa,EACAX,GAGEc,EAAWsH,EAAKtH,UAAY,IAC5BiU,EAAmB3M,EAAKyT,cAAgBpH,GACxCqH,EAA8B1T,EAAK2T,wBAGnC/N,EAAoBlS,EAAA,CACtBkgB,mBAAmB,EACnBC,wBAAwB,EACxBC,qBAAqB,EACrBC,oBAAoB,EACpBhV,sBAAsB,EACtBiV,gCAAgC,GAC7BhU,EAAK4F,QAGNqO,EAAuC,KAEvCzT,EAAc,IAAIjJ,IAElB2c,EAAsD,KAEtDC,EAAkE,KAElEC,EAAsD,KAOtDC,EAA8C,MAAtBrU,EAAKsU,cAE7BC,EAAiB/b,EAAYgb,EAAYxT,EAAK3K,QAAQpC,SAAUyF,GAChE8b,GAAsB,EACtBC,EAAkC,KAEtC,GAAsB,MAAlBF,IAA2Bb,EAA6B,CAG1D,IAAI5c,EAAQsQ,GAAuB,IAAK,CACtCzT,SAAUqM,EAAK3K,QAAQpC,SAASU,YAE9BkG,QAAEA,EAAO/B,MAAEA,GAAUqZ,GAAuBqC,GAChDe,EAAiB1a,EACjB4a,EAAgB,CAAE,CAAC3c,EAAMG,IAAKnB,EAChC,CAQA,GAAIyd,IAAmBvU,EAAKsU,cAAe,CAC1BI,GACbH,EACAf,EACAxT,EAAK3K,QAAQpC,SAASU,UAEXghB,SACXJ,EAAiB,KAErB,CAGA,GAAKA,EAkBE,GAAIA,EAAerY,MAAMkM,GAAMA,EAAEtQ,MAAM6R,OAG5C2J,GAAc,OACT,GAAKiB,EAAerY,MAAMkM,GAAMA,EAAEtQ,MAAM8R,SAGxC,GAAIhE,EAAOkO,oBAAqB,CAIrC,IAAI3Z,EAAa6F,EAAKsU,cAAgBtU,EAAKsU,cAAcna,WAAa,KAClEkP,EAASrJ,EAAKsU,cAAgBtU,EAAKsU,cAAcjL,OAAS,KAE9D,GAAIA,EAAQ,CACV,IAAI/V,EAAMihB,EAAepM,WACtBC,QAA8B7P,IAAxB8Q,EAAQjB,EAAEtQ,MAAMG,MAEzBqb,EAAciB,EACXlb,MAAM,EAAG/F,EAAM,GACfgG,OAAO8O,IAAOyB,GAA2BzB,EAAEtQ,MAAOqC,EAAYkP,IACnE,MACEiK,EAAciB,EAAejb,OAC1B8O,IAAOyB,GAA2BzB,EAAEtQ,MAAOqC,EAAYkP,IAG9D,MAGEiK,EAAoC,MAAtBtT,EAAKsU,mBAvBnBhB,GAAc,OAjBd,GANAA,GAAc,EACdiB,EAAiB,GAKb3O,EAAOkO,oBAAqB,CAC9B,IAAIc,EAAWF,GACb,KACAlB,EACAxT,EAAK3K,QAAQpC,SAASU,UAEpBihB,EAASD,QAAUC,EAAS/a,UAC9B2a,GAAsB,EACtBD,EAAiBK,EAAS/a,QAE9B,CAkCF,IA0BIgb,EA8EAC,EAxGA1hB,EAAqB,CACvB2hB,cAAe/U,EAAK3K,QAAQC,OAC5BrC,SAAU+M,EAAK3K,QAAQpC,SACvB4G,QAAS0a,EACTjB,cACAtB,WAAYzN,EAEZyQ,sBAA6C,MAAtBhV,EAAKsU,eAAgC,KAC5DW,oBAAoB,EACpBC,aAAc,OACd/a,WAAa6F,EAAKsU,eAAiBtU,EAAKsU,cAAcna,YAAe,CAAE,EACvE6W,WAAahR,EAAKsU,eAAiBtU,EAAKsU,cAActD,YAAe,KACrE3H,OAASrJ,EAAKsU,eAAiBtU,EAAKsU,cAAcjL,QAAWoL,EAC7DzJ,SAAU,IAAImK,IACdC,SAAU,IAAID,KAKZE,EAA+BC,EAAc/f,IAI7CggB,GAA4B,EAM5BC,GAA+B,EAG/BC,EAAmD,IAAIN,IAMvDO,EAAmD,KAInDC,GAA8B,EAM9BnN,GAAyB,EAIzBC,EAAoC,GAIpCC,EAAqC,IAAInR,IAGzCqe,GAAmB,IAAIT,IAGvBU,GAAqB,EAKrBC,IAA2B,EAG3BC,GAAiB,IAAIZ,IAGrBtM,GAAmB,IAAItR,IAGvBqR,GAAmB,IAAIuM,IAGvBa,GAAiB,IAAIb,IAIrBxM,GAAkB,IAAIpR,IAMtBsY,GAAkB,IAAIsF,IAItBc,GAAmB,IAAId,IA+H3B,SAASe,GACPC,EACAnP,QAGC,IAHDA,IAAAA,EAGI,CAAA,GAEJ5T,EAAKM,EAAA,CAAA,EACAN,EACA+iB,GAKL,IAAIC,EAA8B,GAC9BC,EAAgC,GAEhCzQ,EAAOgO,mBACTxgB,EAAM4X,SAASjQ,SAAQ,CAACgQ,EAAS1X,KACT,SAAlB0X,EAAQ3X,QACNuV,GAAgBkC,IAAIxX,GAEtBgjB,EAAoB3f,KAAKrD,GAIzB+iB,EAAkB1f,KAAKrD,GAE3B,IAMJsV,GAAgB5N,SAAS1H,IAClBD,EAAM4X,SAASH,IAAIxX,IAASuiB,GAAiB/K,IAAIxX,IACpDgjB,EAAoB3f,KAAKrD,EAC3B,IAMF,IAAImN,GAAazF,SAASuH,GACxBA,EAAWlP,EAAO,CAChBuV,gBAAiB0N,EACjBC,mBAAoBtP,EAAKsP,mBACzBC,WAA8B,IAAnBvP,EAAKuP,cAKhB3Q,EAAOgO,mBACTwC,EAAkBrb,SAAS1H,GAAQD,EAAM4X,SAAS9I,OAAO7O,KACzDgjB,EAAoBtb,SAAS1H,GAAQmjB,GAAcnjB,MAInDgjB,EAAoBtb,SAAS1H,GAAQsV,GAAgBzG,OAAO7O,IAEhE,CAOA,SAASojB,GACPxjB,EACAkjB,EAA0EO,GAEpE,IAAAC,EAAAC,EAAA,IAaF5F,GAdJuF,UAAEA,QAAoC,IAAAG,EAAG,CAAA,EAAEA,EAOvCG,EACkB,MAApBzjB,EAAM4d,YACyB,MAA/B5d,EAAM4e,WAAWxN,YACjBkD,GAAiBtU,EAAM4e,WAAWxN,aACP,YAA3BpR,EAAM4e,WAAW5e,QACe,KAAlB,OAAdujB,EAAA1jB,EAASG,YAAK,EAAdujB,EAAgBG,aAKd9F,EAFAmF,EAASnF,WACPnU,OAAOyM,KAAK6M,EAASnF,YAAY5X,OAAS,EAC/B+c,EAASnF,WAGT,KAEN6F,EAEIzjB,EAAM4d,WAGN,KAIf,IAAI7W,EAAagc,EAAShc,WACtBwW,GACEvd,EAAM+G,WACNgc,EAAShc,WACTgc,EAAStc,SAAW,GACpBsc,EAAS9M,QAEXjW,EAAM+G,WAINib,EAAWhiB,EAAMgiB,SACjBA,EAASvS,KAAO,IAClBuS,EAAW,IAAID,IAAIC,GACnBA,EAASra,SAAQ,CAACqC,EAAGsF,IAAM0S,EAASzR,IAAIjB,EAAGqC,MAK7C,IAsBIuR,EAtBArB,GAC4B,IAA9BM,GACgC,MAA/BniB,EAAM4e,WAAWxN,YAChBkD,GAAiBtU,EAAM4e,WAAWxN,cACF,KAAhCoS,OAAAA,EAAA3jB,EAASG,YAATwjB,EAAAA,EAAgBE,aAqBpB,GAlBIzD,IACFG,EAAaH,EACbA,OAAqB9a,GAGnBod,GAEON,IAAkBC,EAAc/f,MAEhC8f,IAAkBC,EAAc3e,KACzCqJ,EAAK3K,QAAQqB,KAAKzD,EAAUA,EAASG,OAC5BiiB,IAAkBC,EAAcpe,SACzC8I,EAAK3K,QAAQY,QAAQhD,EAAUA,EAASG,QAMtCiiB,IAAkBC,EAAc/f,IAAK,CAEvC,IAAIwhB,EAAatB,EAAuBzT,IAAI5O,EAAMH,SAASU,UACvDojB,GAAcA,EAAWlM,IAAI5X,EAASU,UACxC2iB,EAAqB,CACnBU,gBAAiB5jB,EAAMH,SACvB6f,aAAc7f,GAEPwiB,EAAuB5K,IAAI5X,EAASU,YAG7C2iB,EAAqB,CACnBU,gBAAiB/jB,EACjB6f,aAAc1f,EAAMH,UAGzB,MAAM,GAAIuiB,EAA8B,CAEvC,IAAIyB,EAAUxB,EAAuBzT,IAAI5O,EAAMH,SAASU,UACpDsjB,EACFA,EAAQxV,IAAIxO,EAASU,WAErBsjB,EAAU,IAAI1f,IAAY,CAACtE,EAASU,WACpC8hB,EAAuB9R,IAAIvQ,EAAMH,SAASU,SAAUsjB,IAEtDX,EAAqB,CACnBU,gBAAiB5jB,EAAMH,SACvB6f,aAAc7f,EAElB,CAEAijB,GAAWxiB,EAAA,CAAA,EAEJyiB,EAAQ,CACXnF,aACA7W,aACA4a,cAAeM,EACfpiB,WACAqgB,aAAa,EACbtB,WAAYzN,EACZ2Q,aAAc,OACdF,sBAAuBkC,GACrBjkB,EACAkjB,EAAStc,SAAWzG,EAAMyG,SAE5Bob,qBACAG,aAEF,CACEkB,qBACAC,WAAyB,IAAdA,IAKflB,EAAgBC,EAAc/f,IAC9BggB,GAA4B,EAC5BC,GAA+B,EAC/BG,GAA8B,EAC9BnN,GAAyB,EACzBC,EAA0B,EAC5B,CAwJA9F,eAAewU,GACbpC,EACA9hB,EACA+T,GAgBA6N,GAA+BA,EAA4BpS,QAC3DoS,EAA8B,KAC9BQ,EAAgBN,EAChBY,GACoD,KAAjD3O,GAAQA,EAAKoQ,gCA8pDlB,SACEnkB,EACA4G,GAEA,GAAIqa,GAAwBE,EAAmB,CAC7C,IAAI/gB,EAAMgkB,GAAapkB,EAAU4G,GACjCqa,EAAqB7gB,GAAO+gB,GAC9B,CACF,CAlqDEkD,CAAmBlkB,EAAMH,SAAUG,EAAMyG,SACzC0b,GAAkE,KAArCvO,GAAQA,EAAKiO,oBAE1CO,GAAuE,KAAvCxO,GAAQA,EAAKuQ,sBAE7C,IAAIzO,EAAcuK,GAAsBG,EACpCgE,EAAoBxQ,GAAQA,EAAKyQ,mBACjC5d,EACE,MAAJmN,GAAAA,EAAMsB,kBACNlV,EAAMyG,SACNzG,EAAMyG,QAAQT,OAAS,IACtBob,EAEGphB,EAAMyG,QACNrB,EAAYsQ,EAAa7V,EAAUyF,GACrC6d,GAAyC,KAA5BvP,GAAQA,EAAKuP,WAQ9B,GACE1c,GACAzG,EAAMkgB,cACL9K,GA27HP,SAA0BvP,EAAaC,GACrC,GAAID,EAAEtF,WAAauF,EAAEvF,UAAYsF,EAAErF,SAAWsF,EAAEtF,OAC9C,OAAO,EAGT,GAAe,KAAXqF,EAAEpF,KAEJ,MAAkB,KAAXqF,EAAErF,KACJ,GAAIoF,EAAEpF,OAASqF,EAAErF,KAEtB,OAAO,EACF,GAAe,KAAXqF,EAAErF,KAEX,OAAO,EAKT,OAAO,CACT,CA78HM6jB,CAAiBtkB,EAAMH,SAAUA,MAC/B+T,GAAQA,EAAKY,YAAcF,GAAiBV,EAAKY,WAAWpD,aAG9D,YADAiS,GAAmBxjB,EAAU,CAAE4G,WAAW,CAAE0c,cAI9C,IAAI3B,EAAWF,GAAc7a,EAASiP,EAAa7V,EAASU,UAM5D,GALIihB,EAASD,QAAUC,EAAS/a,UAC9BA,EAAU+a,EAAS/a,UAIhBA,EAAS,CACZ,IAAI/C,MAAEA,EAAK6gB,gBAAEA,EAAe7f,MAAEA,GAAU8f,GACtC3kB,EAASU,UAaX,YAXA8iB,GACExjB,EACA,CACE4G,QAAS8d,EACTxd,WAAY,CAAE,EACdkP,OAAQ,CACN,CAACvR,EAAMG,IAAKnB,IAGhB,CAAEyf,aAGN,CAGA1B,EAA8B,IAAI7T,gBAClC,IAMI+H,EANArD,EAAUgK,GACZ1P,EAAK3K,QACLpC,EACA4hB,EAA4B1T,OAC5B6F,GAAQA,EAAKY,YAIf,GAAIZ,GAAQA,EAAKiJ,aAKflH,EAAsB,CACpBqH,GAAoBvW,GAAS/B,MAAMG,GACnC,CAAEiI,KAAM7I,EAAWP,MAAOA,MAAOkQ,EAAKiJ,oBAEnC,GACLjJ,GACAA,EAAKY,YACLF,GAAiBV,EAAKY,WAAWpD,YACjC,CAEA,IAAIwE,QAyFRrG,eACE+C,EACAzS,EACA2U,EACA/N,EACAge,EACA7Q,QAAgD,IAAhDA,IAAAA,EAAmD,CAAA,GAKnD,IA4CIpL,EAzCJ,GANAkc,KAIA5B,GAAY,CAAElE,WADGE,GAAwBjf,EAAU2U,IACvB,CAAE2O,WAA8B,IAAnBvP,EAAKuP,YAE1CsB,EAAY,CACd,IAAIE,QAAuBC,GACzBne,EACA5G,EAASU,SACT+R,EAAQvE,QAEV,GAA4B,YAAxB4W,EAAe7X,KACjB,MAAO,CAAE+X,gBAAgB,GACpB,GAA4B,UAAxBF,EAAe7X,KAAkB,CAC1C,IAAI+H,EAAamI,GAAoB2H,EAAeG,gBACjDpgB,MAAMG,GACT,MAAO,CACL4B,QAASke,EAAeG,eACxBnP,oBAAqB,CACnBd,EACA,CACE/H,KAAM7I,EAAWP,MACjBA,MAAOihB,EAAejhB,QAI9B,CAAO,IAAKihB,EAAele,QAAS,CAClC,IAAI8d,gBAAEA,EAAe7gB,MAAEA,EAAKgB,MAAEA,GAAU8f,GACtC3kB,EAASU,UAEX,MAAO,CACLkG,QAAS8d,EACT5O,oBAAqB,CACnBjR,EAAMG,GACN,CACEiI,KAAM7I,EAAWP,MACjBA,UAIR,CACE+C,EAAUke,EAAele,OAE7B,CAIA,IAAIse,EAAcjN,GAAerR,EAAS5G,GAE1C,GAAKklB,EAAYrgB,MAAMxC,QAAW6iB,EAAYrgB,MAAM6R,KAS7C,CAWL,GAFA/N,SARoBwc,GAClB,SACAhlB,EACAsS,EACA,CAACyS,GACDte,EACA,OAEese,EAAYrgB,MAAMG,IAE/ByN,EAAQvE,OAAOc,QACjB,MAAO,CAAEgW,gBAAgB,EAE7B,MAtBErc,EAAS,CACPsE,KAAM7I,EAAWP,MACjBA,MAAOsQ,GAAuB,IAAK,CACjCrB,OAAQL,EAAQK,OAChBpS,SAAUV,EAASU,SACnBiX,QAASuN,EAAYrgB,MAAMG,MAmBjC,GAAIiY,GAAiBtU,GAAS,CAC5B,IAAI3F,EACJ,GAAI+Q,GAAwB,MAAhBA,EAAK/Q,QACfA,EAAU+Q,EAAK/Q,YACV,CASLA,EALeqZ,GACb1T,EAAOwT,SAAS3L,QAAQzB,IAAI,YAC5B,IAAI9L,IAAIwP,EAAQjP,KAChBiC,KAEqBtF,EAAMH,SAASU,SAAWP,EAAMH,SAASW,MAClE,CAKA,aAJMykB,GAAwB3S,EAAS9J,GAAQ,EAAM,CACnDgM,aACA3R,YAEK,CAAEgiB,gBAAgB,EAC3B,CAEA,GAAI5H,GAAiBzU,GACnB,MAAMwL,GAAuB,IAAK,CAAElH,KAAM,iBAG5C,GAAI+I,GAAcrN,GAAS,CAGzB,IAAIuU,EAAgBC,GAAoBvW,EAASse,EAAYrgB,MAAMG,IAWnE,OAJ+B,KAA1B+O,GAAQA,EAAK/Q,WAChBof,EAAgBC,EAAc3e,MAGzB,CACLkD,UACAkP,oBAAqB,CAACoH,EAAcrY,MAAMG,GAAI2D,GAElD,CAEA,MAAO,CACL/B,UACAkP,oBAAqB,CAACoP,EAAYrgB,MAAMG,GAAI2D,GAEhD,CA9N6B0c,CACvB5S,EACAzS,EACA+T,EAAKY,WACL/N,EACA+a,EAASD,OACT,CAAE1e,QAAS+Q,EAAK/Q,QAASsgB,cAG3B,GAAIvN,EAAaiP,eACf,OAKF,GAAIjP,EAAaD,oBAAqB,CACpC,IAAK6B,EAAShP,GAAUoN,EAAaD,oBACrC,GACEE,GAAcrN,IACdoI,EAAqBpI,EAAO9E,QACJ,MAAxB8E,EAAO9E,MAAM0M,OAWb,OATAqR,EAA8B,UAE9B4B,GAAmBxjB,EAAU,CAC3B4G,QAASmP,EAAanP,QACtBM,WAAY,CAAE,EACdkP,OAAQ,CACNuB,CAACA,GAAUhP,EAAO9E,QAK1B,CAEA+C,EAAUmP,EAAanP,SAAWA,EAClCkP,EAAsBC,EAAaD,oBACnCyO,EAAoBvF,GAAqBhf,EAAU+T,EAAKY,YACxD2O,GAAY,EAEZ3B,EAASD,QAAS,EAGlBjP,EAAUgK,GACR1P,EAAK3K,QACLqQ,EAAQjP,IACRiP,EAAQvE,OAEZ,CAGA,IAAI8W,eACFA,EACApe,QAAS0e,EAAcpe,WACvBA,EAAUkP,OACVA,SA2KJ1G,eACE+C,EACAzS,EACA4G,EACAge,EACAJ,EACA7P,EACA4Q,EACAviB,EACAqS,EACAiO,EACAxN,GAGA,IAAIyO,EACFC,GAAsBxF,GAAqBhf,EAAU2U,GAInD6Q,EACF7Q,GACA4Q,GACAzG,GAA4ByF,GAQ1BkB,IACD/C,GACC/P,EAAOkO,qBAAwBxL,GAOnC,GAAIuP,EAAY,CACd,GAAIa,EAA6B,CAC/B,IAAI1H,EAAa2H,GAAqB5P,GACtCmN,GAAWxiB,EAAA,CAEPse,WAAYwF,QACOjf,IAAfyY,EAA2B,CAAEA,cAAe,CAAE,GAEpD,CACEuF,aAGN,CAEA,IAAIwB,QAAuBC,GACzBne,EACA5G,EAASU,SACT+R,EAAQvE,QAGV,GAA4B,YAAxB4W,EAAe7X,KACjB,MAAO,CAAE+X,gBAAgB,GACpB,GAA4B,UAAxBF,EAAe7X,KAAkB,CAC1C,IAAI+H,EAAamI,GAAoB2H,EAAeG,gBACjDpgB,MAAMG,GACT,MAAO,CACL4B,QAASke,EAAeG,eACxB/d,WAAY,CAAE,EACdkP,OAAQ,CACNpB,CAACA,GAAa8P,EAAejhB,OAGnC,CAAO,IAAKihB,EAAele,QAAS,CAClC,IAAI/C,MAAEA,EAAK6gB,gBAAEA,EAAe7f,MAAEA,GAAU8f,GACtC3kB,EAASU,UAEX,MAAO,CACLkG,QAAS8d,EACTxd,WAAY,CAAE,EACdkP,OAAQ,CACN,CAACvR,EAAMG,IAAKnB,GAGlB,CACE+C,EAAUke,EAAele,OAE7B,CAEA,IAAIiP,EAAcuK,GAAsBG,GACnCjH,EAAe7B,GAAwBrC,GAC1CrI,EAAK3K,QACLjC,EACAyG,EACA4e,EACAxlB,EACA2S,EAAOkO,sBAA4C,IAArBxL,EAC9B1C,EAAOoO,+BACPxL,EACAC,EACAC,EACAC,GACAC,GACAC,GACAC,EACApQ,EACAqQ,GAeF,GATA6P,IACGhO,KACG/Q,GAAWA,EAAQqC,MAAMkM,GAAMA,EAAEtQ,MAAMG,KAAO2S,MAC/C2B,GAAiBA,EAAcrQ,MAAMkM,GAAMA,EAAEtQ,MAAMG,KAAO2S,MAG/DkL,KAA4BD,GAGC,IAAzBtJ,EAAcnT,QAAgD,IAAhCsR,EAAqBtR,OAAc,CACnE,IAAIyf,EAAkBC,KAgBtB,OAfArC,GACExjB,EAAQS,EAAA,CAENmG,UACAM,WAAY,CAAE,EAEdkP,OACEN,GAAuBE,GAAcF,EAAoB,IACrD,CAAE,CAACA,EAAoB,IAAKA,EAAoB,GAAGjS,OACnD,MACHia,GAAuBhI,GACtB8P,EAAkB,CAAE7N,SAAU,IAAImK,IAAI/hB,EAAM4X,WAAc,CAAE,GAElE,CAAEuL,cAEG,CAAE0B,gBAAgB,EAC3B,CAEA,GAAIS,EAA6B,CAC/B,IAAIK,EAAgC,CAAA,EACpC,IAAKlB,EAAY,CAEfkB,EAAQ/G,WAAawF,EACrB,IAAIxG,EAAa2H,GAAqB5P,QACnBxQ,IAAfyY,IACF+H,EAAQ/H,WAAaA,EAEzB,CACItG,EAAqBtR,OAAS,IAChC2f,EAAQ/N,SAmId,SACEN,GAUA,OARAA,EAAqB3P,SAASyV,IAC5B,IAAIzF,EAAU3X,EAAM4X,SAAShJ,IAAIwO,EAAGnd,KAChC2lB,EAAsB7G,QACxB5Z,EACAwS,EAAUA,EAAQ1Q,UAAO9B,GAE3BnF,EAAM4X,SAASrH,IAAI6M,EAAGnd,IAAK2lB,EAAoB,IAE1C,IAAI7D,IAAI/hB,EAAM4X,SACvB,CA/IyBiO,CAA+BvO,IAEpDwL,GAAY6C,EAAS,CAAExC,aACzB,CAEA7L,EAAqB3P,SAASyV,IAC5B0I,GAAa1I,EAAGnd,KACZmd,EAAGzP,YAIL6U,GAAiBjS,IAAI6M,EAAGnd,IAAKmd,EAAGzP,WAClC,IAIF,IAAIoY,EAAiCA,IACnCzO,EAAqB3P,SAAS4P,GAAMuO,GAAavO,EAAEtX,OACjDwhB,GACFA,EAA4B1T,OAAO7K,iBACjC,QACA6iB,GAIJ,IAAIC,cAAEA,EAAa7I,eAAEA,SACb8I,GACJjmB,EACAyG,EACA0S,EACA7B,EACAhF,GAGJ,GAAIA,EAAQvE,OAAOc,QACjB,MAAO,CAAEgW,gBAAgB,GAMvBpD,GACFA,EAA4B1T,OAAO5K,oBACjC,QACA4iB,GAIJzO,EAAqB3P,SAASyV,GAAOoF,GAAiB1T,OAAOsO,EAAGnd,OAGhE,IAAIkQ,EAAW+N,GAAa8H,GAC5B,GAAI7V,EAIF,aAHM8U,GAAwB3S,EAASnC,EAAS3H,QAAQ,EAAM,CAC5D3F,YAEK,CAAEgiB,gBAAgB,GAI3B,GADA1U,EAAW+N,GAAaf,GACpBhN,EAQF,OAJAsF,GAAiBpH,IAAI8B,EAASlQ,WACxBglB,GAAwB3S,EAASnC,EAAS3H,QAAQ,EAAM,CAC5D3F,YAEK,CAAEgiB,gBAAgB,GAI3B,IAAI9d,WAAEA,EAAUkP,OAAEA,GAAWiH,GAC3Bld,EACAyG,EACAuf,EACArQ,EACA2B,EACA6F,EACAV,IAIFA,GAAgB9U,SAAQ,CAACmU,EAActE,KACrCsE,EAAa3M,WAAWN,KAIlBA,GAAWiN,EAAa1N,OAC1BqO,GAAgB3N,OAAO0I,EACzB,GACA,IAIAhF,EAAOkO,qBAAuBxL,GAAoBlV,EAAMiW,SAC1DA,EAAM3V,EAAQN,CAAAA,EAAAA,EAAMiW,OAAWA,IAGjC,IAAIwP,EAAkBC,KAClBQ,EAAqBC,GAAqBzD,IAC1C0D,EACFX,GAAmBS,GAAsB5O,EAAqBtR,OAAS,EAEzE,OAAA1F,EAAA,CACEmG,UACAM,aACAkP,UACImQ,EAAuB,CAAExO,SAAU,IAAImK,IAAI/hB,EAAM4X,WAAc,CAAE,EAEzE,CA9aYyO,CACR/T,EACAzS,EACA4G,EACA+a,EAASD,OACT6C,EACAxQ,GAAQA,EAAKY,WACbZ,GAAQA,EAAKwR,kBACbxR,GAAQA,EAAK/Q,QACb+Q,IAAkC,IAA1BA,EAAKsB,iBACbiO,EACAxN,GAGEkP,IAOJpD,EAA8B,KAE9B4B,GAAmBxjB,EAAQS,EAAA,CACzBmG,QAAS0e,GAAkB1e,GACxBkX,GAAuBhI,GAAoB,CAC9C5O,aACAkP,YAEJ,CAmZA,SAASsP,GACP5P,GAEA,OAAIA,IAAwBE,GAAcF,EAAoB,IAIrD,CACL,CAACA,EAAoB,IAAKA,EAAoB,GAAG1O,MAE1CjH,EAAM4d,WAC8B,IAAzCnU,OAAOyM,KAAKlW,EAAM4d,YAAY5X,OACzB,KAEAhG,EAAM4d,gBAJV,CAOT,CAqjBArO,eAAe0V,GACb3S,EACAnC,EACAmW,EAAqBC,GAYrB,IAXA/R,WACEA,EAAU4Q,kBACVA,EAAiBvD,mBACjBA,EAAkBhf,QAClBA,QAMD,IAAA0jB,EAAG,CAAA,EAAEA,EAEFpW,EAAS6L,SAAS3L,QAAQoH,IAAI,wBAChCrC,GAAyB,GAG3B,IAAIvV,EAAWsQ,EAAS6L,SAAS3L,QAAQzB,IAAI,YAC7CzP,EAAUU,EAAU,uDACpBA,EAAWqc,GACTrc,EACA,IAAIiD,IAAIwP,EAAQjP,KAChBiC,GAEF,IAAIkhB,EAAmBrmB,EAAeH,EAAMH,SAAUA,EAAU,CAC9D6jB,aAAa,IAGf,GAAI7D,EAAW,CACb,IAAI4G,GAAmB,EAEvB,GAAItW,EAAS6L,SAAS3L,QAAQoH,IAAI,2BAEhCgP,GAAmB,OACd,GAAI3U,EAAmB5I,KAAKrJ,GAAW,CAC5C,MAAMwD,EAAMuJ,EAAK3K,QAAQQ,UAAU5C,GACnC4mB,EAEEpjB,EAAIV,SAAWid,EAAa/f,SAAS8C,QAEI,MAAzC8C,EAAcpC,EAAI9C,SAAU+E,EAChC,CAEA,GAAImhB,EAMF,YALI5jB,EACF+c,EAAa/f,SAASgD,QAAQhD,GAE9B+f,EAAa/f,SAASgE,OAAOhE,GAInC,CAIA4hB,EAA8B,KAE9B,IAAIiF,GACU,IAAZ7jB,GAAoBsN,EAAS6L,SAAS3L,QAAQoH,IAAI,mBAC9CyK,EAAcpe,QACdoe,EAAc3e,MAIhB6N,WAAEA,EAAUC,WAAEA,EAAUC,YAAEA,GAAgBtR,EAAM4e,YAEjDpK,IACA4Q,GACDhU,GACAC,GACAC,IAEAkD,EAAamK,GAA4B3e,EAAM4e,aAMjD,IAAIyG,EAAmB7Q,GAAc4Q,EACrC,GACElU,EAAkCuG,IAAItH,EAAS6L,SAAS5L,SACxDiV,GACA/Q,GAAiB+Q,EAAiBjU,kBAE5B2S,GAAgB2C,EAAuBF,EAAkB,CAC7DhS,WAAUlU,EAAA,CAAA,EACL+kB,EAAgB,CACnBhU,WAAYxR,IAGdgiB,mBAAoBA,GAAsBM,EAC1CgC,qBAAsBmC,EAClBlE,OACAjd,QAED,CAGL,IAAIkf,EAAqBxF,GACvB2H,EACAhS,SAEIuP,GAAgB2C,EAAuBF,EAAkB,CAC7DnC,qBAEAe,oBAEAvD,mBAAoBA,GAAsBM,EAC1CgC,qBAAsBmC,EAClBlE,OACAjd,GAER,CACF,CAIAoK,eAAeyV,GACblY,EACA9M,EACAsS,EACA6G,EACA1S,EACA+S,GAEA,IAAIsB,EACA6L,EAA0C,CAAA,EAC9C,IACE7L,QAAgBxB,GACdC,EACAzM,EACA9M,EACAsS,EACA6G,EACA1S,EACA+S,EACAhV,EACAF,EAYJ,CAVE,MAAO3E,GASP,OANAwZ,EAAcxR,SAASqN,IACrB2R,EAAY3R,EAAEtQ,MAAMG,IAAM,CACxBiI,KAAM7I,EAAWP,MACjBA,MAAO/D,EACR,IAEIgnB,CACT,CAEA,IAAK,IAAKnP,EAAShP,KAAWiB,OAAOuE,QAAQ8M,GAC3C,GAAIqD,GAAmC3V,GAAS,CAC9C,IAAIwT,EAAWxT,EAAOA,OACtBme,EAAYnP,GAAW,CACrB1K,KAAM7I,EAAWkM,SACjB6L,SAAUD,GACRC,EACA1J,EACAkF,EACA/Q,EACAnB,EACAkN,EAAO7G,sBAGb,MACEgb,EAAYnP,SAAiBuD,GAC3BvS,GAKN,OAAOme,CACT,CAEApX,eAAe0W,GACbjmB,EACAyG,EACA0S,EACAyN,EACAtU,GAEA,IAAIgM,EAAiBte,EAAMyG,QAGvBogB,EAAuB7B,GACzB,SACAhlB,EACAsS,EACA6G,EACA1S,EACA,MAGEqgB,EAAwBrZ,QAAQ4L,IAClCuN,EAAeniB,KAAI8K,UACjB,GAAIgI,EAAE9Q,SAAW8Q,EAAEzQ,OAASyQ,EAAE5J,WAAY,CACxC,IAQInF,SARgBwc,GAClB,SACAhlB,EACAsc,GAAwB1P,EAAK3K,QAASsV,EAAErW,KAAMqW,EAAE5J,WAAWI,QAC3D,CAACwJ,EAAEzQ,OACHyQ,EAAE9Q,QACF8Q,EAAEtX,MAEiBsX,EAAEzQ,MAAMpC,MAAMG,IAEnC,MAAO,CAAE,CAAC0S,EAAEtX,KAAMuI,EACpB,CACE,OAAOiF,QAAQ+B,QAAQ,CACrB,CAAC+H,EAAEtX,KAAM,CACP6M,KAAM7I,EAAWP,MACjBA,MAAOsQ,GAAuB,IAAK,CACjCzT,SAAUgX,EAAErW,SAIpB,KAIA8kB,QAAsBa,EACtB1J,SAAwB2J,GAAuB9d,QACjD,CAACiF,EAAKP,IAAMjE,OAAO5F,OAAOoK,EAAKP,IAC/B,CACF,GAaA,aAXMD,QAAQ4L,IAAI,CAChBgF,GACE5X,EACAuf,EACA1T,EAAQvE,OACRuQ,EACAte,EAAM+G,YAER0X,GAA8BhY,EAAS0W,EAAgByJ,KAGlD,CACLZ,gBACA7I,iBAEJ,CAEA,SAASuH,KAEPtP,GAAyB,EAIzBC,EAAwB/R,QAAQkiB,MAGhChQ,GAAiB7N,SAAQ,CAACqC,EAAG/J,KACvBuiB,GAAiB/K,IAAIxX,IACvBqV,EAAsBjH,IAAIpO,GAE5B6lB,GAAa7lB,EAAI,GAErB,CAEA,SAAS8mB,GACP9mB,EACA0X,EACA/D,QAA6B,IAA7BA,IAAAA,EAAgC,CAAA,GAEhC5T,EAAM4X,SAASrH,IAAItQ,EAAK0X,GACxBmL,GACE,CAAElL,SAAU,IAAImK,IAAI/hB,EAAM4X,WAC1B,CAAEuL,WAAwC,KAA5BvP,GAAQA,EAAKuP,YAE/B,CAEA,SAAS6D,GACP/mB,EACAuX,EACA9T,EACAkQ,QAA6B,IAA7BA,IAAAA,EAAgC,CAAA,GAEhC,IAAImJ,EAAgBC,GAAoBhd,EAAMyG,QAAS+Q,GACvD4L,GAAcnjB,GACd6iB,GACE,CACE7M,OAAQ,CACN,CAAC8G,EAAcrY,MAAMG,IAAKnB,GAE5BkU,SAAU,IAAImK,IAAI/hB,EAAM4X,WAE1B,CAAEuL,WAAwC,KAA5BvP,GAAQA,EAAKuP,YAE/B,CAEA,SAAS8D,GAAwBhnB,GAO/B,OANA2iB,GAAerS,IAAItQ,GAAM2iB,GAAehU,IAAI3O,IAAQ,GAAK,GAGrDsV,GAAgBkC,IAAIxX,IACtBsV,GAAgBzG,OAAO7O,GAElBD,EAAM4X,SAAShJ,IAAI3O,IAAQyR,CACpC,CAEA,SAAS0R,GAAcnjB,GACrB,IAAI0X,EAAU3X,EAAM4X,SAAShJ,IAAI3O,IAK/BuiB,GAAiB/K,IAAIxX,IACnB0X,GAA6B,YAAlBA,EAAQ3X,OAAuB2iB,GAAelL,IAAIxX,IAE/D6lB,GAAa7lB,GAEfuV,GAAiB1G,OAAO7O,GACxB0iB,GAAe7T,OAAO7O,GACtBwV,GAAiB3G,OAAO7O,GAQpBuS,EAAOgO,mBACTjL,GAAgBzG,OAAO7O,GAGzBqV,EAAsBxG,OAAO7O,GAC7BD,EAAM4X,SAAS9I,OAAO7O,EACxB,CAiBA,SAAS6lB,GAAa7lB,GACpB,IAAI0N,EAAa6U,GAAiB5T,IAAI3O,GAClC0N,IACFA,EAAW0B,QACXmT,GAAiB1T,OAAO7O,GAE5B,CAEA,SAASinB,GAAiBhR,GACxB,IAAK,IAAIjW,KAAOiW,EAAM,CACpB,IACImH,EAAcC,GADJ2J,GAAWhnB,GACgBgH,MACzCjH,EAAM4X,SAASrH,IAAItQ,EAAKod,EAC1B,CACF,CAEA,SAASqI,KACP,IAAIyB,EAAW,GACX1B,GAAkB,EACtB,IAAK,IAAIxlB,KAAOwV,GAAkB,CAChC,IAAIkC,EAAU3X,EAAM4X,SAAShJ,IAAI3O,GACjCd,EAAUwY,EAA8B1X,qBAAAA,GAClB,YAAlB0X,EAAQ3X,QACVyV,GAAiB3G,OAAO7O,GACxBknB,EAAS7jB,KAAKrD,GACdwlB,GAAkB,EAEtB,CAEA,OADAyB,GAAiBC,GACV1B,CACT,CAEA,SAASU,GAAqBiB,GAC5B,IAAIC,EAAa,GACjB,IAAK,IAAKpnB,EAAK4E,KAAO8d,GACpB,GAAI9d,EAAKuiB,EAAU,CACjB,IAAIzP,EAAU3X,EAAM4X,SAAShJ,IAAI3O,GACjCd,EAAUwY,EAA8B1X,qBAAAA,GAClB,YAAlB0X,EAAQ3X,QACV8lB,GAAa7lB,GACb0iB,GAAe7T,OAAO7O,GACtBonB,EAAW/jB,KAAKrD,GAEpB,CAGF,OADAinB,GAAiBG,GACVA,EAAWrhB,OAAS,CAC7B,CAYA,SAASshB,GAAcrnB,GACrBD,EAAMgiB,SAASlT,OAAO7O,GACtB4iB,GAAiB/T,OAAO7O,EAC1B,CAGA,SAASsnB,GAActnB,EAAaunB,GAClC,IAAIC,EAAUznB,EAAMgiB,SAASpT,IAAI3O,IAAQ0R,EAIzCxS,EACqB,cAAlBsoB,EAAQznB,OAA8C,YAArBwnB,EAAWxnB,OACxB,YAAlBynB,EAAQznB,OAA4C,YAArBwnB,EAAWxnB,OACxB,YAAlBynB,EAAQznB,OAA4C,eAArBwnB,EAAWxnB,OACxB,YAAlBynB,EAAQznB,OAA4C,cAArBwnB,EAAWxnB,OACxB,eAAlBynB,EAAQznB,OAA+C,cAArBwnB,EAAWxnB,MAAsB,qCACjCynB,EAAQznB,MAAK,OAAOwnB,EAAWxnB,OAGtE,IAAIgiB,EAAW,IAAID,IAAI/hB,EAAMgiB,UAC7BA,EAASzR,IAAItQ,EAAKunB,GAClB1E,GAAY,CAAEd,YAChB,CAEA,SAAS0F,GAAqBxZ,GAQP,IARQ0V,gBAC7BA,EAAelE,aACfA,EAAYiC,cACZA,GAKDzT,EACC,GAA8B,IAA1B2U,GAAiBpT,KACnB,OAKEoT,GAAiBpT,KAAO,GAC1BlQ,GAAQ,EAAO,gDAGjB,IAAIyO,EAAUV,MAAMpB,KAAK2W,GAAiB7U,YACrC2Z,EAAYC,GAAmB5Z,EAAQA,EAAQhI,OAAS,GACzDyhB,EAAUznB,EAAMgiB,SAASpT,IAAI+Y,GAEjC,OAAIF,GAA6B,eAAlBA,EAAQznB,WAAvB,EAQI4nB,EAAgB,CAAEhE,kBAAiBlE,eAAciC,kBAC5CgG,OADT,CAGF,CAEA,SAASnD,GAAsBjkB,GAC7B,IAAImD,EAAQsQ,GAAuB,IAAK,CAAEzT,aACtCmV,EAAcuK,GAAsBG,GACpC3Z,QAAEA,EAAO/B,MAAEA,GAAUqZ,GAAuBrI,GAKhD,OAFA8P,KAEO,CAAEjB,gBAAiB9d,EAAS/B,QAAOhB,QAC5C,CAEA,SAAS8hB,GACPqC,GAEA,IAAIC,EAA8B,GAWlC,OAVArL,GAAgB9U,SAAQ,CAACogB,EAAKvQ,KACvBqQ,IAAaA,EAAUrQ,KAI1BuQ,EAAI3Y,SACJ0Y,EAAkBxkB,KAAKkU,GACvBiF,GAAgB3N,OAAO0I,GACzB,IAEKsQ,CACT,CA+BA,SAAS7D,GAAapkB,EAAoB4G,GACxC,GAAIsa,EAAyB,CAK3B,OAJUA,EACRlhB,EACA4G,EAAQhC,KAAKuQ,GAAMnO,EAA2BmO,EAAGhV,EAAM+G,gBAE3ClH,EAASI,GACzB,CACA,OAAOJ,EAASI,GAClB,CAYA,SAAS6jB,GACPjkB,EACA4G,GAEA,GAAIqa,EAAsB,CACxB,IAAI7gB,EAAMgkB,GAAapkB,EAAU4G,GAC7BuhB,EAAIlH,EAAqB7gB,GAC7B,GAAiB,iBAAN+nB,EACT,OAAOA,CAEX,CACA,OAAO,IACT,CAEA,SAAS1G,GACP7a,EACAiP,EACAnV,GAEA,GAAI+f,EAA6B,CAC/B,IAAK7Z,EAAS,CAQZ,MAAO,CAAE8a,QAAQ,EAAM9a,QAPNlB,EACfmQ,EACAnV,EACA+E,GACA,IAG4C,GAChD,CACE,GAAImE,OAAOyM,KAAKzP,EAAQ,GAAGO,QAAQhB,OAAS,EAAG,CAU7C,MAAO,CAAEub,QAAQ,EAAM9a,QANFlB,EACnBmQ,EACAnV,EACA+E,GACA,GAGJ,CAEJ,CAEA,MAAO,CAAEic,QAAQ,EAAO9a,QAAS,KACnC,CAiBA8I,eAAeqV,GACbne,EACAlG,EACAwN,EACAyL,GAEA,IAAK8G,EACH,MAAO,CAAExT,KAAM,UAAWrG,WAG5B,IAAIqe,EAAkDre,EACtD,OAAa,CACX,IAAIwhB,EAAiC,MAAtBhI,EACXvK,EAAcuK,GAAsBG,EACpC8H,EAAgB1jB,EACpB,UACQ8b,EAA4B,CAChCvS,SACA7M,KAAMX,EACNkG,QAASqe,EACTtL,aACA2O,MAAOA,CAAC3Q,EAASzS,KACXgJ,EAAOc,SACX0J,GACEf,EACAzS,EACA2Q,EACAwS,EACA5jB,EACD,GAeP,CAZE,MAAO3E,GACP,MAAO,CAAEmN,KAAM,QAASpJ,MAAO/D,EAAGmlB,iBACpC,CAAU,QAOJmD,IAAala,EAAOc,UACtBuR,EAAa,IAAIA,GAErB,CAEA,GAAIrS,EAAOc,QACT,MAAO,CAAE/B,KAAM,WAGjB,IAAIsb,EAAahjB,EAAYsQ,EAAanV,EAAU+E,GACpD,GAAI8iB,EACF,MAAO,CAAEtb,KAAM,UAAWrG,QAAS2hB,GAGrC,IAAIC,EAAoB9iB,EACtBmQ,EACAnV,EACA+E,GACA,GAIF,IACG+iB,GACAvD,EAAe9e,SAAWqiB,EAAkBriB,QAC3C8e,EAAe5e,OACb,CAAC8O,EAAG7O,IAAM6O,EAAEtQ,MAAMG,KAAOwjB,EAAmBliB,GAAGzB,MAAMG,KAGzD,MAAO,CAAEiI,KAAM,UAAWrG,QAAS,MAGrCqe,EAAiBuD,CACnB,CACF,CA4EA,OAvCAlI,EAAS,CACH7a,eACF,OAAOA,CACR,EACGkN,aACF,OAAOA,CACR,EACGxS,YACF,OAAOA,CACR,EACGqE,aACF,OAAO+b,CACR,EACGxe,aACF,OAAOge,CACR,EACD0I,WAn1EF,WAiEE,GA9DAzH,EAAkBjU,EAAK3K,QAAQe,QAC7BhC,IAAgD,IAA7CkB,OAAQyf,EAAa9hB,SAAEA,EAAQ2C,MAAEA,GAAOxB,EAGzC,GAAI0gB,EAGF,OAFAA,SACAA,OAA8Bvc,GAIhC5F,EAC4B,IAA1BsjB,GAAiBpT,MAAuB,MAATjN,EAC/B,8YAQF,IAAImlB,EAAaD,GAAsB,CACrC9D,gBAAiB5jB,EAAMH,SACvB6f,aAAc7f,EACd8hB,kBAGF,GAAIgG,GAAuB,MAATnlB,EAAe,CAE/B,IAAI+lB,EAA2B,IAAI9a,SAAe+B,IAChDkS,EAA8BlS,CAAO,IA0BvC,OAxBA5C,EAAK3K,QAAQ8B,IAAY,EAATvB,QAGhB+kB,GAAcI,EAAY,CACxB3nB,MAAO,UACPH,WACA+R,UACE2V,GAAcI,EAAa,CACzB3nB,MAAO,aACP4R,aAASzM,EACT0M,WAAO1M,EACPtF,aAKF0oB,EAAyB/Z,MAAK,IAAM5B,EAAK3K,QAAQ8B,GAAGvB,IACrD,EACDqP,QACE,IAAImQ,EAAW,IAAID,IAAI/hB,EAAMgiB,UAC7BA,EAASzR,IAAIoX,EAAahW,GAC1BmR,GAAY,CAAEd,YAChB,GAGJ,CAEA,OAAO+B,GAAgBpC,EAAe9hB,EAAS,IAI/CggB,EAAW,EAqwJnB,SACE2I,EACAC,GAEA,IACE,IAAIC,EAAmBF,EAAQG,eAAeC,QAC5C1W,GAEF,GAAIwW,EAAkB,CACpB,IAAIlX,EAAOjG,KAAKkJ,MAAMiU,GACtB,IAAK,IAAKpZ,EAAG/E,KAAMd,OAAOuE,QAAQwD,GAAQ,CAAA,GACpCjH,GAAK+C,MAAMC,QAAQhD,IACrBke,EAAYlY,IAAIjB,EAAG,IAAInL,IAAIoG,GAAK,IAGtC,CAEA,CADA,MAAO5K,GACP,CAEJ,CArxJMkpB,CAA0BjJ,EAAcyC,GACxC,IAAIyG,EAA0BA,IAsxJpC,SACEN,EACAC,GAEA,GAAIA,EAAYhZ,KAAO,EAAG,CACxB,IAAI+B,EAAiC,CAAA,EACrC,IAAK,IAAKlC,EAAG/E,KAAMke,EACjBjX,EAAKlC,GAAK,IAAI/E,GAEhB,IACEie,EAAQG,eAAeI,QACrB7W,EACA3G,KAAKC,UAAUgG,GAOnB,CALE,MAAO9N,GACPnE,GACE,EAC8DmE,8DAAAA,OAElE,CACF,CACF,CA1yJQslB,CAA0BpJ,EAAcyC,GAC1CzC,EAAa1c,iBAAiB,WAAY4lB,GAC1CxG,EAA8BA,IAC5B1C,EAAazc,oBAAoB,WAAY2lB,EACjD,CAaA,OANK9oB,EAAMkgB,aACT6D,GAAgB7B,EAAc/f,IAAKnC,EAAMH,SAAU,CACjDqV,kBAAkB,IAIfiL,CACT,EA4vEEhR,UA3uEF,SAAmBlM,GAEjB,OADAmK,EAAYiB,IAAIpL,GACT,IAAMmK,EAAY0B,OAAO7L,EAClC,EAyuEEgmB,wBAjPF,SACEC,EACAC,EACAC,GASA,GAPAtI,EAAuBoI,EACvBlI,EAAoBmI,EACpBpI,EAA0BqI,GAAU,MAK/BnI,GAAyBjhB,EAAM4e,aAAezN,EAAiB,CAClE8P,GAAwB,EACxB,IAAI+G,EAAIlE,GAAuB9jB,EAAMH,SAAUG,EAAMyG,SAC5C,MAALuhB,GACFlF,GAAY,CAAElB,sBAAuBoG,GAEzC,CAEA,MAAO,KACLlH,EAAuB,KACvBE,EAAoB,KACpBD,EAA0B,IAAI,CAElC,EAyNEsI,SArhEF9Z,eAAe8Z,EACbhpB,EACAuT,GAEA,GAAkB,iBAAPvT,EAET,YADAuM,EAAK3K,QAAQ8B,GAAG1D,GAIlB,IAAIipB,EAAiB1W,GACnB5S,EAAMH,SACNG,EAAMyG,QACNnB,EACAkN,EAAOmO,mBACPtgB,EACAmS,EAAO7G,qBACPiI,MAAAA,OAAAA,EAAAA,EAAMd,YACF,MAAJc,OAAI,EAAJA,EAAMb,WAEJ7R,KAAEA,EAAIsT,WAAEA,EAAU9Q,MAAEA,GAAU+P,GAChCjB,EAAOiO,wBACP,EACA6I,EACA1V,GAGEgQ,EAAkB5jB,EAAMH,SACxB6f,EAAevf,EAAeH,EAAMH,SAAUqB,EAAM0S,GAAQA,EAAK5T,OAOrE0f,EAAYpf,EACPof,CAAAA,EAAAA,EACA9S,EAAK3K,QAAQmB,eAAesc,IAGjC,IAAI6J,EAAc3V,GAAwB,MAAhBA,EAAK/Q,QAAkB+Q,EAAK/Q,aAAUsC,EAE5Dwc,EAAgBO,EAAc3e,MAEd,IAAhBgmB,EACF5H,EAAgBO,EAAcpe,SACL,IAAhBylB,GAGK,MAAd/U,GACAF,GAAiBE,EAAWpD,aAC5BoD,EAAWnD,aAAerR,EAAMH,SAASU,SAAWP,EAAMH,SAASW,SAMnEmhB,EAAgBO,EAAcpe,SAGhC,IAAI+d,EACFjO,GAAQ,uBAAwBA,GACA,IAA5BA,EAAKiO,wBACL1c,EAEFge,GAAyC,KAA5BvP,GAAQA,EAAKuP,WAE1BwE,EAAaD,GAAsB,CACrC9D,kBACAlE,eACAiC,kBAGF,IAAIgG,EAwBJ,aAAa5D,GAAgBpC,EAAejC,EAAc,CACxDlL,aAGAqI,aAAcnZ,EACdme,qBACAhf,QAAS+Q,GAAQA,EAAK/Q,QACtBshB,qBAAsBvQ,GAAQA,EAAK4V,eACnCrG,cA9BAoE,GAAcI,EAAY,CACxB3nB,MAAO,UACPH,SAAU6f,EACV9N,UACE2V,GAAcI,EAAa,CACzB3nB,MAAO,aACP4R,aAASzM,EACT0M,WAAO1M,EACPtF,SAAU6f,IAGZ2J,EAAShpB,EAAIuT,EACd,EACD/B,QACE,IAAImQ,EAAW,IAAID,IAAI/hB,EAAMgiB,UAC7BA,EAASzR,IAAIoX,EAAahW,GAC1BmR,GAAY,CAAEd,YAChB,GAeN,EA46DEyH,MA1wCF,SACExpB,EACAuX,EACA5U,EACAgR,GAEA,GAAImM,EACF,MAAM,IAAIzgB,MACR,oMAMJwmB,GAAa7lB,GAEb,IAAIkjB,GAAyC,KAA5BvP,GAAQA,EAAKuP,WAE1BzN,EAAcuK,GAAsBG,EACpCkJ,EAAiB1W,GACnB5S,EAAMH,SACNG,EAAMyG,QACNnB,EACAkN,EAAOmO,mBACP/d,EACA4P,EAAO7G,qBACP6L,EACI,MAAJ5D,OAAI,EAAJA,EAAMb,UAEJtM,EAAUrB,EAAYsQ,EAAa4T,EAAgBhkB,GAEnDkc,EAAWF,GAAc7a,EAASiP,EAAa4T,GAKnD,GAJI9H,EAASD,QAAUC,EAAS/a,UAC9BA,EAAU+a,EAAS/a,UAGhBA,EAOH,YANAugB,GACE/mB,EACAuX,EACAxD,GAAuB,IAAK,CAAEzT,SAAU+oB,IACxC,CAAEnG,cAKN,IAAIjiB,KAAEA,EAAIsT,WAAEA,EAAU9Q,MAAEA,GAAU+P,GAChCjB,EAAOiO,wBACP,EACA6I,EACA1V,GAGF,GAAIlQ,EAEF,YADAsjB,GAAgB/mB,EAAKuX,EAAS9T,EAAO,CAAEyf,cAIzC,IAAIrc,EAAQgR,GAAerR,EAASvF,GAEhC2gB,GAA2D,KAArCjO,GAAQA,EAAKiO,oBAEnCrN,GAAcF,GAAiBE,EAAWpD,YAiChD7B,eACEtP,EACAuX,EACAtW,EACA4F,EACA4iB,EACAjF,EACAtB,EACAtB,EACArN,GAKA,SAASmV,EAAwB3U,GAC/B,IAAKA,EAAEtQ,MAAMxC,SAAW8S,EAAEtQ,MAAM6R,KAAM,CACpC,IAAI7S,EAAQsQ,GAAuB,IAAK,CACtCrB,OAAQ6B,EAAWpD,WACnB7Q,SAAUW,EACVsW,QAASA,IAGX,OADAwP,GAAgB/mB,EAAKuX,EAAS9T,EAAO,CAAEyf,eAChC,CACT,CACA,OAAO,CACT,CAEA,GAhBAuB,KACAlP,GAAiB1G,OAAO7O,IAenBwkB,GAAckF,EAAwB7iB,GACzC,OAIF,IAAI8iB,EAAkB5pB,EAAM4X,SAAShJ,IAAI3O,GACzC8mB,GAAmB9mB,EA0lHvB,SACEuU,EACAoV,GAYA,MAV2C,CACzC5pB,MAAO,aACPoR,WAAYoD,EAAWpD,WACvBC,WAAYmD,EAAWnD,WACvBC,YAAakD,EAAWlD,YACxBC,SAAUiD,EAAWjD,SACrBC,KAAMgD,EAAWhD,KACjBC,KAAM+C,EAAW/C,KACjBxK,KAAM2iB,EAAkBA,EAAgB3iB,UAAO9B,EAGnD,CAzmH4B0kB,CAAqBrV,EAAYoV,GAAkB,CACzEzG,cAGF,IAAI2G,EAAkB,IAAIlc,gBACtBmc,EAAezN,GACjB1P,EAAK3K,QACLf,EACA4oB,EAAgB/b,OAChByG,GAGF,GAAIiQ,EAAY,CACd,IAAIE,QAAuBC,GACzB8E,EACA,IAAI5mB,IAAIinB,EAAa1mB,KAAK9C,SAC1BwpB,EAAahc,OACb9N,GAGF,GAA4B,YAAxB0kB,EAAe7X,KACjB,OACK,GAA4B,UAAxB6X,EAAe7X,KAExB,YADAka,GAAgB/mB,EAAKuX,EAASmN,EAAejhB,MAAO,CAAEyf,cAEjD,IAAKwB,EAAele,QAOzB,YANAugB,GACE/mB,EACAuX,EACAxD,GAAuB,IAAK,CAAEzT,SAAUW,IACxC,CAAEiiB,cAOJ,GAAIwG,EAFJ7iB,EAAQgR,GADR4R,EAAiB/E,EAAele,QACOvF,IAGrC,MAGN,CAGAshB,GAAiBjS,IAAItQ,EAAK6pB,GAE1B,IAAIE,EAAoBvH,GASpB7M,SARsBoP,GACxB,SACAhlB,EACA+pB,EACA,CAACjjB,GACD4iB,EACAzpB,IAE+B6G,EAAMpC,MAAMG,IAE7C,GAAIklB,EAAahc,OAAOc,QAMtB,YAHI2T,GAAiB5T,IAAI3O,KAAS6pB,GAChCtH,GAAiB1T,OAAO7O,IAQ5B,GAAIuS,EAAOgO,mBAAqBjL,GAAgBkC,IAAIxX,IAClD,GAAI6c,GAAiBlH,IAAiBC,GAAcD,GAElD,YADAmR,GAAmB9mB,EAAKqd,QAAenY,QAIpC,CACL,GAAI2X,GAAiBlH,GAEnB,OADA4M,GAAiB1T,OAAO7O,GACpByiB,GAA0BsH,OAK5BjD,GAAmB9mB,EAAKqd,QAAenY,KAGvCsQ,GAAiBpH,IAAIpO,GACrB8mB,GAAmB9mB,EAAK8e,GAAkBvK,IACnCyQ,GAAwB8E,EAAcnU,GAAc,EAAO,CAChEwP,kBAAmB5Q,EACnBqN,wBAMN,GAAIhM,GAAcD,GAEhB,YADAoR,GAAgB/mB,EAAKuX,EAAS5B,EAAalS,MAG/C,CAEA,GAAIuZ,GAAiBrH,GACnB,MAAM5B,GAAuB,IAAK,CAAElH,KAAM,iBAK5C,IAAI4S,EAAe1f,EAAM4e,WAAW/e,UAAYG,EAAMH,SAClDoqB,EAAsB3N,GACxB1P,EAAK3K,QACLyd,EACAoK,EAAgB/b,QAEd2H,EAAcuK,GAAsBG,EACpC3Z,EACyB,SAA3BzG,EAAM4e,WAAW5e,MACboF,EAAYsQ,EAAa1V,EAAM4e,WAAW/e,SAAUyF,GACpDtF,EAAMyG,QAEZtH,EAAUsH,EAAS,gDAEnB,IAAIyjB,IAAWzH,GACfE,GAAepS,IAAItQ,EAAKiqB,GAExB,IAAIC,EAAcpL,GAAkBvK,EAAYoB,EAAa3O,MAC7DjH,EAAM4X,SAASrH,IAAItQ,EAAKkqB,GAExB,IAAKhR,EAAe7B,GAAwBrC,GAC1CrI,EAAK3K,QACLjC,EACAyG,EACA+N,EACAkL,GACA,EACAlN,EAAOoO,+BACPxL,EACAC,EACAC,EACAC,GACAC,GACAC,GACAC,EACApQ,EACA,CAACwB,EAAMpC,MAAMG,GAAI+Q,IAMnB0B,EACGvO,QAAQqU,GAAOA,EAAGnd,MAAQA,IAC1B0H,SAASyV,IACR,IAAIgN,EAAWhN,EAAGnd,IACd2pB,EAAkB5pB,EAAM4X,SAAShJ,IAAIwb,GACrCxE,EAAsB7G,QACxB5Z,EACAykB,EAAkBA,EAAgB3iB,UAAO9B,GAE3CnF,EAAM4X,SAASrH,IAAI6Z,EAAUxE,GAC7BE,GAAasE,GACThN,EAAGzP,YACL6U,GAAiBjS,IAAI6Z,EAAUhN,EAAGzP,WACpC,IAGJmV,GAAY,CAAElL,SAAU,IAAImK,IAAI/hB,EAAM4X,YAEtC,IAAImO,EAAiCA,IACnCzO,EAAqB3P,SAASyV,GAAO0I,GAAa1I,EAAGnd,OAEvD6pB,EAAgB/b,OAAO7K,iBACrB,QACA6iB,GAGF,IAAIC,cAAEA,EAAa7I,eAAEA,SACb8I,GACJjmB,EACAyG,EACA0S,EACA7B,EACA2S,GAGJ,GAAIH,EAAgB/b,OAAOc,QACzB,OAGFib,EAAgB/b,OAAO5K,oBACrB,QACA4iB,GAGFpD,GAAe7T,OAAO7O,GACtBuiB,GAAiB1T,OAAO7O,GACxBqX,EAAqB3P,SAAS+F,GAAM8U,GAAiB1T,OAAOpB,EAAEzN,OAE9D,IAAIkQ,EAAW+N,GAAa8H,GAC5B,GAAI7V,EACF,OAAO8U,GACLgF,EACA9Z,EAAS3H,QACT,EACA,CAAEqZ,uBAKN,GADA1R,EAAW+N,GAAaf,GACpBhN,EAKF,OADAsF,GAAiBpH,IAAI8B,EAASlQ,KACvBglB,GACLgF,EACA9Z,EAAS3H,QACT,EACA,CAAEqZ,uBAKN,IAAI9a,WAAEA,EAAUkP,OAAEA,GAAWiH,GAC3Bld,EACAyG,EACAuf,OACA7gB,EACAmS,EACA6F,EACAV,IAKF,GAAIzc,EAAM4X,SAASH,IAAIxX,GAAM,CAC3B,IAAIod,EAAcC,GAAe1H,EAAa3O,MAC9CjH,EAAM4X,SAASrH,IAAItQ,EAAKod,EAC1B,CAEA8I,GAAqB+D,GAMQ,YAA3BlqB,EAAM4e,WAAW5e,OACjBkqB,EAASxH,IAETvjB,EAAU8iB,EAAe,2BACzBR,GAA+BA,EAA4BpS,QAE3DgU,GAAmBrjB,EAAM4e,WAAW/e,SAAU,CAC5C4G,UACAM,aACAkP,SACA2B,SAAU,IAAImK,IAAI/hB,EAAM4X,cAM1BkL,GAAY,CACV7M,SACAlP,WAAYwW,GACVvd,EAAM+G,WACNA,EACAN,EACAwP,GAEF2B,SAAU,IAAImK,IAAI/hB,EAAM4X,YAE1BxC,GAAyB,EAE7B,CAnVIiV,CACEpqB,EACAuX,EACAtW,EACA4F,EACAL,EACA+a,EAASD,OACT4B,EACAtB,EACArN,IAOJgB,GAAiBjF,IAAItQ,EAAK,CAAEuX,UAAStW,SAsUvCqO,eACEtP,EACAuX,EACAtW,EACA4F,EACAL,EACAge,EACAtB,EACAtB,EACArN,GAEA,IAAIoV,EAAkB5pB,EAAM4X,SAAShJ,IAAI3O,GACzC8mB,GACE9mB,EACA8e,GACEvK,EACAoV,EAAkBA,EAAgB3iB,UAAO9B,GAE3C,CAAEge,cAGJ,IAAI2G,EAAkB,IAAIlc,gBACtBmc,EAAezN,GACjB1P,EAAK3K,QACLf,EACA4oB,EAAgB/b,QAGlB,GAAI0W,EAAY,CACd,IAAIE,QAAuBC,GACzBne,EACA,IAAI3D,IAAIinB,EAAa1mB,KAAK9C,SAC1BwpB,EAAahc,OACb9N,GAGF,GAA4B,YAAxB0kB,EAAe7X,KACjB,OACK,GAA4B,UAAxB6X,EAAe7X,KAExB,YADAka,GAAgB/mB,EAAKuX,EAASmN,EAAejhB,MAAO,CAAEyf,cAEjD,IAAKwB,EAAele,QAOzB,YANAugB,GACE/mB,EACAuX,EACAxD,GAAuB,IAAK,CAAEzT,SAAUW,IACxC,CAAEiiB,cAKJrc,EAAQgR,GADRrR,EAAUke,EAAele,QACOvF,EAEpC,CAGAshB,GAAiBjS,IAAItQ,EAAK6pB,GAE1B,IAAIE,EAAoBvH,GASpBja,SARgBwc,GAClB,SACAhlB,EACA+pB,EACA,CAACjjB,GACDL,EACAxG,IAEmB6G,EAAMpC,MAAMG,IAM7BoY,GAAiBzU,KACnBA,QACSgW,GAAoBhW,EAAQuhB,EAAahc,QAAQ,IACxDvF,GAKAga,GAAiB5T,IAAI3O,KAAS6pB,GAChCtH,GAAiB1T,OAAO7O,GAG1B,GAAI8pB,EAAahc,OAAOc,QACtB,OAKF,GAAI0G,GAAgBkC,IAAIxX,GAEtB,YADA8mB,GAAmB9mB,EAAKqd,QAAenY,IAKzC,GAAI2X,GAAiBtU,GACnB,OAAIka,GAA0BsH,OAG5BjD,GAAmB9mB,EAAKqd,QAAenY,KAGvCsQ,GAAiBpH,IAAIpO,cACfglB,GAAwB8E,EAAcvhB,GAAQ,EAAO,CACzDqZ,wBAON,GAAIhM,GAAcrN,GAEhB,YADAwe,GAAgB/mB,EAAKuX,EAAShP,EAAO9E,OAIvCvE,GAAW8d,GAAiBzU,GAAS,mCAGrCue,GAAmB9mB,EAAKqd,GAAe9U,EAAOvB,MAChD,CA/bEqjB,CACErqB,EACAuX,EACAtW,EACA4F,EACAL,EACA+a,EAASD,OACT4B,EACAtB,EACArN,GAEJ,EAgrCE+V,WAx6DF,WACE7F,KACA5B,GAAY,CAAEhB,aAAc,YAIG,eAA3B9hB,EAAM4e,WAAW5e,QAOU,SAA3BA,EAAM4e,WAAW5e,MAUrB+jB,GACE9B,GAAiBjiB,EAAM2hB,cACvB3hB,EAAM4e,WAAW/e,SACjB,CACEwkB,mBAAoBrkB,EAAM4e,WAE1BuF,sBAAuD,IAAjC/B,IAfxB2B,GAAgB/jB,EAAM2hB,cAAe3hB,EAAMH,SAAU,CACnDmkB,gCAAgC,IAiBtC,EA24DEviB,WAAapB,GAAWuM,EAAK3K,QAAQR,WAAWpB,GAChD+C,eAAiB/C,GAAWuM,EAAK3K,QAAQmB,eAAe/C,GACxD4mB,cACA7D,cA/ZF,SAAqCnjB,GACnC,IAAIuqB,GAAS5H,GAAehU,IAAI3O,IAAQ,GAAK,EACzCuqB,GAAS,GACX5H,GAAe9T,OAAO7O,GACtBsV,GAAgBlH,IAAIpO,GACfuS,EAAOgO,mBACV4C,GAAcnjB,IAGhB2iB,GAAerS,IAAItQ,EAAKuqB,GAG1B1H,GAAY,CAAElL,SAAU,IAAImK,IAAI/hB,EAAM4X,WACxC,EAmZE6S,QApwEF,WACM5J,GACFA,IAEEyB,GACFA,IAEFlV,EAAYsd,QACZjJ,GAA+BA,EAA4BpS,QAC3DrP,EAAM4X,SAASjQ,SAAQ,CAACqC,EAAG/J,IAAQmjB,GAAcnjB,KACjDD,EAAMgiB,SAASra,SAAQ,CAACqC,EAAG/J,IAAQqnB,GAAcrnB,IACnD,EA0vEE0qB,WAjWF,SAAoB1qB,EAAagD,GAC/B,IAAIwkB,EAAmBznB,EAAMgiB,SAASpT,IAAI3O,IAAQ0R,EAMlD,OAJIkR,GAAiBjU,IAAI3O,KAASgD,GAChC4f,GAAiBtS,IAAItQ,EAAKgD,GAGrBwkB,CACT,EA0VEH,iBACAsD,YAxDF,SACEpT,EACAzS,GAEA,IAAIkjB,EAAiC,MAAtBhI,EAEf1H,GACEf,EACAzS,EAHgBkb,GAAsBG,EAKtC5b,EACAF,GAQE2jB,IACF7H,EAAa,IAAIA,GACjB0C,GAAY,CAAE,GAElB,EAkCE+H,0BAA2BrI,GAC3BsI,yBAA0BrO,GAG1BsO,mBAvEF,SAA4BrS,GAC1BlU,EAAW,CAAA,EACXyb,EAAqB7b,EACnBsU,EACApU,OACAa,EACAX,EAEJ,GAkEO2b,CACT,wBA2BO,SACL9b,EACAuP,GAEAzU,EACEkF,EAAO2B,OAAS,EAChB,oEAGF,IAEI1B,EAFAE,EAA0B,CAAA,EAC1Bc,GAAYsO,EAAOA,EAAKtO,SAAW,OAAS,IAEhD,GAAQ,MAAJsO,GAAAA,EAAMtP,mBACRA,EAAqBsP,EAAKtP,wBACrB,SAAIsP,GAAAA,EAAMoM,oBAAqB,CAEpC,IAAIA,EAAsBpM,EAAKoM,oBAC/B1b,EAAsBI,IAAW,CAC/BsN,iBAAkBgO,EAAoBtb,IAE1C,MACEJ,EAAqByN,EAGvB,IAAIS,EAAiClS,EAAA,CACnCqL,sBAAsB,EACtB8G,qBAAqB,GACjBmB,EAAOA,EAAKpB,OAAS,MAGvB4N,EAAahc,EACfC,EACAC,OACAa,EACAX,GA+MF+K,eAAeyb,EACb1Y,EACAzS,EACA4G,EACAgT,EACA4G,EACA3D,EACAuO,GAEA9rB,EACEmT,EAAQvE,OACR,wEAGF,IACE,GAAIuG,GAAiBhC,EAAQK,OAAOlI,eAAgB,CAClD,IAAIjC,QA8CV+G,eACE+C,EACA7L,EACAse,EACAtL,EACA4G,EACA3D,EACAnK,GAEA,IAAI/J,EAEJ,GAAKuc,EAAYrgB,MAAMxC,QAAW6iB,EAAYrgB,MAAM6R,KAa7C,CAUL/N,SAToBwc,EAClB,SACA1S,EACA,CAACyS,GACDte,EACA8L,EACAkH,EACA4G,IAEe0E,EAAYrgB,MAAMG,IAE/ByN,EAAQvE,OAAOc,SACjBwD,GAA+BC,EAASC,EAAgBC,EAE5D,KA5B0D,CACxD,IAAI9O,EAAQsQ,GAAuB,IAAK,CACtCrB,OAAQL,EAAQK,OAChBpS,SAAU,IAAIuC,IAAIwP,EAAQjP,KAAK9C,SAC/BiX,QAASuN,EAAYrgB,MAAMG,KAE7B,GAAI0N,EACF,MAAM7O,EAER8E,EAAS,CACPsE,KAAM7I,EAAWP,MACjBA,QAEJ,CAiBA,GAAIoZ,GAAiBtU,GAKnB,MAAM,IAAIgI,SAAS,KAAM,CACvBJ,OAAQ5H,EAAOwT,SAAS5L,OACxBC,QAAS,CACP6a,SAAU1iB,EAAOwT,SAAS3L,QAAQzB,IAAI,eAK5C,GAAIqO,GAAiBzU,GAAS,CAC5B,IAAI9E,EAAQsQ,GAAuB,IAAK,CAAElH,KAAM,iBAChD,GAAIyF,EACF,MAAM7O,EAER8E,EAAS,CACPsE,KAAM7I,EAAWP,MACjBA,QAEJ,CAEA,GAAI6O,EAAgB,CAGlB,GAAIsD,GAAcrN,GAChB,MAAMA,EAAO9E,MAGf,MAAO,CACL+C,QAAS,CAACse,GACVhe,WAAY,CAAE,EACd6W,WAAY,CAAE,CAACmH,EAAYrgB,MAAMG,IAAK2D,EAAOvB,MAC7CgP,OAAQ,KAGRG,WAAY,IACZwG,cAAe,CAAE,EACjBuO,cAAe,CAAE,EACjB1O,gBAAiB,KAErB,CAGA,IAAI2O,EAAgB,IAAI7O,QAAQjK,EAAQjP,IAAK,CAC3CgN,QAASiC,EAAQjC,QACjBF,SAAUmC,EAAQnC,SAClBpC,OAAQuE,EAAQvE,SAGlB,GAAI8H,GAAcrN,GAAS,CAGzB,IAAIuU,EAAgBL,EAChBqI,EACA/H,GAAoBvW,EAASse,EAAYrgB,MAAMG,IAanD,OAAAvE,WAXoB+qB,EAClBD,EACA3kB,EACAgT,EACA4G,EACA3D,EACA,KACA,CAACK,EAAcrY,MAAMG,GAAI2D,IAKf,CACV4N,WAAYxF,EAAqBpI,EAAO9E,OACpC8E,EAAO9E,MAAM0M,OACQ,MAArB5H,EAAO4N,WACP5N,EAAO4N,WACP,IACJwH,WAAY,KACZuN,cAAa7qB,EAAA,GACPkI,EAAO6H,QAAU,CAAE,CAAC0U,EAAYrgB,MAAMG,IAAK2D,EAAO6H,SAAY,KAGxE,CAWA,OAAA/P,WAToB+qB,EAClBD,EACA3kB,EACAgT,EACA4G,EACA3D,EACA,MAIU,CACVkB,WAAY,CACV,CAACmH,EAAYrgB,MAAMG,IAAK2D,EAAOvB,OAG7BuB,EAAO4N,WAAa,CAAEA,WAAY5N,EAAO4N,YAAe,GAAE,CAC9D+U,cAAe3iB,EAAO6H,QAClB,CAAE,CAAC0U,EAAYrgB,MAAMG,IAAK2D,EAAO6H,SACjC,CAAC,GAET,CA/LyBib,CACjBhZ,EACA7L,EACAwkB,GAAcnT,GAAerR,EAAS5G,GACtC4Z,EACA4G,EACA3D,EACc,MAAduO,GAEF,OAAOziB,CACT,CAEA,IAAIA,QAAe6iB,EACjB/Y,EACA7L,EACAgT,EACA4G,EACA3D,EACAuO,GAEF,OAAOhQ,GAAWzS,GACdA,EAAMlI,EAAA,CAAA,EAEDkI,EAAM,CACToV,WAAY,KACZuN,cAAe,CAAC,GAkBxB,CAhBE,MAAOxrB,GAIP,GAwzDN,SAA8B6I,GAC5B,OACY,MAAVA,GACkB,iBAAXA,GACP,SAAUA,GACV,WAAYA,IACXA,EAAOsE,OAAS7I,EAAWgD,MAAQuB,EAAOsE,OAAS7I,EAAWP,MAEnE,CAh0DU6nB,CAAqB5rB,IAAMsb,GAAWtb,EAAE6I,QAAS,CACnD,GAAI7I,EAAEmN,OAAS7I,EAAWP,MACxB,MAAM/D,EAAE6I,OAEV,OAAO7I,EAAE6I,MACX,CAGA,GA+2DN,SAA4BA,GAC1B,IAAKyS,GAAWzS,GACd,OAAO,EAGT,IAAI4H,EAAS5H,EAAO4H,OAChBvQ,EAAW2I,EAAO6H,QAAQzB,IAAI,YAClC,OAAOwB,GAAU,KAAOA,GAAU,KAAmB,MAAZvQ,CAC3C,CAv3DU2rB,CAAmB7rB,GACrB,OAAOA,EAET,MAAMA,CACR,CACF,CAqJA4P,eAAe8b,EACb/Y,EACA7L,EACAgT,EACA4G,EACA3D,EACAuO,EACAtV,GAQA,IAAIpD,EAA+B,MAAd0Y,EAGrB,GACE1Y,IACC0Y,MAAAA,IAAAA,EAAYvmB,MAAM8R,UAClByU,MAAAA,IAAAA,EAAYvmB,MAAM6R,MAEnB,MAAMvC,GAAuB,IAAK,CAChCrB,OAAQL,EAAQK,OAChBpS,SAAU,IAAIuC,IAAIwP,EAAQjP,KAAK9C,SAC/BiX,QAAmB,MAAVyT,OAAU,EAAVA,EAAYvmB,MAAMG,KAI/B,IAKIsU,GALiB8R,EACjB,CAACA,GACDtV,GAAuBE,GAAcF,EAAoB,IACzDf,GAA8BnO,EAASkP,EAAoB,IAC3DlP,GAC+BsC,QAChCiM,GAAMA,EAAEtQ,MAAM8R,QAAUxB,EAAEtQ,MAAM6R,OAInC,GAA6B,IAAzB4C,EAAcnT,OAChB,MAAO,CACLS,UAEAM,WAAYN,EAAQuC,QAClB,CAACiF,EAAK+G,IAAMvL,OAAO5F,OAAOoK,EAAK,CAAE,CAAC+G,EAAEtQ,MAAMG,IAAK,QAC/C,CAAA,GAEFoR,OACEN,GAAuBE,GAAcF,EAAoB,IACrD,CACE,CAACA,EAAoB,IAAKA,EAAoB,GAAGjS,OAEnD,KACN0S,WAAY,IACZwG,cAAe,CAAE,EACjBH,gBAAiB,MAIrB,IAAI3B,QAAgBkK,EAClB,SACA1S,EACA6G,EACA1S,EACA8L,EACAkH,EACA4G,GAGE/N,EAAQvE,OAAOc,SACjBwD,GAA+BC,EAASC,EAAgBC,GAI1D,IAAIiK,EAAkB,IAAIsF,IACtBrH,EAAU8B,GACZ/V,EACAqU,EACAnF,EACA8G,EACAC,GAIE+O,EAAkB,IAAItnB,IACxBgV,EAAc1U,KAAKqC,GAAUA,EAAMpC,MAAMG,MAQ3C,OANA4B,EAAQkB,SAASb,IACV2kB,EAAgBhU,IAAI3Q,EAAMpC,MAAMG,MACnC6V,EAAQ3T,WAAWD,EAAMpC,MAAMG,IAAM,KACvC,IAGFvE,KACKoa,EAAO,CACVjU,UACAgW,gBACEA,EAAgBhN,KAAO,EACnBhG,OAAOiiB,YAAYjP,EAAgBzO,WACnC,MAEV,CAIAuB,eAAeyV,EACblY,EACAwF,EACA6G,EACA1S,EACA8L,EACAkH,EACA4G,GAEA,IAAIvF,QAAgBxB,GAClB+G,GAAgBpH,GAChBnM,EACA,KACAwF,EACA6G,EACA1S,EACA,KACAjC,EACAF,EACAmV,GAGEkN,EAA0C,CAAA,EA6B9C,aA5BMlZ,QAAQ4L,IACZ5S,EAAQhC,KAAI8K,UACV,KAAMzI,EAAMpC,MAAMG,MAAMiW,GACtB,OAEF,IAAItS,EAASsS,EAAQhU,EAAMpC,MAAMG,IACjC,GAAIsZ,GAAmC3V,GAAS,CAG9C,MAAMuT,GAFSvT,EAAOA,OAIpB8J,EACAxL,EAAMpC,MAAMG,GACZ4B,EACAnB,EACAkN,EAAO7G,qBAEX,CACA,GAAIsP,GAAWzS,EAAOA,SAAW+J,EAG/B,MAAM/J,EAGRme,EAAY7f,EAAMpC,MAAMG,UAChBkW,GAAsCvS,EAAO,KAGlDme,CACT,CAEA,MAAO,CACLvG,aACAuL,MAriBFpc,eACE+C,EAAgBsZ,GAU0B,IAT1CnS,eACEA,EAAciD,wBACdA,EAAuB2D,aACvBA,QAKD,IAAAuL,EAAG,CAAA,EAAEA,EAEFvoB,EAAM,IAAIP,IAAIwP,EAAQjP,KACtBsP,EAASL,EAAQK,OACjB9S,EAAWM,EAAe,GAAIY,EAAWsC,GAAM,KAAM,WACrDoD,EAAUrB,EAAYgb,EAAYvgB,EAAUyF,GAGhD,IAAKyO,GAAcpB,IAAsB,SAAXA,EAAmB,CAC/C,IAAIjP,EAAQsQ,GAAuB,IAAK,CAAErB,YACpClM,QAASolB,EAAuBnnB,MAAEA,GACtCqZ,GAAuBqC,GACzB,MAAO,CACL9a,WACAzF,WACA4G,QAASolB,EACT9kB,WAAY,CAAE,EACd6W,WAAY,KACZ3H,OAAQ,CACN,CAACvR,EAAMG,IAAKnB,GAEd0S,WAAY1S,EAAM0M,OAClBwM,cAAe,CAAE,EACjBuO,cAAe,CAAE,EACjB1O,gBAAiB,KAErB,CAAO,IAAKhW,EAAS,CACnB,IAAI/C,EAAQsQ,GAAuB,IAAK,CAAEzT,SAAUV,EAASU,YACvDkG,QAAS8d,EAAe7f,MAAEA,GAC9BqZ,GAAuBqC,GACzB,MAAO,CACL9a,WACAzF,WACA4G,QAAS8d,EACTxd,WAAY,CAAE,EACd6W,WAAY,KACZ3H,OAAQ,CACN,CAACvR,EAAMG,IAAKnB,GAEd0S,WAAY1S,EAAM0M,OAClBwM,cAAe,CAAE,EACjBuO,cAAe,CAAE,EACjB1O,gBAAiB,KAErB,CAEA,IAAIjU,QAAewiB,EACjB1Y,EACAzS,EACA4G,EACAgT,EACA4G,GAAgB,MACY,IAA5B3D,EACA,MAEF,OAAIzB,GAAWzS,GACNA,EAMTlI,EAAA,CAAST,WAAUyF,YAAakD,EAClC,EA6dEsjB,WAjcFvc,eACE+C,EAAgByZ,GAUF,IATdvU,QACEA,EAAOiC,eACPA,EAAc4G,aACdA,QAKD,IAAA0L,EAAG,CAAA,EAAEA,EAEF1oB,EAAM,IAAIP,IAAIwP,EAAQjP,KACtBsP,EAASL,EAAQK,OACjB9S,EAAWM,EAAe,GAAIY,EAAWsC,GAAM,KAAM,WACrDoD,EAAUrB,EAAYgb,EAAYvgB,EAAUyF,GAGhD,IAAKyO,GAAcpB,IAAsB,SAAXA,GAAgC,YAAXA,EACjD,MAAMqB,GAAuB,IAAK,CAAErB,WAC/B,IAAKlM,EACV,MAAMuN,GAAuB,IAAK,CAAEzT,SAAUV,EAASU,WAGzD,IAAIuG,EAAQ0Q,EACR/Q,EAAQqX,MAAM9I,GAAMA,EAAEtQ,MAAMG,KAAO2S,IACnCM,GAAerR,EAAS5G,GAE5B,GAAI2X,IAAY1Q,EACd,MAAMkN,GAAuB,IAAK,CAChCzT,SAAUV,EAASU,SACnBiX,YAEG,IAAK1Q,EAEV,MAAMkN,GAAuB,IAAK,CAAEzT,SAAUV,EAASU,WAGzD,IAAIiI,QAAewiB,EACjB1Y,EACAzS,EACA4G,EACAgT,EACA4G,GAAgB,MAChB,EACAvZ,GAGF,GAAImU,GAAWzS,GACb,OAAOA,EAGT,IAAI9E,EAAQ8E,EAAOyN,OAASxM,OAAOuiB,OAAOxjB,EAAOyN,QAAQ,QAAK9Q,EAC9D,QAAcA,IAAVzB,EAKF,MAAMA,EAIR,GAAI8E,EAAOoV,WACT,OAAOnU,OAAOuiB,OAAOxjB,EAAOoV,YAAY,GAG1C,GAAIpV,EAAOzB,WAAY,CAAA,IAAAklB,EACrB,IAAIhlB,EAAOwC,OAAOuiB,OAAOxjB,EAAOzB,YAAY,GAI5C,OAHIklB,OAAJA,EAAIzjB,EAAOiU,kBAAPwP,EAAyBnlB,EAAMpC,MAAMG,MACvCoC,EAAKkL,IAA0B3J,EAAOiU,gBAAgB3V,EAAMpC,MAAMG,KAE7DoC,CACT,CAGF,EAwXF,SDjnFO,SAAiBA,EAAS2F,GAC/B,OAAO,IAAIF,EACTzF,EACgB,iBAAT2F,EAAoB,CAAEwD,OAAQxD,GAASA,EAElD,UA+MoC,SAAC3F,EAAM2F,GAGzC,YAH6C,IAAJA,IAAAA,EAAO,CAAA,GAGzC,IAAII,EAAa/F,EAFW,iBAAT2F,EAAoB,CAAEwD,OAAQxD,GAASA,EAGnE,iBA9uBO,SACLsf,EACAllB,QAEC,IAFDA,IAAAA,EAEI,CAAA,GAEJ,IAAI9F,EAAegrB,EACfhrB,EAAKmH,SAAS,MAAiB,MAATnH,IAAiBA,EAAKmH,SAAS,QACvD9I,GACE,EACA,eAAe2B,EAAf,oCACMA,EAAK2B,QAAQ,MAAO,MAD1B,qIAGsC3B,EAAK2B,QAAQ,MAAO,MAAK,MAEjE3B,EAAOA,EAAK2B,QAAQ,MAAO,OAI7B,MAAMspB,EAASjrB,EAAKqG,WAAW,KAAO,IAAM,GAEtCiE,EAAa4gB,GACZ,MAALA,EAAY,GAAkB,iBAANA,EAAiBA,EAAIxnB,OAAOwnB,GA4BtD,OAAOD,EA1BUjrB,EACd+G,MAAM,OACNxD,KAAI,CAACwE,EAASnJ,EAAOusB,KAIpB,GAHsBvsB,IAAUusB,EAAMrmB,OAAS,GAGd,MAAZiD,EAAiB,CAGpC,OAAOuC,EAAUxE,EAFJ,KAGf,CAEA,MAAMslB,EAAWrjB,EAAQnC,MAAM,oBAC/B,GAAIwlB,EAAU,CACZ,OAASrsB,EAAKssB,GAAYD,EAC1B,IAAIE,EAAQxlB,EAAO/G,GAEnB,OADAd,EAAuB,MAAbotB,GAA6B,MAATC,EAAa,aAAevsB,EAAG,WACtDuL,EAAUghB,EACnB,CAGA,OAAOvjB,EAAQpG,QAAQ,OAAQ,GAAG,IAGnCkG,QAAQE,KAAcA,IAEAnE,KAAK,IAChC,8BCgmGO,SACLT,EACAqW,EACAhX,GASA,OAPoCpD,EAAA,CAAA,EAC/Boa,EAAO,CACVtE,WAAYxF,EAAqBlN,GAASA,EAAM0M,OAAS,IACzD6F,OAAQ,CACN,CAACyE,EAAQ+R,4BAA8BpoB,EAAO,GAAGQ,IAAKnB,IAI5D,kBDxtFO,SAAuBrD,GAE5B,MAAc,KAAPA,GAAuC,KAAzBA,EAAYE,SAC7B,IACc,iBAAPF,EACPK,EAAUL,GAAIE,SACdF,EAAGE,QACT,gGA0CkC,SAAC0G,EAAM2F,QAAI,IAAJA,IAAAA,EAAO,CAAA,GAC9C,IAAIK,EAA+B,iBAATL,EAAoB,CAAEwD,OAAQxD,GAASA,EAE7DyD,EAAU,IAAIC,QAAQrD,EAAaoD,SAKvC,OAJKA,EAAQoH,IAAI,iBACfpH,EAAQE,IAAI,eAAgB,mCAGvB,IAAIC,SAASjF,KAAKC,UAAUvE,GAAK3G,EAAA,CAAA,EACnC2M,EAAY,CACfoD,YAEJ,oGA0QkDqc,CAACrpB,EAAKuJ,KACtD,IAAIoP,EAAW7L,EAAS9M,EAAKuJ,GAE7B,OADAoP,EAAS3L,QAAQE,IAAI,0BAA2B,QACzCyL,CAAQ,YASwBnZ,CAACQ,EAAKuJ,KAC7C,IAAIoP,EAAW7L,EAAS9M,EAAKuJ,GAE7B,OADAoP,EAAS3L,QAAQE,IAAI,kBAAmB,QACjCyL,CAAQ"}