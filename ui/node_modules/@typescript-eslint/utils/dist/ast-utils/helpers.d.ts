import type { AST_NODE_TYPES, AST_TOKEN_TYPES, TSESTree } from '../ts-estree';
export declare const isNodeOfType: <NodeType extends AST_NODE_TYPES>(nodeType: NodeType) => (node: TSESTree.Node | null | undefined) => node is Extract<TSESTree.Program, {
    type: NodeType;
}> | Extract<TSESTree.AccessorPropertyComputedName, {
    type: NodeType;
}> | Extract<TSESTree.AccessorPropertyNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.ArrayExpression, {
    type: NodeType;
}> | Extract<TSESTree.ArrayPattern, {
    type: NodeType;
}> | Extract<TSESTree.ArrowFunctionExpression, {
    type: NodeType;
}> | Extract<TSESTree.AssignmentExpression, {
    type: NodeType;
}> | Extract<TSESTree.AssignmentPattern, {
    type: NodeType;
}> | Extract<TSESTree.AwaitExpression, {
    type: NodeType;
}> | Extract<TSESTree.BinaryExpression, {
    type: NodeType;
}> | Extract<TSESTree.BlockStatement, {
    type: NodeType;
}> | Extract<TSESTree.BreakStatement, {
    type: NodeType;
}> | Extract<TSESTree.CallExpression, {
    type: NodeType;
}> | Extract<TSESTree.CatchClause, {
    type: NodeType;
}> | Extract<TSESTree.ChainExpression, {
    type: NodeType;
}> | Extract<TSESTree.ClassBody, {
    type: NodeType;
}> | Extract<TSESTree.ClassDeclarationWithName, {
    type: NodeType;
}> | Extract<TSESTree.ClassDeclarationWithOptionalName, {
    type: NodeType;
}> | Extract<TSESTree.ClassExpression, {
    type: NodeType;
}> | Extract<TSESTree.ConditionalExpression, {
    type: NodeType;
}> | Extract<TSESTree.ContinueStatement, {
    type: NodeType;
}> | Extract<TSESTree.DebuggerStatement, {
    type: NodeType;
}> | Extract<TSESTree.Decorator, {
    type: NodeType;
}> | Extract<TSESTree.DoWhileStatement, {
    type: NodeType;
}> | Extract<TSESTree.EmptyStatement, {
    type: NodeType;
}> | Extract<TSESTree.ExportAllDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.ExportDefaultDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.ExportNamedDeclarationWithoutSourceWithMultiple, {
    type: NodeType;
}> | Extract<TSESTree.ExportNamedDeclarationWithoutSourceWithSingle, {
    type: NodeType;
}> | Extract<TSESTree.ExportNamedDeclarationWithSource, {
    type: NodeType;
}> | Extract<TSESTree.ExportSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.ExpressionStatement, {
    type: NodeType;
}> | Extract<TSESTree.ForInStatement, {
    type: NodeType;
}> | Extract<TSESTree.ForOfStatement, {
    type: NodeType;
}> | Extract<TSESTree.ForStatement, {
    type: NodeType;
}> | Extract<TSESTree.FunctionDeclarationWithName, {
    type: NodeType;
}> | Extract<TSESTree.FunctionDeclarationWithOptionalName, {
    type: NodeType;
}> | Extract<TSESTree.FunctionExpression, {
    type: NodeType;
}> | Extract<TSESTree.Identifier, {
    type: NodeType;
}> | Extract<TSESTree.IfStatement, {
    type: NodeType;
}> | Extract<TSESTree.ImportAttribute, {
    type: NodeType;
}> | Extract<TSESTree.ImportDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.ImportDefaultSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.ImportExpression, {
    type: NodeType;
}> | Extract<TSESTree.ImportNamespaceSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.ImportSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.JSXAttribute, {
    type: NodeType;
}> | Extract<TSESTree.JSXClosingElement, {
    type: NodeType;
}> | Extract<TSESTree.JSXClosingFragment, {
    type: NodeType;
}> | Extract<TSESTree.JSXElement, {
    type: NodeType;
}> | Extract<TSESTree.JSXEmptyExpression, {
    type: NodeType;
}> | Extract<TSESTree.JSXExpressionContainer, {
    type: NodeType;
}> | Extract<TSESTree.JSXFragment, {
    type: NodeType;
}> | Extract<TSESTree.JSXIdentifier, {
    type: NodeType;
}> | Extract<TSESTree.JSXMemberExpression, {
    type: NodeType;
}> | Extract<TSESTree.JSXNamespacedName, {
    type: NodeType;
}> | Extract<TSESTree.JSXOpeningElement, {
    type: NodeType;
}> | Extract<TSESTree.JSXOpeningFragment, {
    type: NodeType;
}> | Extract<TSESTree.JSXSpreadAttribute, {
    type: NodeType;
}> | Extract<TSESTree.JSXSpreadChild, {
    type: NodeType;
}> | Extract<TSESTree.JSXText, {
    type: NodeType;
}> | Extract<TSESTree.LabeledStatement, {
    type: NodeType;
}> | Extract<TSESTree.BigIntLiteral, {
    type: NodeType;
}> | Extract<TSESTree.BooleanLiteral, {
    type: NodeType;
}> | Extract<TSESTree.NullLiteral, {
    type: NodeType;
}> | Extract<TSESTree.NumberLiteral, {
    type: NodeType;
}> | Extract<TSESTree.RegExpLiteral, {
    type: NodeType;
}> | Extract<TSESTree.StringLiteral, {
    type: NodeType;
}> | Extract<TSESTree.LogicalExpression, {
    type: NodeType;
}> | Extract<TSESTree.MemberExpressionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.MemberExpressionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.MetaProperty, {
    type: NodeType;
}> | Extract<TSESTree.MethodDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.MethodDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.NewExpression, {
    type: NodeType;
}> | Extract<TSESTree.ObjectExpression, {
    type: NodeType;
}> | Extract<TSESTree.ObjectPattern, {
    type: NodeType;
}> | Extract<TSESTree.PrivateIdentifier, {
    type: NodeType;
}> | Extract<TSESTree.PropertyComputedName, {
    type: NodeType;
}> | Extract<TSESTree.PropertyNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.PropertyDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.PropertyDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.RestElement, {
    type: NodeType;
}> | Extract<TSESTree.ReturnStatement, {
    type: NodeType;
}> | Extract<TSESTree.SequenceExpression, {
    type: NodeType;
}> | Extract<TSESTree.SpreadElement, {
    type: NodeType;
}> | Extract<TSESTree.StaticBlock, {
    type: NodeType;
}> | Extract<TSESTree.Super, {
    type: NodeType;
}> | Extract<TSESTree.SwitchCase, {
    type: NodeType;
}> | Extract<TSESTree.SwitchStatement, {
    type: NodeType;
}> | Extract<TSESTree.TaggedTemplateExpression, {
    type: NodeType;
}> | Extract<TSESTree.TemplateElement, {
    type: NodeType;
}> | Extract<TSESTree.TemplateLiteral, {
    type: NodeType;
}> | Extract<TSESTree.ThisExpression, {
    type: NodeType;
}> | Extract<TSESTree.ThrowStatement, {
    type: NodeType;
}> | Extract<TSESTree.TryStatement, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractAccessorPropertyComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractAccessorPropertyNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractMethodDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractMethodDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractPropertyDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractPropertyDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAnyKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSArrayType, {
    type: NodeType;
}> | Extract<TSESTree.TSAsExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSAsyncKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSBigIntKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSBooleanKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSCallSignatureDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSClassImplements, {
    type: NodeType;
}> | Extract<TSESTree.TSConditionalType, {
    type: NodeType;
}> | Extract<TSESTree.TSConstructorType, {
    type: NodeType;
}> | Extract<TSESTree.TSConstructSignatureDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSDeclareFunction, {
    type: NodeType;
}> | Extract<TSESTree.TSDeclareKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSEmptyBodyFunctionExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSEnumDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSEnumMemberComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSEnumMemberNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSExportAssignment, {
    type: NodeType;
}> | Extract<TSESTree.TSExportKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSExternalModuleReference, {
    type: NodeType;
}> | Extract<TSESTree.TSFunctionType, {
    type: NodeType;
}> | Extract<TSESTree.TSImportEqualsDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSImportType, {
    type: NodeType;
}> | Extract<TSESTree.TSIndexedAccessType, {
    type: NodeType;
}> | Extract<TSESTree.TSIndexSignature, {
    type: NodeType;
}> | Extract<TSESTree.TSInferType, {
    type: NodeType;
}> | Extract<TSESTree.TSInstantiationExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSInterfaceBody, {
    type: NodeType;
}> | Extract<TSESTree.TSInterfaceDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSInterfaceHeritage, {
    type: NodeType;
}> | Extract<TSESTree.TSIntersectionType, {
    type: NodeType;
}> | Extract<TSESTree.TSIntrinsicKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSLiteralType, {
    type: NodeType;
}> | Extract<TSESTree.TSMappedType, {
    type: NodeType;
}> | Extract<TSESTree.TSMethodSignatureComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSMethodSignatureNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleBlock, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationGlobal, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationModuleWithIdentifierId, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationModuleWithStringIdDeclared, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationModuleWithStringIdNotDeclared, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationNamespace, {
    type: NodeType;
}> | Extract<TSESTree.TSNamedTupleMember, {
    type: NodeType;
}> | Extract<TSESTree.TSNamespaceExportDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSNeverKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSNonNullExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSNullKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSNumberKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSObjectKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSOptionalType, {
    type: NodeType;
}> | Extract<TSESTree.TSParameterProperty, {
    type: NodeType;
}> | Extract<TSESTree.TSPrivateKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSPropertySignatureComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSPropertySignatureNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSProtectedKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSPublicKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSQualifiedName, {
    type: NodeType;
}> | Extract<TSESTree.TSReadonlyKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSRestType, {
    type: NodeType;
}> | Extract<TSESTree.TSSatisfiesExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSStaticKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSStringKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSSymbolKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSTemplateLiteralType, {
    type: NodeType;
}> | Extract<TSESTree.TSThisType, {
    type: NodeType;
}> | Extract<TSESTree.TSTupleType, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeAliasDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeAnnotation, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeAssertion, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeLiteral, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeOperator, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeParameter, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeParameterDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeParameterInstantiation, {
    type: NodeType;
}> | Extract<TSESTree.TSTypePredicate, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeQuery, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeReference, {
    type: NodeType;
}> | Extract<TSESTree.TSUndefinedKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSUnionType, {
    type: NodeType;
}> | Extract<TSESTree.TSUnknownKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSVoidKeyword, {
    type: NodeType;
}> | Extract<TSESTree.UnaryExpression, {
    type: NodeType;
}> | Extract<TSESTree.UpdateExpression, {
    type: NodeType;
}> | Extract<TSESTree.LetOrConstOrVarDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.UsingInForOfDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.UsingInNormalContextDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.LetOrConstOrVarDeclarator, {
    type: NodeType;
}> | Extract<TSESTree.UsingInForOfDeclarator, {
    type: NodeType;
}> | Extract<TSESTree.UsingInNomalConextDeclarator, {
    type: NodeType;
}> | Extract<TSESTree.WhileStatement, {
    type: NodeType;
}> | Extract<TSESTree.WithStatement, {
    type: NodeType;
}> | Extract<TSESTree.YieldExpression, {
    type: NodeType;
}>;
export declare const isNodeOfTypes: <NodeTypes extends readonly AST_NODE_TYPES[]>(nodeTypes: NodeTypes) => (node: TSESTree.Node | null | undefined) => node is Extract<TSESTree.Program, {
    type: NodeTypes[number];
}> | Extract<TSESTree.AccessorPropertyComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.AccessorPropertyNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ArrayExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ArrayPattern, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ArrowFunctionExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.AssignmentExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.AssignmentPattern, {
    type: NodeTypes[number];
}> | Extract<TSESTree.AwaitExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.BinaryExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.BlockStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.BreakStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.CallExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.CatchClause, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ChainExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ClassBody, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ClassDeclarationWithName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ClassDeclarationWithOptionalName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ClassExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ConditionalExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ContinueStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.DebuggerStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.Decorator, {
    type: NodeTypes[number];
}> | Extract<TSESTree.DoWhileStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.EmptyStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExportAllDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExportDefaultDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExportNamedDeclarationWithoutSourceWithMultiple, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExportNamedDeclarationWithoutSourceWithSingle, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExportNamedDeclarationWithSource, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExportSpecifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ExpressionStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ForInStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ForOfStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ForStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.FunctionDeclarationWithName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.FunctionDeclarationWithOptionalName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.FunctionExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.Identifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.IfStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ImportAttribute, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ImportDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ImportDefaultSpecifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ImportExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ImportNamespaceSpecifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ImportSpecifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXAttribute, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXClosingElement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXClosingFragment, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXElement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXEmptyExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXExpressionContainer, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXFragment, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXIdentifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXMemberExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXNamespacedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXOpeningElement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXOpeningFragment, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXSpreadAttribute, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXSpreadChild, {
    type: NodeTypes[number];
}> | Extract<TSESTree.JSXText, {
    type: NodeTypes[number];
}> | Extract<TSESTree.LabeledStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.BigIntLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.BooleanLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.NullLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.NumberLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.RegExpLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.StringLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.LogicalExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.MemberExpressionComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.MemberExpressionNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.MetaProperty, {
    type: NodeTypes[number];
}> | Extract<TSESTree.MethodDefinitionComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.MethodDefinitionNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.NewExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ObjectExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ObjectPattern, {
    type: NodeTypes[number];
}> | Extract<TSESTree.PrivateIdentifier, {
    type: NodeTypes[number];
}> | Extract<TSESTree.PropertyComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.PropertyNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.PropertyDefinitionComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.PropertyDefinitionNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.RestElement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ReturnStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.SequenceExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.SpreadElement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.StaticBlock, {
    type: NodeTypes[number];
}> | Extract<TSESTree.Super, {
    type: NodeTypes[number];
}> | Extract<TSESTree.SwitchCase, {
    type: NodeTypes[number];
}> | Extract<TSESTree.SwitchStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TaggedTemplateExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TemplateElement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TemplateLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ThisExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.ThrowStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TryStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractAccessorPropertyComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractAccessorPropertyNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractMethodDefinitionComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractMethodDefinitionNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractPropertyDefinitionComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAbstractPropertyDefinitionNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAnyKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSArrayType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAsExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSAsyncKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSBigIntKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSBooleanKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSCallSignatureDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSClassImplements, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSConditionalType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSConstructorType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSConstructSignatureDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSDeclareFunction, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSDeclareKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSEmptyBodyFunctionExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSEnumDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSEnumMemberComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSEnumMemberNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSExportAssignment, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSExportKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSExternalModuleReference, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSFunctionType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSImportEqualsDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSImportType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSIndexedAccessType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSIndexSignature, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSInferType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSInstantiationExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSInterfaceBody, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSInterfaceDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSInterfaceHeritage, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSIntersectionType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSIntrinsicKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSLiteralType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSMappedType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSMethodSignatureComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSMethodSignatureNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSModuleBlock, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSModuleDeclarationGlobal, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSModuleDeclarationModuleWithIdentifierId, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSModuleDeclarationModuleWithStringIdDeclared, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSModuleDeclarationModuleWithStringIdNotDeclared, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSModuleDeclarationNamespace, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSNamedTupleMember, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSNamespaceExportDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSNeverKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSNonNullExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSNullKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSNumberKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSObjectKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSOptionalType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSParameterProperty, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSPrivateKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSPropertySignatureComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSPropertySignatureNonComputedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSProtectedKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSPublicKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSQualifiedName, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSReadonlyKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSRestType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSSatisfiesExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSStaticKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSStringKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSSymbolKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTemplateLiteralType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSThisType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTupleType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeAliasDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeAnnotation, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeAssertion, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeLiteral, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeOperator, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeParameter, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeParameterDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeParameterInstantiation, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypePredicate, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeQuery, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSTypeReference, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSUndefinedKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSUnionType, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSUnknownKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.TSVoidKeyword, {
    type: NodeTypes[number];
}> | Extract<TSESTree.UnaryExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.UpdateExpression, {
    type: NodeTypes[number];
}> | Extract<TSESTree.LetOrConstOrVarDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.UsingInForOfDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.UsingInNormalContextDeclaration, {
    type: NodeTypes[number];
}> | Extract<TSESTree.LetOrConstOrVarDeclarator, {
    type: NodeTypes[number];
}> | Extract<TSESTree.UsingInForOfDeclarator, {
    type: NodeTypes[number];
}> | Extract<TSESTree.UsingInNomalConextDeclarator, {
    type: NodeTypes[number];
}> | Extract<TSESTree.WhileStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.WithStatement, {
    type: NodeTypes[number];
}> | Extract<TSESTree.YieldExpression, {
    type: NodeTypes[number];
}>;
export declare const isNodeOfTypeWithConditions: <NodeType extends AST_NODE_TYPES, ExtractedNode extends Extract<TSESTree.Program, {
    type: NodeType;
}> | Extract<TSESTree.AccessorPropertyComputedName, {
    type: NodeType;
}> | Extract<TSESTree.AccessorPropertyNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.ArrayExpression, {
    type: NodeType;
}> | Extract<TSESTree.ArrayPattern, {
    type: NodeType;
}> | Extract<TSESTree.ArrowFunctionExpression, {
    type: NodeType;
}> | Extract<TSESTree.AssignmentExpression, {
    type: NodeType;
}> | Extract<TSESTree.AssignmentPattern, {
    type: NodeType;
}> | Extract<TSESTree.AwaitExpression, {
    type: NodeType;
}> | Extract<TSESTree.BinaryExpression, {
    type: NodeType;
}> | Extract<TSESTree.BlockStatement, {
    type: NodeType;
}> | Extract<TSESTree.BreakStatement, {
    type: NodeType;
}> | Extract<TSESTree.CallExpression, {
    type: NodeType;
}> | Extract<TSESTree.CatchClause, {
    type: NodeType;
}> | Extract<TSESTree.ChainExpression, {
    type: NodeType;
}> | Extract<TSESTree.ClassBody, {
    type: NodeType;
}> | Extract<TSESTree.ClassDeclarationWithName, {
    type: NodeType;
}> | Extract<TSESTree.ClassDeclarationWithOptionalName, {
    type: NodeType;
}> | Extract<TSESTree.ClassExpression, {
    type: NodeType;
}> | Extract<TSESTree.ConditionalExpression, {
    type: NodeType;
}> | Extract<TSESTree.ContinueStatement, {
    type: NodeType;
}> | Extract<TSESTree.DebuggerStatement, {
    type: NodeType;
}> | Extract<TSESTree.Decorator, {
    type: NodeType;
}> | Extract<TSESTree.DoWhileStatement, {
    type: NodeType;
}> | Extract<TSESTree.EmptyStatement, {
    type: NodeType;
}> | Extract<TSESTree.ExportAllDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.ExportDefaultDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.ExportNamedDeclarationWithoutSourceWithMultiple, {
    type: NodeType;
}> | Extract<TSESTree.ExportNamedDeclarationWithoutSourceWithSingle, {
    type: NodeType;
}> | Extract<TSESTree.ExportNamedDeclarationWithSource, {
    type: NodeType;
}> | Extract<TSESTree.ExportSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.ExpressionStatement, {
    type: NodeType;
}> | Extract<TSESTree.ForInStatement, {
    type: NodeType;
}> | Extract<TSESTree.ForOfStatement, {
    type: NodeType;
}> | Extract<TSESTree.ForStatement, {
    type: NodeType;
}> | Extract<TSESTree.FunctionDeclarationWithName, {
    type: NodeType;
}> | Extract<TSESTree.FunctionDeclarationWithOptionalName, {
    type: NodeType;
}> | Extract<TSESTree.FunctionExpression, {
    type: NodeType;
}> | Extract<TSESTree.Identifier, {
    type: NodeType;
}> | Extract<TSESTree.IfStatement, {
    type: NodeType;
}> | Extract<TSESTree.ImportAttribute, {
    type: NodeType;
}> | Extract<TSESTree.ImportDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.ImportDefaultSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.ImportExpression, {
    type: NodeType;
}> | Extract<TSESTree.ImportNamespaceSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.ImportSpecifier, {
    type: NodeType;
}> | Extract<TSESTree.JSXAttribute, {
    type: NodeType;
}> | Extract<TSESTree.JSXClosingElement, {
    type: NodeType;
}> | Extract<TSESTree.JSXClosingFragment, {
    type: NodeType;
}> | Extract<TSESTree.JSXElement, {
    type: NodeType;
}> | Extract<TSESTree.JSXEmptyExpression, {
    type: NodeType;
}> | Extract<TSESTree.JSXExpressionContainer, {
    type: NodeType;
}> | Extract<TSESTree.JSXFragment, {
    type: NodeType;
}> | Extract<TSESTree.JSXIdentifier, {
    type: NodeType;
}> | Extract<TSESTree.JSXMemberExpression, {
    type: NodeType;
}> | Extract<TSESTree.JSXNamespacedName, {
    type: NodeType;
}> | Extract<TSESTree.JSXOpeningElement, {
    type: NodeType;
}> | Extract<TSESTree.JSXOpeningFragment, {
    type: NodeType;
}> | Extract<TSESTree.JSXSpreadAttribute, {
    type: NodeType;
}> | Extract<TSESTree.JSXSpreadChild, {
    type: NodeType;
}> | Extract<TSESTree.JSXText, {
    type: NodeType;
}> | Extract<TSESTree.LabeledStatement, {
    type: NodeType;
}> | Extract<TSESTree.BigIntLiteral, {
    type: NodeType;
}> | Extract<TSESTree.BooleanLiteral, {
    type: NodeType;
}> | Extract<TSESTree.NullLiteral, {
    type: NodeType;
}> | Extract<TSESTree.NumberLiteral, {
    type: NodeType;
}> | Extract<TSESTree.RegExpLiteral, {
    type: NodeType;
}> | Extract<TSESTree.StringLiteral, {
    type: NodeType;
}> | Extract<TSESTree.LogicalExpression, {
    type: NodeType;
}> | Extract<TSESTree.MemberExpressionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.MemberExpressionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.MetaProperty, {
    type: NodeType;
}> | Extract<TSESTree.MethodDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.MethodDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.NewExpression, {
    type: NodeType;
}> | Extract<TSESTree.ObjectExpression, {
    type: NodeType;
}> | Extract<TSESTree.ObjectPattern, {
    type: NodeType;
}> | Extract<TSESTree.PrivateIdentifier, {
    type: NodeType;
}> | Extract<TSESTree.PropertyComputedName, {
    type: NodeType;
}> | Extract<TSESTree.PropertyNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.PropertyDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.PropertyDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.RestElement, {
    type: NodeType;
}> | Extract<TSESTree.ReturnStatement, {
    type: NodeType;
}> | Extract<TSESTree.SequenceExpression, {
    type: NodeType;
}> | Extract<TSESTree.SpreadElement, {
    type: NodeType;
}> | Extract<TSESTree.StaticBlock, {
    type: NodeType;
}> | Extract<TSESTree.Super, {
    type: NodeType;
}> | Extract<TSESTree.SwitchCase, {
    type: NodeType;
}> | Extract<TSESTree.SwitchStatement, {
    type: NodeType;
}> | Extract<TSESTree.TaggedTemplateExpression, {
    type: NodeType;
}> | Extract<TSESTree.TemplateElement, {
    type: NodeType;
}> | Extract<TSESTree.TemplateLiteral, {
    type: NodeType;
}> | Extract<TSESTree.ThisExpression, {
    type: NodeType;
}> | Extract<TSESTree.ThrowStatement, {
    type: NodeType;
}> | Extract<TSESTree.TryStatement, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractAccessorPropertyComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractAccessorPropertyNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractMethodDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractMethodDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractPropertyDefinitionComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAbstractPropertyDefinitionNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSAnyKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSArrayType, {
    type: NodeType;
}> | Extract<TSESTree.TSAsExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSAsyncKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSBigIntKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSBooleanKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSCallSignatureDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSClassImplements, {
    type: NodeType;
}> | Extract<TSESTree.TSConditionalType, {
    type: NodeType;
}> | Extract<TSESTree.TSConstructorType, {
    type: NodeType;
}> | Extract<TSESTree.TSConstructSignatureDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSDeclareFunction, {
    type: NodeType;
}> | Extract<TSESTree.TSDeclareKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSEmptyBodyFunctionExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSEnumDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSEnumMemberComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSEnumMemberNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSExportAssignment, {
    type: NodeType;
}> | Extract<TSESTree.TSExportKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSExternalModuleReference, {
    type: NodeType;
}> | Extract<TSESTree.TSFunctionType, {
    type: NodeType;
}> | Extract<TSESTree.TSImportEqualsDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSImportType, {
    type: NodeType;
}> | Extract<TSESTree.TSIndexedAccessType, {
    type: NodeType;
}> | Extract<TSESTree.TSIndexSignature, {
    type: NodeType;
}> | Extract<TSESTree.TSInferType, {
    type: NodeType;
}> | Extract<TSESTree.TSInstantiationExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSInterfaceBody, {
    type: NodeType;
}> | Extract<TSESTree.TSInterfaceDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSInterfaceHeritage, {
    type: NodeType;
}> | Extract<TSESTree.TSIntersectionType, {
    type: NodeType;
}> | Extract<TSESTree.TSIntrinsicKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSLiteralType, {
    type: NodeType;
}> | Extract<TSESTree.TSMappedType, {
    type: NodeType;
}> | Extract<TSESTree.TSMethodSignatureComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSMethodSignatureNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleBlock, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationGlobal, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationModuleWithIdentifierId, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationModuleWithStringIdDeclared, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationModuleWithStringIdNotDeclared, {
    type: NodeType;
}> | Extract<TSESTree.TSModuleDeclarationNamespace, {
    type: NodeType;
}> | Extract<TSESTree.TSNamedTupleMember, {
    type: NodeType;
}> | Extract<TSESTree.TSNamespaceExportDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSNeverKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSNonNullExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSNullKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSNumberKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSObjectKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSOptionalType, {
    type: NodeType;
}> | Extract<TSESTree.TSParameterProperty, {
    type: NodeType;
}> | Extract<TSESTree.TSPrivateKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSPropertySignatureComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSPropertySignatureNonComputedName, {
    type: NodeType;
}> | Extract<TSESTree.TSProtectedKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSPublicKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSQualifiedName, {
    type: NodeType;
}> | Extract<TSESTree.TSReadonlyKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSRestType, {
    type: NodeType;
}> | Extract<TSESTree.TSSatisfiesExpression, {
    type: NodeType;
}> | Extract<TSESTree.TSStaticKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSStringKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSSymbolKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSTemplateLiteralType, {
    type: NodeType;
}> | Extract<TSESTree.TSThisType, {
    type: NodeType;
}> | Extract<TSESTree.TSTupleType, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeAliasDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeAnnotation, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeAssertion, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeLiteral, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeOperator, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeParameter, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeParameterDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeParameterInstantiation, {
    type: NodeType;
}> | Extract<TSESTree.TSTypePredicate, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeQuery, {
    type: NodeType;
}> | Extract<TSESTree.TSTypeReference, {
    type: NodeType;
}> | Extract<TSESTree.TSUndefinedKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSUnionType, {
    type: NodeType;
}> | Extract<TSESTree.TSUnknownKeyword, {
    type: NodeType;
}> | Extract<TSESTree.TSVoidKeyword, {
    type: NodeType;
}> | Extract<TSESTree.UnaryExpression, {
    type: NodeType;
}> | Extract<TSESTree.UpdateExpression, {
    type: NodeType;
}> | Extract<TSESTree.LetOrConstOrVarDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.UsingInForOfDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.UsingInNormalContextDeclaration, {
    type: NodeType;
}> | Extract<TSESTree.LetOrConstOrVarDeclarator, {
    type: NodeType;
}> | Extract<TSESTree.UsingInForOfDeclarator, {
    type: NodeType;
}> | Extract<TSESTree.UsingInNomalConextDeclarator, {
    type: NodeType;
}> | Extract<TSESTree.WhileStatement, {
    type: NodeType;
}> | Extract<TSESTree.WithStatement, {
    type: NodeType;
}> | Extract<TSESTree.YieldExpression, {
    type: NodeType;
}>, Conditions extends Partial<ExtractedNode>>(nodeType: NodeType, conditions: Conditions) => (node: TSESTree.Node | null | undefined) => node is Conditions & ExtractedNode;
export declare const isTokenOfTypeWithConditions: <TokenType extends AST_TOKEN_TYPES, ExtractedToken extends Extract<TSESTree.BooleanToken, {
    type: TokenType;
}> | Extract<TSESTree.BlockComment, {
    type: TokenType;
}> | Extract<TSESTree.LineComment, {
    type: TokenType;
}> | Extract<TSESTree.IdentifierToken, {
    type: TokenType;
}> | Extract<TSESTree.JSXIdentifierToken, {
    type: TokenType;
}> | Extract<TSESTree.JSXTextToken, {
    type: TokenType;
}> | Extract<TSESTree.KeywordToken, {
    type: TokenType;
}> | Extract<TSESTree.NullToken, {
    type: TokenType;
}> | Extract<TSESTree.NumericToken, {
    type: TokenType;
}> | Extract<TSESTree.PunctuatorToken, {
    type: TokenType;
}> | Extract<TSESTree.RegularExpressionToken, {
    type: TokenType;
}> | Extract<TSESTree.StringToken, {
    type: TokenType;
}> | Extract<TSESTree.TemplateToken, {
    type: TokenType;
}>, Conditions extends Partial<TSESTree.Token & {
    type: TokenType;
}>>(tokenType: TokenType, conditions: Conditions) => (token: TSESTree.Token | null | undefined) => token is Conditions & ExtractedToken;
export declare const isNotTokenOfTypeWithConditions: <TokenType extends AST_TOKEN_TYPES, ExtractedToken extends Extract<TSESTree.BooleanToken, {
    type: TokenType;
}> | Extract<TSESTree.BlockComment, {
    type: TokenType;
}> | Extract<TSESTree.LineComment, {
    type: TokenType;
}> | Extract<TSESTree.IdentifierToken, {
    type: TokenType;
}> | Extract<TSESTree.JSXIdentifierToken, {
    type: TokenType;
}> | Extract<TSESTree.JSXTextToken, {
    type: TokenType;
}> | Extract<TSESTree.KeywordToken, {
    type: TokenType;
}> | Extract<TSESTree.NullToken, {
    type: TokenType;
}> | Extract<TSESTree.NumericToken, {
    type: TokenType;
}> | Extract<TSESTree.PunctuatorToken, {
    type: TokenType;
}> | Extract<TSESTree.RegularExpressionToken, {
    type: TokenType;
}> | Extract<TSESTree.StringToken, {
    type: TokenType;
}> | Extract<TSESTree.TemplateToken, {
    type: TokenType;
}>, Conditions extends Partial<ExtractedToken>>(tokenType: TokenType, conditions: Conditions) => (token: TSESTree.Token | null | undefined) => token is Exclude<TSESTree.BooleanToken, Conditions & ExtractedToken> | Exclude<TSESTree.BlockComment, Conditions & ExtractedToken> | Exclude<TSESTree.LineComment, Conditions & ExtractedToken> | Exclude<TSESTree.IdentifierToken, Conditions & ExtractedToken> | Exclude<TSESTree.JSXIdentifierToken, Conditions & ExtractedToken> | Exclude<TSESTree.JSXTextToken, Conditions & ExtractedToken> | Exclude<TSESTree.KeywordToken, Conditions & ExtractedToken> | Exclude<TSESTree.NullToken, Conditions & ExtractedToken> | Exclude<TSESTree.NumericToken, Conditions & ExtractedToken> | Exclude<TSESTree.PunctuatorToken, Conditions & ExtractedToken> | Exclude<TSESTree.RegularExpressionToken, Conditions & ExtractedToken> | Exclude<TSESTree.StringToken, Conditions & ExtractedToken> | Exclude<TSESTree.TemplateToken, Conditions & ExtractedToken>;
//# sourceMappingURL=helpers.d.ts.map