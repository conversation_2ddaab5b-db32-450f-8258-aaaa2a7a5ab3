import React, { useState, useEffect, useRef, useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import { ChatMessage } from '../../types';
import { getAllMessages, sendMessage } from '../../services/chatService';
import { connectToChat, disconnectFromChat, subscribeToTopic, sendToTopic } from '../../services/websocketService';
import { shouldUseMockServices, mockUsers } from '../../utils/mockServiceHelper';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import UserList from './UserList';
import './ChatRoom.css';

const ChatRoom: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useContext(AuthContext);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);

  // Initialize online users
  useEffect(() => {
    if (shouldUseMockServices()) {
      // Use mock users from our helper
      setOnlineUsers(mockUsers.map(user => user.username));
    } else {
      // In a real app, you would fetch online users from the server
      setOnlineUsers(['user1', 'user2', 'user3']);
    }
  }, []);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);
        const fetchedMessages = await getAllMessages();
        setMessages(fetchedMessages);
        setError(null);
      } catch (err) {
        console.error('Error fetching messages:', err);
        setError('Failed to load messages. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Connect to WebSocket
    let broadcastSubscription: any = null;
    let directSubscription: any = null;

    const setupWebSocket = async () => {
      try {
        await connectToChat();
        console.log('WebSocket connected successfully');

        // Subscribe to broadcast messages
        broadcastSubscription = subscribeToTopic('/topic/messages', (message: ChatMessage) => {
          console.log('Received broadcast message:', message);
          setMessages(prevMessages => {
            // Avoid duplicate messages
            if (!prevMessages.some(m =>
              m.id === message.id ||
              (m.sender === message.sender && m.timestamp === message.timestamp && m.content === message.content)
            )) {
              return [...prevMessages, message];
            }
            return prevMessages;
          });
        });

        // Subscribe to direct messages if user is logged in
        if (user?.username) {
          directSubscription = subscribeToTopic(`/user/${user.username}/queue/messages`, (message: ChatMessage) => {
            console.log('Received direct message:', message);
            setMessages(prevMessages => {
              // Avoid duplicate messages
              if (!prevMessages.some(m =>
                m.id === message.id ||
                (m.sender === message.sender && m.timestamp === message.timestamp && m.content === message.content)
              )) {
                return [...prevMessages, message];
              }
              return prevMessages;
            });
          });
        }
      } catch (err) {
        console.error('WebSocket connection error:', err);
        // Don't set error for WebSocket issues to avoid blocking the UI
        // Users can still use the app without real-time updates
      }
    };

    setupWebSocket();

    // Cleanup on unmount
    return () => {
      try {
        console.log('Cleaning up WebSocket connections');
        if (broadcastSubscription) {
          broadcastSubscription.unsubscribe();
        }
        if (directSubscription) {
          directSubscription.unsubscribe();
        }
        disconnectFromChat();
      } catch (err) {
        console.error('Error during cleanup:', err);
      }
    };
  }, [user?.username]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || !user) return;

    const newMessage: ChatMessage = {
      sender: user.username,
      content,
      timestamp: new Date().toISOString()
    };

    try {
      // Add the message to local state immediately for better UX
      setMessages(prevMessages => [...prevMessages, newMessage]);

      // Send via WebSocket directly
      sendToTopic('/app/chat', newMessage);

      // Also save to database via REST API as a backup
      try {
        await sendMessage(newMessage);
      } catch (apiErr) {
        console.error('Error saving message to database:', apiErr);
        // Continue anyway since the message was sent via WebSocket
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
    }
  };

  return (
    <div className="chat-container">
      <div className="chat-sidebar">
        <UserList users={onlineUsers} />
      </div>
      <div className="chat-main">
        {error && <div className="error-banner">{error}</div>}
        <div className="messages-container">
          {loading ? (
            <div className="loading-messages">Loading messages...</div>
          ) : (
            <MessageList messages={messages} currentUser={user?.username || ''} />
          )}
          <div ref={messagesEndRef} />
        </div>
        <MessageInput onSendMessage={handleSendMessage} disabled={loading} />
      </div>
    </div>
  );
};

export default ChatRoom;
