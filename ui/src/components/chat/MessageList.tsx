import React from 'react';
import { ChatMessage } from '../../types';
import './ChatRoom.css';

interface MessageListProps {
  messages: ChatMessage[];
  currentUser: string;
}

const MessageList: React.FC<MessageListProps> = ({ messages, currentUser }) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (messages.length === 0) {
    return <div className="no-messages">No messages yet. Start the conversation!</div>;
  }

  return (
    <div className="message-list">
      {messages.map((message, index) => {
        const isCurrentUser = message.sender === currentUser;
        return (
          <div 
            key={message.id || index} 
            className={`message ${isCurrentUser ? 'message-own' : 'message-other'}`}
          >
            {!isCurrentUser && <div className="message-sender">{message.sender}</div>}
            <div className="message-bubble">
              <div className="message-content">{message.content}</div>
              <div className="message-time">{formatTime(message.timestamp)}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default MessageList;
