import React from 'react';
import './ChatRoom.css';

interface UserListProps {
  users: string[];
}

const UserList: React.FC<UserListProps> = ({ users }) => {
  return (
    <div className="user-list">
      <h3 className="user-list-title">Online Users</h3>
      {users.length === 0 ? (
        <div className="no-users">No users online</div>
      ) : (
        <ul className="users">
          {users.map((user, index) => (
            <li key={index} className="user-item">
              <div className="user-avatar">
                {user.charAt(0).toUpperCase()}
              </div>
              <div className="user-name">{user}</div>
              <div className="user-status online"></div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default UserList;
