.chat-container {
  display: flex;
  height: calc(100vh - 80px);
  background-color: var(--background-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.chat-sidebar {
  width: 250px;
  background-color: var(--card-bg);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--background-color);
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-own {
  align-self: flex-end;
}

.message-other {
  align-self: flex-start;
}

.message-sender {
  font-size: 12px;
  margin-bottom: 4px;
  color: var(--secondary-color);
  font-weight: 500;
}

.message-bubble {
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
}

.message-own .message-bubble {
  background-color: var(--primary-color);
  color: white;
  border-bottom-right-radius: 4px;
}

.message-other .message-bubble {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-bottom-left-radius: 4px;
}

.message-content {
  margin-bottom: 5px;
  word-break: break-word;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  text-align: right;
}

.message-input-container {
  display: flex;
  padding: 15px;
  background-color: var(--card-bg);
  border-top: 1px solid var(--border-color);
}

.message-input {
  flex: 1;
  padding: 12px;
  border-radius: 20px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  resize: none;
  min-height: 24px;
  max-height: 120px;
  font-family: inherit;
  font-size: 14px;
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.send-button {
  margin-left: 10px;
  padding: 0 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #3a5a8c;
}

.send-button:disabled {
  background-color: var(--secondary-color);
  cursor: not-allowed;
}

.no-messages {
  text-align: center;
  padding: 40px 0;
  color: var(--secondary-color);
}

.loading-messages {
  text-align: center;
  padding: 40px 0;
  color: var(--secondary-color);
}

.error-banner {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  text-align: center;
  font-size: 14px;
}

/* User List Styles */
.user-list {
  padding: 15px;
}

.user-list-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: var(--primary-color);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.users {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
}

.user-name {
  flex: 1;
  font-size: 14px;
}

.user-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.user-status.online {
  background-color: #28a745;
}

.no-users {
  text-align: center;
  padding: 20px 0;
  color: var(--secondary-color);
  font-size: 14px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
    height: calc(100vh - 60px);
  }

  .chat-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .message {
    max-width: 85%;
  }
}
