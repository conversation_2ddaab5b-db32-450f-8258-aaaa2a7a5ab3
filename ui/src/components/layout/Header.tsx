import React, { useContext, useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { enableMockServices, disableMockServices } from '../../utils/mockServiceHelper';
import './Header.css';

interface HeaderProps {
  darkMode: boolean;
  toggleDarkMode: () => void;
}

const Header: React.FC<HeaderProps> = ({ darkMode, toggleDarkMode }) => {
  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  const [mockServicesEnabled, setMockServicesEnabled] = useState<boolean>(false);

  // Check if mock services are enabled on mount
  useEffect(() => {
    const storedValue = localStorage.getItem('useMockServices');
    setMockServicesEnabled(storedValue === 'true');
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleMockServices = () => {
    const newValue = !mockServicesEnabled;
    setMockServicesEnabled(newValue);
    if (newValue) {
      enableMockServices();
    } else {
      disableMockServices();
    }
    // Reload the page to apply changes
    window.location.reload();
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <Link to="/">MyChatApp</Link>
        </div>
        <div className="header-right">
          <div className="header-buttons">
            {process.env.NODE_ENV === 'development' && (
              <button
                className="mock-toggle"
                onClick={toggleMockServices}
                title={mockServicesEnabled ? 'Disable mock services' : 'Enable mock services'}
                style={{
                  marginRight: '10px',
                  padding: '5px 10px',
                  fontSize: '0.8rem',
                  background: mockServicesEnabled ? '#4caf50' : '#f44336',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                {mockServicesEnabled ? 'Mock: ON' : 'Mock: OFF'}
              </button>
            )}
            <button
              className="theme-toggle"
              onClick={toggleDarkMode}
              aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {darkMode ? '☀️' : '🌙'}
            </button>
          </div>
          {isAuthenticated ? (
            <div className="user-menu">
              <span className="username">Hello, {user?.name}</span>
              <button className="logout-button" onClick={handleLogout}>
                Logout
              </button>
            </div>
          ) : (
            <div className="auth-links">
              <Link to="/login" className="auth-link">Login</Link>
              <Link to="/register" className="auth-link register">Register</Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
