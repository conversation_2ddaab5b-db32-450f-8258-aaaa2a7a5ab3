import React from 'react';
import { Link } from 'react-router-dom';
import './HomePage.css';

const HomePage: React.FC = () => {
  return (
    <div className="home-container">
      <div className="home-content">
        <h1>Welcome to MyChatApp</h1>
        <p>A simple and secure way to chat with your friends and colleagues.</p>
        
        <div className="home-buttons">
          <Link to="/login" className="home-button login-button">
            Login
          </Link>
          <Link to="/register" className="home-button register-button">
            Register
          </Link>
        </div>
        
        <div className="home-features">
          <div className="feature">
            <h3>Real-time Chat</h3>
            <p>Instant messaging with real-time updates</p>
          </div>
          <div className="feature">
            <h3>Secure</h3>
            <p>Your conversations are protected</p>
          </div>
          <div className="feature">
            <h3>User Friendly</h3>
            <p>Simple and intuitive interface</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
