.home-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 20px;
}

.home-content {
  max-width: 800px;
  text-align: center;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 40px;
  box-shadow: var(--shadow);
}

.home-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.home-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: var(--text-color);
}

.home-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
}

.home-button {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 4px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.login-button {
  background-color: var(--primary-color);
  color: white;
}

.login-button:hover {
  background-color: var(--primary-dark);
}

.register-button {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.register-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.home-features {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 40px;
}

.feature {
  flex: 1;
  min-width: 200px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.3s ease;
}

.feature:hover {
  transform: translateY(-5px);
}

.feature h3 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.feature p {
  font-size: 1rem;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .home-features {
    flex-direction: column;
  }
  
  .home-content h1 {
    font-size: 2rem;
  }
  
  .home-content {
    padding: 30px 20px;
  }
  
  .home-buttons {
    flex-direction: column;
    gap: 10px;
  }
}
