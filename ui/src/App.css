:root {
  --primary-color: #4a6fa5;
  --primary-dark: #3a5a8c;
  --secondary-color: #6c757d;
  --accent-color: #47b881;
  --background-color: #f8f9fa;
  --text-color: #212529;
  --border-color: #dee2e6;
  --input-bg: #ffffff;
  --card-bg: #ffffff;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --hover-bg: #f1f3f5;
}

.dark-theme {
  --primary-color: #5b8ad6;
  --primary-dark: #4a79c5;
  --secondary-color: #adb5bd;
  --accent-color: #57d997;
  --background-color: #121212;
  --text-color: #e9ecef;
  --border-color: #495057;
  --input-bg: #2d2d2d;
  --card-bg: #1e1e1e;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  --hover-bg: #2c2c2c;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

button {
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
  transition: background-color 0.2s;
}

button:hover {
  background-color: #3a5a8c;
}

button.secondary {
  background-color: var(--secondary-color);
}

button.secondary:hover {
  background-color: #5a6268;
}

input,
textarea {
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  width: 100%;
  box-sizing: border-box;
}

input:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.card {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  margin-top: 5px;
}