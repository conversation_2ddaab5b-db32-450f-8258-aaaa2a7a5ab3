/// <reference types="vite/client" />

interface ImportMeta {
    readonly env: {
        readonly REACT_APP_USE_MOCK_SERVICES: string;
        readonly REACT_APP_WEBSOCKET_URL: string;
        // Add other environment variables here as needed
    };
}

const env = import.meta.env; // Use Vite's `import.meta.env` for environment variables

export const shouldUseMockServices = env.REACT_APP_USE_MOCK_SERVICES === 'true';

// Dynamically set the WebSocket URL based on the environment
export const websocketUrl = env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8080/ws';
