// Flag to determine if we should use mock services
let useMockServices = false;

// Check if we should use mock services from localStorage
try {
  useMockServices = localStorage.getItem('useMockServices') === 'true';
} catch (e) {
  console.error('Error accessing localStorage:', e);
}

// Function to enable mock services
export const enableMockServices = () => {
  useMockServices = true;
  try {
    localStorage.setItem('useMockServices', 'true');
  } catch (e) {
    console.error('Error setting localStorage:', e);
  }
};

// Function to disable mock services
export const disableMockServices = () => {
  useMockServices = false;
  try {
    localStorage.setItem('useMockServices', 'false');
  } catch (e) {
    console.error('Error setting localStorage:', e);
  }
};

// Function to check if we should use mock services
export const shouldUseMockServices = (): boolean => {
  return useMockServices || process.env.NODE_ENV === 'development';
};

// Mock data for users
export const mockUsers = [
  {
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    username: 'testuser'
  },
  {
    id: 2,
    name: 'Jane Doe',
    email: '<EMAIL>',
    username: 'janedoe'
  },
  {
    id: 3,
    name: 'John Smith',
    email: '<EMAIL>',
    username: 'johnsmith'
  }
];

// Mock data for messages
export const mockMessages = [
  {
    id: 1,
    sender: 'janedoe',
    content: 'Hello everyone!',
    timestamp: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
  },
  {
    id: 2,
    sender: 'johnsmith',
    content: 'Hi Jane, how are you doing?',
    timestamp: new Date(Date.now() - 3500000).toISOString() 
  },
  {
    id: 3,
    sender: 'testuser',
    content: 'Hello Jane and John! Nice to meet you both.',
    timestamp: new Date(Date.now() - 3400000).toISOString()
  },
  {
    id: 4,
    sender: 'janedoe',
    content: 'I\'m doing great, thanks for asking!',
    timestamp: new Date(Date.now() - 3300000).toISOString()
  },
  {
    id: 5,
    sender: 'johnsmith',
    content: 'What are you all working on today?',
    timestamp: new Date(Date.now() - 3200000).toISOString()
  },
  {
    id: 6,
    sender: 'testuser',
    content: 'I\'m building this chat application. It\'s coming along nicely!',
    timestamp: new Date(Date.now() - 3100000).toISOString()
  }
];

// Function to get a mock user by username
export const getMockUserByUsername = (username: string) => {
  return mockUsers.find(user => user.username === username) || mockUsers[0];
};

// Function to get all mock messages
export const getAllMockMessages = () => {
  return [...mockMessages];
};

// Function to add a new mock message
export const addMockMessage = (message: any) => {
  const newMessage = {
    ...message,
    id: mockMessages.length + 1
  };
  mockMessages.push(newMessage);
  return newMessage;
};
