import SockJS from 'sockjs-client';
import { Client, StompHeaders, StompSubscription } from '@stomp/stompjs';
import { shouldUseMockServices } from '../utils/mockServiceHelper';

let stompClient: Client | null = null;
let mockSubscriptions: { [topic: string]: any[] } = {};
let mockConnected = false;

export const connectToChat = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Use mock WebSocket if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock WebSocket service');
      // Simulate network delay
      setTimeout(() => {
        mockConnected = true;
        console.log('Mock WebSocket connected');
        resolve();
      }, 500);
      return;
    }

    try {
      // Use a simple path instead of constructing a full URL
      const socket = new SockJS('/ws');

      stompClient = new Client({
        webSocketFactory: () => socket,
        debug: (str) => {
          console.log('STOMP Debug:', str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        onConnect: () => {
          console.log('Connected to WebSocket');
          resolve();
        },
        onStompError: (error) => {
          console.error('WebSocket connection error:', error);
          reject(error);
        }
      });

      stompClient.activate();
    } catch (error) {
      console.error('Error setting up WebSocket:', error);
      // Fall back to mock if real connection fails
      if (shouldUseMockServices()) {
        setTimeout(() => {
          mockConnected = true;
          console.log('Fallback to mock WebSocket after connection error');
          resolve();
        }, 500);
      } else {
        reject(error);
      }
    }
  });
};

export const disconnectFromChat = (): void => {
  // Handle mock WebSocket
  if (shouldUseMockServices() && mockConnected) {
    mockConnected = false;
    mockSubscriptions = {};
    console.log('Mock WebSocket disconnected');
    return;
  }

  // Handle real WebSocket
  if (stompClient) {
    try {
      stompClient.deactivate();
      console.log('Disconnected from WebSocket');
    } catch (error) {
      console.error('Error disconnecting from WebSocket:', error);
    }
  }
};

export const subscribeToTopic = <T>(
  topic: string,
  callback: (message: T) => void
): StompSubscription | any => {
  // Handle mock WebSocket
  if (shouldUseMockServices()) {
    if (!mockConnected) {
      console.error('Mock WebSocket not connected');
      return null;
    }

    // Initialize topic if it doesn't exist
    if (!mockSubscriptions[topic]) {
      mockSubscriptions[topic] = [];
    }

    // Add callback to subscriptions
    const id = Date.now().toString();
    mockSubscriptions[topic].push({ id, callback });

    // Return a mock subscription object
    return {
      id,
      unsubscribe: () => {
        if (mockSubscriptions[topic]) {
          mockSubscriptions[topic] = mockSubscriptions[topic].filter(sub => sub.id !== id);
        }
      }
    };
  }

  // Handle real WebSocket
  if (!stompClient || !stompClient.connected) {
    console.error('STOMP client not connected');
    return null;
  }

  return stompClient.subscribe(topic, (message) => {
    try {
      const parsedMessage = JSON.parse(message.body) as T;
      callback(parsedMessage);
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  });
};

export const sendToTopic = <T>(destination: string, message: T): void => {
  // Handle mock WebSocket
  if (shouldUseMockServices()) {
    if (!mockConnected) {
      console.error('Mock WebSocket not connected');
      return;
    }

    console.log(`Mock sending message to ${destination}:`, message);

    // Simulate broadcasting to all subscribers after a small delay
    setTimeout(() => {
      if (mockSubscriptions[destination]) {
        const messageStr = JSON.stringify(message);
        mockSubscriptions[destination].forEach(sub => {
          try {
            sub.callback(JSON.parse(messageStr));
          } catch (error) {
            console.error('Error in mock subscription callback:', error);
          }
        });
      }
    }, 100);

    return;
  }

  // Handle real WebSocket
  if (!stompClient || !stompClient.connected) {
    console.error('STOMP client not connected');
    return;
  }

  stompClient.publish({
    destination: destination,
    headers: {} as StompHeaders,
    body: JSON.stringify(message)
  });
};
