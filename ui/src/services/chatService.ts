import axios from 'axios';
import { ChatMessage } from '../types';
import { shouldUseMockServices, getAllMockMessages, addMockMessage } from '../utils/mockServiceHelper';

const API_URL = '/messages';

export const getAllMessages = async (): Promise<ChatMessage[]> => {
  try {
    // Use mock services if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock messages service');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 600));
      return getAllMockMessages();
    }

    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching messages:', error);
    // If there's an error, try to use mock data as fallback
    if (shouldUseMockServices()) {
      return getAllMockMessages();
    }
    // Return empty array instead of throwing to prevent app from crashing
    // This is a more graceful degradation
    return [];
  }
};

export const sendMessage = async (message: ChatMessage): Promise<ChatMessage> => {
  try {
    // Use mock services if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock send message service');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));
      return addMockMessage(message);
    }

    const response = await axios.post(API_URL, message);
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error);
    // If there's an error, try to use mock data as fallback
    if (shouldUseMockServices()) {
      return addMockMessage(message);
    }
    throw error;
  }
};
