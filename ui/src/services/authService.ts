import axios from 'axios';
import { AuthResponse } from '../types';
import { shouldUseMockServices, getMockUserByUsername } from '../utils/mockServiceHelper';

const API_URL = '/api/auth';

export const login = async (username: string, password: string): Promise<AuthResponse> => {
  try {
    // Use mock services if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock login service');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Get mock user or use default if username doesn't exist
      const user = getMockUserByUsername(username);

      return {
        token: 'mock-jwt-token-' + Math.random().toString(36).substring(2),
        user: {
          ...user,
          username: username // Ensure the username matches what was entered
        }
      };
    }

    const response = await axios.post(`${API_URL}/login`, { username, password });
    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

export const register = async (
  name: string,
  email: string,
  username: string,
  password: string
): Promise<AuthResponse> => {
  try {
    // Use mock services if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock register service');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 800));

      return {
        token: 'mock-jwt-token-' + Math.random().toString(36).substring(2),
        user: {
          id: Math.floor(Math.random() * 1000) + 10,
          name,
          email,
          username
        }
      };
    }

    const response = await axios.post(`${API_URL}/register`, {
      name,
      email,
      username,
      password
    });
    return response.data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

export const requestPasswordReset = async (email: string): Promise<void> => {
  try {
    // Use mock services if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock password reset request service');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 600));
      return;
    }

    await axios.post(`${API_URL}/forgot-password`, { email });
  } catch (error) {
    console.error('Password reset request error:', error);
    throw error;
  }
};

export const resetPassword = async (token: string, newPassword: string): Promise<void> => {
  try {
    // Use mock services if enabled or in development mode
    if (shouldUseMockServices()) {
      console.log('Using mock password reset service');
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 700));
      return;
    }

    await axios.post(`${API_URL}/reset-password`, { token, newPassword });
  } catch (error) {
    console.error('Password reset error:', error);
    throw error;
  }
};
